# Galaxy Guns 3D - Debug Overlay
# Shows debug information during development

extends Control

@onready var debug_label = Label.new()
var player: PlayerController
var game_manager: Node3D

func _ready():
	# Setup debug label
	add_child(debug_label)
	debug_label.position = Vector2(10, 10)
	debug_label.add_theme_color_override("font_color", Color.YELLOW)
	
	# Find references
	player = get_tree().get_first_node_in_group("player")
	game_manager = get_node("../")
	
	print("🐛 Debug overlay initialized")

func _process(_delta):
	if not player:
		player = get_tree().get_first_node_in_group("player")
		return
	
	var debug_text = ""
	debug_text += "=== GALAXY GUNS 3D DEBUG ===\n"
	debug_text += "FPS: %d\n" % Engine.get_frames_per_second()
	debug_text += "\n"
	
	# Player info
	debug_text += "PLAYER:\n"
	debug_text += "Position: %s\n" % player.global_position
	debug_text += "Velocity: %s\n" % player.velocity
	debug_text += "Health: %d/%d\n" % [player.current_health, player.max_health]
	debug_text += "On Floor: %s\n" % player.is_on_floor()
	debug_text += "\n"
	
	# Weapon info
	if player.current_weapon:
		debug_text += "WEAPON:\n"
		debug_text += "Name: %s\n" % player.current_weapon.weapon_name
		debug_text += "Ammo: %d/%d\n" % [player.current_weapon.current_ammo, player.current_weapon.reserve_ammo]
		debug_text += "Firing: %s\n" % player.current_weapon.is_firing
		debug_text += "Reloading: %s\n" % player.current_weapon.is_reloading
		debug_text += "\n"
	
	# Enemy info
	var enemies = get_tree().get_nodes_in_group("enemies")
	debug_text += "ENEMIES: %d\n" % enemies.size()
	for i in range(min(3, enemies.size())):
		var enemy = enemies[i]
		if is_instance_valid(enemy):
			debug_text += "  Enemy %d: %s (HP: %d)\n" % [i+1, enemy.current_state, enemy.current_health]
	
	debug_text += "\n"
	debug_text += "CONTROLS:\n"
	debug_text += "WASD - Move\n"
	debug_text += "Mouse - Look\n"
	debug_text += "LMB - Fire\n"
	debug_text += "R - Reload\n"
	debug_text += "Space - Jump\n"
	
	debug_label.text = debug_text
