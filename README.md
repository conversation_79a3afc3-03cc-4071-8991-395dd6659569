# 🌌 Galaxy Guns 3D
**Next-Generation Multiplayer Pixel FPS for iOS**

[![<PERSON>ot](https://img.shields.io/badge/Godot-4.3-blue.svg)](https://godotengine.org/)
[![iOS](https://img.shields.io/badge/iOS-15.0+-black.svg)](https://developer.apple.com/ios/)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)]()

> A pixel-art multiplayer FPS designed to outclass Pixel Gun 3D through superior visual polish, stable netcode, and ethical player retention systems.

---

## 🎯 Project Overview

**Galaxy Guns 3D** combines nostalgic pixel aesthetics with modern dynamic lighting and smooth 60fps gameplay. Built with Godot 4.3 for iOS, targeting iPhone 12 mini and above.

### Key Features
- 🎨 **Dynamic Pixel Lighting**: Per-pixel normal mapping on sprite art
- 🌐 **Stable Multiplayer**: Client-side prediction with server reconciliation
- ⚡ **60fps Performance**: Optimized for iPhone 12 mini and above
- 💎 **Ethical Monetization**: Cosmetics and battle pass, no loot boxes
- 🏆 **Competitive Systems**: Skill-based matchmaking and ranked ladder

---

## 📁 Repository Structure

```
galaxy-guns-3d/
├── client/                 # Godot 4.3 iOS client
│   ├── scenes/            # Game scenes and UI
│   ├── scripts/           # GDScript gameplay code
│   ├── assets/            # Sprites, audio, shaders
│   ├── export_presets.cfg # iOS export configuration
│   └── project.godot      # Main project file
├── server/                # Dedicated game server
│   ├── src/               # Server source code
│   ├── docker/            # Container configuration
│   └── deploy/            # Deployment scripts
├── shared/                # Shared code and data
│   ├── protocols/         # Network protocol definitions
│   ├── data/              # Game data (weapons, maps)
│   └── utils/             # Common utilities
├── marketing/             # App Store assets
│   ├── screenshots/       # Device-specific screenshots
│   ├── videos/            # App preview videos
│   └── metadata/          # Store descriptions
├── ci/                    # CI/CD configuration
│   ├── fastlane/          # iOS deployment automation
│   ├── github/            # GitHub Actions workflows
│   └── scripts/           # Build and test scripts
└── docs/                  # Project documentation
    ├── game-design-document.md
    ├── assets-manifest.md
    └── app-store-compliance.md
```

---

## 🛠 Development Setup

### Prerequisites
- **Godot 4.3-stable** - Game engine
- **Xcode 17.x** - iOS development tools
- **Fastlane** - iOS CI/CD automation
- **Git** - Version control
- **Docker** - Server containerization

### Quick Start
```bash
# Clone repository
git clone https://github.com/orvyn/galaxy-guns-3d.git
cd galaxy-guns-3d

# Install dependencies
./scripts/setup_dev_environment.sh

# Open client in Godot
godot client/project.godot

# Start development server
cd server && docker-compose up -d
```

---

## 🎮 Development Workflow

### Client Development
```bash
# Open Godot project
cd client
godot project.godot

# Export iOS build
godot --headless --export-release "iOS" ../builds/ios/GalaxyGuns.ipa
```

### Server Development
```bash
# Start local server
cd server
docker-compose up --build

# Run tests
cargo test

# Deploy to staging
./deploy/staging.sh
```

### Asset Pipeline
```bash
# Process sprites with Aseprite
./scripts/process_sprites.sh

# Optimize textures for iOS
./scripts/optimize_textures.sh

# Generate audio assets
./scripts/process_audio.sh
```

---

## 📱 Build Targets

### iOS Production
- **Target:** iPhone 12 mini and above
- **iOS Version:** 15.0+
- **Performance:** 60fps stable
- **Size:** <2GB initial download

### Development Builds
- **iOS Simulator** - Quick testing
- **Desktop** - Rapid iteration
- **TestFlight** - Beta distribution

---

## 🧪 Testing Strategy

### Automated Testing
- **Unit Tests**: Core gameplay logic
- **Integration Tests**: Multiplayer systems
- **Performance Tests**: 60fps validation
- **UI Tests**: User interface flows

### Manual Testing
- **Device Testing**: iPhone 12 mini through iPhone 15 Pro Max
- **Network Testing**: Various connection conditions
- **Accessibility Testing**: VoiceOver and motor accessibility
- **Compliance Testing**: App Store guidelines

---

## 🚀 Deployment Pipeline

### Continuous Integration
```yaml
# .github/workflows/ios.yml
name: iOS Build and Test
on: [push, pull_request]
jobs:
  build:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Godot
        run: ./ci/scripts/setup_godot.sh
      - name: Run Tests
        run: ./ci/scripts/run_tests.sh
      - name: Build iOS
        run: ./ci/scripts/build_ios.sh
```

### Release Process
1. **Version Bump**: Semantic versioning
2. **Build**: Automated iOS IPA generation
3. **Test**: Comprehensive test suite
4. **Deploy**: TestFlight beta distribution
5. **Release**: App Store submission

---

## 📊 Performance Targets

| Metric | Target | Minimum |
|--------|--------|---------|
| Frame Rate | 60fps | 55fps |
| Memory Usage | <512MB | <768MB |
| Battery Life | 2+ hours | 1.5 hours |
| Network Latency | <80ms | <120ms |
| Crash Rate | <0.1% | <0.2% |
| Load Time | <2s | <3s |

---

## 🤝 Contributing

### Code Style
- **GDScript**: Follow Godot style guide
- **Comments**: Heavy commenting for complex logic
- **Commits**: Semantic commit messages
- **Testing**: Unit tests for new features

### Development Process
1. Create feature branch from `develop`
2. Implement feature with tests
3. Submit pull request with description
4. Code review and approval
5. Merge to `develop` branch

---

## 📄 License

**Proprietary License** - All rights reserved to Orvyn Studios.

This project contains proprietary code and assets. Unauthorized copying, distribution, or modification is strictly prohibited.

---

## 📞 Support

- **Documentation**: `/docs` directory
- **Issues**: GitHub Issues tracker
- **Discord**: [Development Server](https://discord.gg/galaxyguns)
- **Email**: <EMAIL>

---

**Built with ❤️ using Godot 4.3 and modern iOS development practices.**
