# Galaxy Guns 3D - Adaptive Audio Manager
# Dynamic soundtrack system with 3 BPM tiers and auto-duck vs VOIP
# Optimized for 60fps performance with minimal audio processing overhead

class_name AdaptiveAudioManager
extends Node

## Signals for audio events
signal intensity_changed(new_intensity: IntensityLevel)
signal music_track_changed(track_name: String)
signal voip_state_changed(is_active: bool)

## Intensity levels for adaptive music
enum IntensityLevel {
	LOW = 0,    # 120 BPM - Exploration, menu, calm moments
	MEDIUM = 1, # 140 BPM - Normal gameplay, moderate action
	HIGH = 2    # 160 BPM - Combat, high action, boss fights
}

## Audio bus names (configured in Godot's Audio Bus Layout)
const MASTER_BUS = "Master"
const MUSIC_BUS = "Music"
const SFX_BUS = "SFX"
const VOICE_BUS = "Voice"
const AMBIENT_BUS = "Ambient"

## Configuration
@export var crossfade_duration: float = 2.0
@export var voip_duck_amount: float = -12.0  # dB reduction when VOIP is active
@export var voip_duck_speed: float = 0.5     # Seconds to duck/unduck
@export var max_simultaneous_sfx: int = 16   # Performance limit
@export var spatial_audio_max_distance: float = 1000.0

## Current state
var current_intensity: IntensityLevel = IntensityLevel.MEDIUM
var is_voip_active: bool = false
var is_music_playing: bool = false
var current_music_track: String = ""

## Audio players for different intensity levels
var music_players: Array[AudioStreamPlayer] = []
var ambient_player: AudioStreamPlayer
var voip_duck_tween: Tween

## Music tracks for each intensity level
var music_tracks: Dictionary = {
	IntensityLevel.LOW: [],
	IntensityLevel.MEDIUM: [],
	IntensityLevel.HIGH: []
}

## SFX pool for performance optimization
var sfx_pool: Array[AudioStreamPlayer] = []
var sfx_pool_index: int = 0

## Spatial audio players pool
var spatial_sfx_pool: Array[AudioStreamPlayer3D] = []
var spatial_pool_index: int = 0

func _ready():
	# Initialize audio system
	_setup_audio_buses()
	_create_music_players()
	_create_sfx_pools()
	_load_music_tracks()
	
	# Connect to game events
	_connect_game_signals()
	
	print("🎵 Adaptive Audio Manager initialized")

func _setup_audio_buses():
	"""Configure audio bus settings for optimal performance"""
	# Get audio bus indices
	var music_bus_idx = AudioServer.get_bus_index(MUSIC_BUS)
	var sfx_bus_idx = AudioServer.get_bus_index(SFX_BUS)
	var voice_bus_idx = AudioServer.get_bus_index(VOICE_BUS)
	
	# Set initial volumes (can be adjusted by player settings)
	AudioServer.set_bus_volume_db(music_bus_idx, -5.0)
	AudioServer.set_bus_volume_db(sfx_bus_idx, 0.0)
	AudioServer.set_bus_volume_db(voice_bus_idx, 0.0)
	
	# Add compressor to master bus for consistent audio levels
	var compressor = AudioEffectCompressor.new()
	compressor.threshold = -12.0
	compressor.ratio = 4.0
	compressor.gain = 2.0
	AudioServer.add_bus_effect(AudioServer.get_bus_index(MASTER_BUS), compressor)

func _create_music_players():
	"""Create audio players for each intensity level"""
	for i in range(IntensityLevel.size()):
		var player = AudioStreamPlayer.new()
		player.name = "MusicPlayer_" + str(i)
		player.bus = MUSIC_BUS
		player.volume_db = -80.0  # Start silent
		add_child(player)
		music_players.append(player)
	
	# Create ambient player
	ambient_player = AudioStreamPlayer.new()
	ambient_player.name = "AmbientPlayer"
	ambient_player.bus = AMBIENT_BUS
	ambient_player.autoplay = false
	add_child(ambient_player)

func _create_sfx_pools():
	"""Create pools of audio players for efficient SFX playback"""
	# 2D SFX pool
	for i in range(max_simultaneous_sfx):
		var player = AudioStreamPlayer.new()
		player.name = "SFXPlayer_" + str(i)
		player.bus = SFX_BUS
		add_child(player)
		sfx_pool.append(player)
	
	# 3D spatial SFX pool
	for i in range(max_simultaneous_sfx):
		var player = AudioStreamPlayer3D.new()
		player.name = "SpatialSFXPlayer_" + str(i)
		player.bus = SFX_BUS
		player.max_distance = spatial_audio_max_distance
		player.attenuation_model = AudioStreamPlayer3D.ATTENUATION_INVERSE_DISTANCE
		add_child(player)
		spatial_sfx_pool.append(player)

func _load_music_tracks():
	"""Load music tracks for each intensity level"""
	# Load low intensity tracks (120 BPM)
	music_tracks[IntensityLevel.LOW] = [
		preload("res://assets/audio/music/adaptive/low/exploration_theme.ogg"),
		preload("res://assets/audio/music/adaptive/low/menu_ambient.ogg"),
		preload("res://assets/audio/music/adaptive/low/calm_moments.ogg")
	]
	
	# Load medium intensity tracks (140 BPM)
	music_tracks[IntensityLevel.MEDIUM] = [
		preload("res://assets/audio/music/adaptive/medium/gameplay_theme.ogg"),
		preload("res://assets/audio/music/adaptive/medium/action_moderate.ogg"),
		preload("res://assets/audio/music/adaptive/medium/tension_buildup.ogg")
	]
	
	# Load high intensity tracks (160 BPM)
	music_tracks[IntensityLevel.HIGH] = [
		preload("res://assets/audio/music/adaptive/high/combat_intense.ogg"),
		preload("res://assets/audio/music/adaptive/high/boss_battle.ogg"),
		preload("res://assets/audio/music/adaptive/high/final_showdown.ogg")
	]
	
	print("🎼 Loaded music tracks for all intensity levels")

func _connect_game_signals():
	"""Connect to game events that trigger audio changes"""
	# Connect to game state manager (would be implemented in game)
	# GameStateManager.combat_started.connect(_on_combat_started)
	# GameStateManager.combat_ended.connect(_on_combat_ended)
	# VoipManager.voip_started.connect(_on_voip_started)
	# VoipManager.voip_ended.connect(_on_voip_ended)
	pass

## Public API for controlling adaptive audio

func set_intensity(new_intensity: IntensityLevel, immediate: bool = false):
	"""Change music intensity level with smooth crossfade"""
	if new_intensity == current_intensity:
		return
	
	var old_intensity = current_intensity
	current_intensity = new_intensity
	
	print("🎵 Changing intensity from %s to %s" % [
		IntensityLevel.keys()[old_intensity],
		IntensityLevel.keys()[new_intensity]
	])
	
	if immediate:
		_switch_music_immediate(new_intensity)
	else:
		_crossfade_music(old_intensity, new_intensity)
	
	intensity_changed.emit(new_intensity)

func play_sfx(sound: AudioStream, volume_db: float = 0.0, pitch_scale: float = 1.0) -> AudioStreamPlayer:
	"""Play 2D sound effect using pooled audio players"""
	var player = _get_next_sfx_player()
	if player == null:
		push_warning("SFX pool exhausted, skipping sound")
		return null
	
	player.stream = sound
	player.volume_db = volume_db
	player.pitch_scale = pitch_scale
	player.play()
	
	return player

func play_sfx_3d(sound: AudioStream, position: Vector3, volume_db: float = 0.0, pitch_scale: float = 1.0) -> AudioStreamPlayer3D:
	"""Play 3D spatial sound effect"""
	var player = _get_next_spatial_sfx_player()
	if player == null:
		push_warning("Spatial SFX pool exhausted, skipping sound")
		return null
	
	player.stream = sound
	player.global_position = position
	player.volume_db = volume_db
	player.pitch_scale = pitch_scale
	player.play()
	
	return player

func play_ambient(sound: AudioStream, volume_db: float = -10.0, fade_in: bool = true):
	"""Play ambient background sound"""
	ambient_player.stream = sound
	
	if fade_in:
		ambient_player.volume_db = -80.0
		ambient_player.play()
		var tween = create_tween()
		tween.tween_property(ambient_player, "volume_db", volume_db, 1.0)
	else:
		ambient_player.volume_db = volume_db
		ambient_player.play()

func stop_ambient(fade_out: bool = true):
	"""Stop ambient sound"""
	if not ambient_player.playing:
		return
	
	if fade_out:
		var tween = create_tween()
		tween.tween_property(ambient_player, "volume_db", -80.0, 1.0)
		tween.tween_callback(ambient_player.stop)
	else:
		ambient_player.stop()

func set_voip_active(active: bool):
	"""Enable/disable VOIP ducking"""
	if active == is_voip_active:
		return
	
	is_voip_active = active
	_apply_voip_ducking(active)
	voip_state_changed.emit(active)

func set_master_volume(volume_db: float):
	"""Set master volume"""
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index(MASTER_BUS), volume_db)

func set_music_volume(volume_db: float):
	"""Set music volume"""
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index(MUSIC_BUS), volume_db)

func set_sfx_volume(volume_db: float):
	"""Set SFX volume"""
	AudioServer.set_bus_volume_db(AudioServer.get_bus_index(SFX_BUS), volume_db)

## Private helper methods

func _switch_music_immediate(intensity: IntensityLevel):
	"""Immediately switch to new intensity music"""
	# Stop all music players
	for player in music_players:
		player.stop()
		player.volume_db = -80.0
	
	# Start new intensity music
	var tracks = music_tracks[intensity]
	if tracks.size() > 0:
		var track = tracks[randi() % tracks.size()]
		var player = music_players[intensity]
		player.stream = track
		player.volume_db = 0.0
		player.play()
		is_music_playing = true
		current_music_track = track.resource_path.get_file()
		music_track_changed.emit(current_music_track)

func _crossfade_music(old_intensity: IntensityLevel, new_intensity: IntensityLevel):
	"""Smoothly crossfade between intensity levels"""
	var old_player = music_players[old_intensity]
	var new_player = music_players[new_intensity]
	
	# Select random track from new intensity
	var tracks = music_tracks[new_intensity]
	if tracks.size() == 0:
		push_warning("No tracks available for intensity level: " + str(new_intensity))
		return
	
	var track = tracks[randi() % tracks.size()]
	new_player.stream = track
	new_player.volume_db = -80.0
	new_player.play()
	
	# Crossfade using tween
	var tween = create_tween()
	tween.set_parallel(true)
	
	# Fade out old track
	if old_player.playing:
		tween.tween_property(old_player, "volume_db", -80.0, crossfade_duration)
		tween.tween_callback(old_player.stop).set_delay(crossfade_duration)
	
	# Fade in new track
	tween.tween_property(new_player, "volume_db", 0.0, crossfade_duration)
	
	# Update state
	current_music_track = track.resource_path.get_file()
	music_track_changed.emit(current_music_track)

func _apply_voip_ducking(enable: bool):
	"""Apply or remove VOIP ducking effect"""
	if voip_duck_tween:
		voip_duck_tween.kill()
	
	voip_duck_tween = create_tween()
	
	var music_bus_idx = AudioServer.get_bus_index(MUSIC_BUS)
	var sfx_bus_idx = AudioServer.get_bus_index(SFX_BUS)
	
	if enable:
		# Duck music and SFX when VOIP is active
		voip_duck_tween.set_parallel(true)
		voip_duck_tween.tween_method(_set_bus_volume, music_bus_idx, 
			AudioServer.get_bus_volume_db(music_bus_idx), 
			AudioServer.get_bus_volume_db(music_bus_idx) + voip_duck_amount, 
			voip_duck_speed)
		voip_duck_tween.tween_method(_set_bus_volume, sfx_bus_idx,
			AudioServer.get_bus_volume_db(sfx_bus_idx),
			AudioServer.get_bus_volume_db(sfx_bus_idx) + voip_duck_amount * 0.5,
			voip_duck_speed)
	else:
		# Restore original volumes
		voip_duck_tween.set_parallel(true)
		voip_duck_tween.tween_method(_set_bus_volume, music_bus_idx,
			AudioServer.get_bus_volume_db(music_bus_idx),
			-5.0,  # Original music volume
			voip_duck_speed)
		voip_duck_tween.tween_method(_set_bus_volume, sfx_bus_idx,
			AudioServer.get_bus_volume_db(sfx_bus_idx),
			0.0,   # Original SFX volume
			voip_duck_speed)

func _set_bus_volume(bus_idx: int, volume_db: float):
	"""Helper method for tween volume changes"""
	AudioServer.set_bus_volume_db(bus_idx, volume_db)

func _get_next_sfx_player() -> AudioStreamPlayer:
	"""Get next available SFX player from pool"""
	for i in range(max_simultaneous_sfx):
		var player = sfx_pool[sfx_pool_index]
		sfx_pool_index = (sfx_pool_index + 1) % max_simultaneous_sfx
		
		if not player.playing:
			return player
	
	return null  # Pool exhausted

func _get_next_spatial_sfx_player() -> AudioStreamPlayer3D:
	"""Get next available spatial SFX player from pool"""
	for i in range(max_simultaneous_sfx):
		var player = spatial_sfx_pool[spatial_pool_index]
		spatial_pool_index = (spatial_pool_index + 1) % max_simultaneous_sfx
		
		if not player.playing:
			return player
	
	return null  # Pool exhausted

## Event handlers (would be connected to game systems)

func _on_combat_started():
	"""Handle combat start event"""
	set_intensity(IntensityLevel.HIGH)

func _on_combat_ended():
	"""Handle combat end event"""
	set_intensity(IntensityLevel.MEDIUM)

func _on_voip_started():
	"""Handle VOIP activation"""
	set_voip_active(true)

func _on_voip_ended():
	"""Handle VOIP deactivation"""
	set_voip_active(false)

## Debug and utility methods

func get_current_intensity_name() -> String:
	"""Get human-readable name of current intensity"""
	return IntensityLevel.keys()[current_intensity]

func get_audio_stats() -> Dictionary:
	"""Get current audio system statistics"""
	var active_sfx = 0
	var active_spatial = 0
	
	for player in sfx_pool:
		if player.playing:
			active_sfx += 1
	
	for player in spatial_sfx_pool:
		if player.playing:
			active_spatial += 1
	
	return {
		"current_intensity": get_current_intensity_name(),
		"current_track": current_music_track,
		"voip_active": is_voip_active,
		"active_sfx": active_sfx,
		"active_spatial_sfx": active_spatial,
		"sfx_pool_usage": float(active_sfx) / max_simultaneous_sfx,
		"spatial_pool_usage": float(active_spatial) / max_simultaneous_sfx
	}
