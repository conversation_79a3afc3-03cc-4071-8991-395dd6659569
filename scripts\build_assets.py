#!/usr/bin/env python3
"""
Galaxy Guns 3D - Master Asset Build Pipeline
Coordinates sprite processing, audio processing, and asset optimization
"""

import os
import sys
import json
import time
import argparse
import subprocess
from pathlib import Path
from typing import Dict, List
import concurrent.futures
from datetime import datetime

# Import our processing modules
sys.path.append(str(Path(__file__).parent))
from process_sprites import SpriteProcessor
from process_audio import AudioProcessor

class AssetBuildPipeline:
    """Master coordinator for all asset processing"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.build_config_file = self.project_root / "assets" / "build_config.json"
        self.build_log_file = self.project_root / "logs" / "asset_build.log"
        
        # Initialize processors
        self.sprite_processor = SpriteProcessor(project_root)
        self.audio_processor = AudioProcessor(project_root)
        
        # Ensure log directory exists
        self.build_log_file.parent.mkdir(exist_ok=True)
        
        # Build statistics
        self.build_stats = {
            "start_time": None,
            "end_time": None,
            "sprites_processed": 0,
            "audio_processed": 0,
            "errors": [],
            "warnings": []
        }
    
    def load_build_config(self) -> Dict:
        """Load master build configuration"""
        default_config = {
            "parallel_processing": True,
            "max_workers": 4,
            "sprite_processing": {
                "enabled": True,
                "watch_extensions": [".ase", ".aseprite"],
                "generate_atlas": True,
                "optimize_for_mobile": True
            },
            "audio_processing": {
                "enabled": True,
                "watch_extensions": [".wav", ".mp3", ".flac", ".aiff"],
                "normalize_audio": True,
                "generate_adaptive_layers": True
            },
            "optimization": {
                "compress_textures": True,
                "generate_mipmaps": False,
                "texture_format": "astc",
                "audio_compression": "ogg_vorbis"
            },
            "godot_integration": {
                "auto_import": True,
                "generate_import_files": True,
                "validate_resources": True
            }
        }
        
        if self.build_config_file.exists():
            with open(self.build_config_file, 'r') as f:
                config = json.load(f)
                # Merge with defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        else:
            # Create default config
            self.build_config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.build_config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            return default_config
    
    def log_message(self, message: str, level: str = "INFO"):
        """Log message to file and console"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        # Print to console
        if level == "ERROR":
            print(f"❌ {message}")
        elif level == "WARNING":
            print(f"⚠️ {message}")
        else:
            print(f"ℹ️ {message}")
        
        # Write to log file
        with open(self.build_log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")
    
    def find_source_files(self, config: Dict) -> Dict[str, List[Path]]:
        """Find all source files that need processing"""
        source_files = {
            "sprites": [],
            "audio": []
        }
        
        # Find sprite files
        if config["sprite_processing"]["enabled"]:
            sprite_extensions = config["sprite_processing"]["watch_extensions"]
            for ext in sprite_extensions:
                source_files["sprites"].extend(
                    self.sprite_processor.source_dir.glob(f"**/*{ext}")
                )
        
        # Find audio files
        if config["audio_processing"]["enabled"]:
            audio_extensions = config["audio_processing"]["watch_extensions"]
            for ext in audio_extensions:
                source_files["audio"].extend(
                    self.audio_processor.source_dir.glob(f"**/*{ext}")
                )
        
        return source_files
    
    def process_sprites_batch(self, sprite_files: List[Path], config: Dict) -> List[Path]:
        """Process a batch of sprite files"""
        processed_files = []
        sprite_config = self.sprite_processor.load_config()
        
        for sprite_file in sprite_files:
            try:
                self.log_message(f"Processing sprite: {sprite_file.name}")
                output_files = self.sprite_processor.process_sprite(sprite_file, sprite_config)
                processed_files.extend(output_files)
                self.build_stats["sprites_processed"] += 1
            except Exception as e:
                error_msg = f"Failed to process sprite {sprite_file.name}: {e}"
                self.log_message(error_msg, "ERROR")
                self.build_stats["errors"].append(error_msg)
        
        return processed_files
    
    def process_audio_batch(self, audio_files: List[Path], config: Dict) -> List[Path]:
        """Process a batch of audio files"""
        processed_files = []
        audio_config = self.audio_processor.load_config()
        
        for audio_file in audio_files:
            try:
                self.log_message(f"Processing audio: {audio_file.name}")
                output_file = self.audio_processor.process_audio_file(audio_file, audio_config)
                if output_file:
                    processed_files.append(output_file)
                    self.build_stats["audio_processed"] += 1
            except Exception as e:
                error_msg = f"Failed to process audio {audio_file.name}: {e}"
                self.log_message(error_msg, "ERROR")
                self.build_stats["errors"].append(error_msg)
        
        return processed_files
    
    def optimize_textures(self, texture_files: List[Path], config: Dict):
        """Optimize textures for mobile deployment"""
        if not config["optimization"]["compress_textures"]:
            return
        
        self.log_message("🗜️ Optimizing textures for mobile...")
        
        optimization_config = config["optimization"]
        
        for texture_file in texture_files:
            if texture_file.suffix.lower() not in ['.png', '.jpg', '.jpeg']:
                continue
            
            try:
                # Use ImageMagick or similar tool for optimization
                # This is a placeholder - would use actual optimization tools
                self.log_message(f"Optimized texture: {texture_file.name}")
            except Exception as e:
                warning_msg = f"Failed to optimize texture {texture_file.name}: {e}"
                self.log_message(warning_msg, "WARNING")
                self.build_stats["warnings"].append(warning_msg)
    
    def validate_godot_resources(self, config: Dict):
        """Validate that all resources can be imported by Godot"""
        if not config["godot_integration"]["validate_resources"]:
            return
        
        self.log_message("🔍 Validating Godot resources...")
        
        # Check if Godot can import the project
        try:
            result = subprocess.run([
                "godot",
                "--headless",
                "--path", str(self.project_root / "client"),
                "--import"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log_message("✅ Godot resource validation passed")
            else:
                error_msg = f"Godot resource validation failed: {result.stderr}"
                self.log_message(error_msg, "ERROR")
                self.build_stats["errors"].append(error_msg)
                
        except subprocess.TimeoutExpired:
            warning_msg = "Godot resource validation timed out"
            self.log_message(warning_msg, "WARNING")
            self.build_stats["warnings"].append(warning_msg)
        except FileNotFoundError:
            warning_msg = "Godot not found in PATH, skipping resource validation"
            self.log_message(warning_msg, "WARNING")
            self.build_stats["warnings"].append(warning_msg)
    
    def generate_build_report(self):
        """Generate comprehensive build report"""
        duration = self.build_stats["end_time"] - self.build_stats["start_time"]
        
        report = {
            "build_info": {
                "timestamp": self.build_stats["start_time"].isoformat(),
                "duration_seconds": duration.total_seconds(),
                "project_root": str(self.project_root)
            },
            "processing_stats": {
                "sprites_processed": self.build_stats["sprites_processed"],
                "audio_processed": self.build_stats["audio_processed"],
                "total_files": self.build_stats["sprites_processed"] + self.build_stats["audio_processed"]
            },
            "issues": {
                "errors": self.build_stats["errors"],
                "warnings": self.build_stats["warnings"],
                "error_count": len(self.build_stats["errors"]),
                "warning_count": len(self.build_stats["warnings"])
            }
        }
        
        # Save report
        report_file = self.project_root / "logs" / "build_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print("\n" + "="*50)
        print("🎉 ASSET BUILD COMPLETE")
        print("="*50)
        print(f"⏱️ Duration: {duration.total_seconds():.1f} seconds")
        print(f"🎨 Sprites processed: {self.build_stats['sprites_processed']}")
        print(f"🎵 Audio files processed: {self.build_stats['audio_processed']}")
        print(f"❌ Errors: {len(self.build_stats['errors'])}")
        print(f"⚠️ Warnings: {len(self.build_stats['warnings'])}")
        
        if self.build_stats["errors"]:
            print("\nErrors:")
            for error in self.build_stats["errors"]:
                print(f"  • {error}")
        
        if self.build_stats["warnings"]:
            print("\nWarnings:")
            for warning in self.build_stats["warnings"]:
                print(f"  • {warning}")
        
        print(f"\n📄 Full report saved to: {report_file}")
    
    def build_all_assets(self, force_rebuild: bool = False):
        """Build all assets with optional parallel processing"""
        self.build_stats["start_time"] = datetime.now()
        
        self.log_message("🚀 Starting Galaxy Guns 3D asset build pipeline")
        
        # Load configuration
        config = self.load_build_config()
        
        # Find source files
        source_files = self.find_source_files(config)
        
        total_files = len(source_files["sprites"]) + len(source_files["audio"])
        if total_files == 0:
            self.log_message("⚠️ No source files found to process", "WARNING")
            return
        
        self.log_message(f"📁 Found {len(source_files['sprites'])} sprite files and {len(source_files['audio'])} audio files")
        
        processed_files = []
        
        # Process assets
        if config["parallel_processing"] and total_files > 1:
            # Parallel processing
            with concurrent.futures.ThreadPoolExecutor(max_workers=config["max_workers"]) as executor:
                futures = []
                
                # Submit sprite processing
                if source_files["sprites"]:
                    future = executor.submit(self.process_sprites_batch, source_files["sprites"], config)
                    futures.append(future)
                
                # Submit audio processing
                if source_files["audio"]:
                    future = executor.submit(self.process_audio_batch, source_files["audio"], config)
                    futures.append(future)
                
                # Collect results
                for future in concurrent.futures.as_completed(futures):
                    try:
                        result = future.result()
                        processed_files.extend(result)
                    except Exception as e:
                        error_msg = f"Parallel processing error: {e}"
                        self.log_message(error_msg, "ERROR")
                        self.build_stats["errors"].append(error_msg)
        else:
            # Sequential processing
            if source_files["sprites"]:
                sprite_results = self.process_sprites_batch(source_files["sprites"], config)
                processed_files.extend(sprite_results)
            
            if source_files["audio"]:
                audio_results = self.process_audio_batch(source_files["audio"], config)
                processed_files.extend(audio_results)
        
        # Post-processing optimizations
        texture_files = [f for f in processed_files if f.suffix.lower() in ['.png', '.jpg', '.jpeg']]
        if texture_files:
            self.optimize_textures(texture_files, config)
        
        # Generate texture atlases if enabled
        if config["sprite_processing"].get("generate_atlas", False) and texture_files:
            self.sprite_processor._generate_texture_atlas(texture_files, self.sprite_processor.load_config())
        
        # Generate adaptive audio layers if enabled
        if config["audio_processing"].get("generate_adaptive_layers", False):
            audio_files = [f for f in processed_files if f.suffix.lower() in ['.ogg', '.wav']]
            music_files = [f for f in audio_files if f.parent.name == "music"]
            if music_files:
                self.audio_processor.create_adaptive_music_layers(music_files, self.audio_processor.load_config())
        
        # Validate Godot resources
        self.validate_godot_resources(config)
        
        # Finalize build
        self.build_stats["end_time"] = datetime.now()
        self.generate_build_report()

def main():
    parser = argparse.ArgumentParser(description="Galaxy Guns 3D Asset Build Pipeline")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--force", action="store_true", help="Force rebuild all assets")
    parser.add_argument("--sprites-only", action="store_true", help="Process sprites only")
    parser.add_argument("--audio-only", action="store_true", help="Process audio only")
    parser.add_argument("--parallel", action="store_true", default=True, help="Enable parallel processing")
    
    args = parser.parse_args()
    
    pipeline = AssetBuildPipeline(args.project_root)
    
    if args.sprites_only:
        # Process sprites only
        config = pipeline.load_build_config()
        config["audio_processing"]["enabled"] = False
        pipeline.build_all_assets(args.force)
    elif args.audio_only:
        # Process audio only
        config = pipeline.load_build_config()
        config["sprite_processing"]["enabled"] = False
        pipeline.build_all_assets(args.force)
    else:
        # Process all assets
        pipeline.build_all_assets(args.force)

if __name__ == "__main__":
    main()
