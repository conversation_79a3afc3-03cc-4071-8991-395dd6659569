# Galaxy Guns 3D - Matchmaking Manager
# Skill-based matchmaking with region selection and anti-cheat integration
# Optimized for mobile with quick match times and balanced games

class_name MatchmakingManager
extends Node

## Signals for matchmaking events
signal matchmaking_started(queue_type: String)
signal match_found(match_data: Dictionary)
signal matchmaking_cancelled()
signal matchmaking_failed(reason: String)
signal queue_status_updated(position: int, estimated_time: float)
signal skill_rating_updated(new_rating: int, change: int)

## Matchmaking configuration
@export_group("Matchmaking Settings")
@export var default_region: String = "us-east"
@export var max_search_time: float = 120.0  # 2 minutes max search
@export var skill_expansion_rate: float = 50.0  # Rating range expansion per second
@export var ping_weight: float = 0.3  # Weight of ping in matchmaking
@export var skill_weight: float = 0.7  # Weight of skill in matchmaking

@export_group("Queue Types")
@export var available_queues: Array[String] = [
	"casual",
	"ranked",
	"team_deathmatch",
	"domination",
	"capture_the_flag"
]

## Player data
var player_profile: PlayerProfile
var current_queue: String = ""
var is_searching: bool = false
var search_start_time: float = 0.0
var queue_position: int = 0

## Matchmaking state
var skill_range_min: int = 0
var skill_range_max: int = 0
var preferred_regions: Array[String] = []
var party_members: Array[int] = []

## Server communication
var matchmaking_server_url: String = "wss://matchmaking.galaxyguns3d.com"
var websocket: WebSocketPeer
var connection_established: bool = false

## Player profile for matchmaking
class PlayerProfile:
	var player_id: String
	var username: String
	var skill_ratings: Dictionary = {}  # queue_type -> rating
	var region: String
	var preferred_game_modes: Array[String] = []
	var total_matches: int = 0
	var win_rate: float = 0.0
	var average_ping: float = 50.0
	var reputation_score: int = 100  # Anti-cheat reputation
	var last_match_time: float = 0.0
	var consecutive_leaves: int = 0
	
	func _init():
		player_id = _generate_player_id()
		username = "Player" + str(randi() % 10000)
		region = "us-east"
		
		# Initialize skill ratings
		for queue in ["casual", "ranked", "team_deathmatch", "domination", "capture_the_flag"]:
			skill_ratings[queue] = 1000  # Starting rating
	
	func get_skill_rating(queue_type: String) -> int:
		return skill_ratings.get(queue_type, 1000)
	
	func update_skill_rating(queue_type: String, new_rating: int):
		var old_rating = skill_ratings.get(queue_type, 1000)
		skill_ratings[queue_type] = new_rating
		return new_rating - old_rating
	
	func _generate_player_id() -> String:
		return "player_" + str(Time.get_time_dict_from_system()["unix"]) + "_" + str(randi())
	
	func to_dict() -> Dictionary:
		return {
			"player_id": player_id,
			"username": username,
			"skill_ratings": skill_ratings,
			"region": region,
			"preferred_game_modes": preferred_game_modes,
			"total_matches": total_matches,
			"win_rate": win_rate,
			"average_ping": average_ping,
			"reputation_score": reputation_score,
			"last_match_time": last_match_time,
			"consecutive_leaves": consecutive_leaves
		}

## Match data structure
class MatchData:
	var match_id: String
	var game_mode: String
	var map_name: String
	var server_address: String
	var server_port: int
	var players: Array[Dictionary] = []
	var team_assignments: Dictionary = {}
	var estimated_match_quality: float = 0.0
	var region: String
	
	func _init(data: Dictionary):
		match_id = data.get("match_id", "")
		game_mode = data.get("game_mode", "team_deathmatch")
		map_name = data.get("map_name", "default_map")
		server_address = data.get("server_address", "")
		server_port = data.get("server_port", 7777)
		players = data.get("players", [])
		team_assignments = data.get("team_assignments", {})
		estimated_match_quality = data.get("match_quality", 0.0)
		region = data.get("region", "us-east")

func _ready():
	# Initialize player profile
	_load_player_profile()
	
	# Setup WebSocket connection
	_setup_websocket()
	
	# Connect to network events
	_connect_network_signals()
	
	print("🎯 Matchmaking Manager initialized")

func _load_player_profile():
	"""Load or create player profile"""
	var save_file = "user://player_profile.dat"
	var file = FileAccess.open(save_file, FileAccess.READ)
	
	if file:
		var profile_data = file.get_var()
		file.close()
		
		player_profile = PlayerProfile.new()
		player_profile.player_id = profile_data.get("player_id", player_profile.player_id)
		player_profile.username = profile_data.get("username", player_profile.username)
		player_profile.skill_ratings = profile_data.get("skill_ratings", player_profile.skill_ratings)
		player_profile.region = profile_data.get("region", player_profile.region)
		player_profile.total_matches = profile_data.get("total_matches", 0)
		player_profile.win_rate = profile_data.get("win_rate", 0.0)
		player_profile.reputation_score = profile_data.get("reputation_score", 100)
	else:
		player_profile = PlayerProfile.new()
		_save_player_profile()

func _save_player_profile():
	"""Save player profile to disk"""
	var save_file = "user://player_profile.dat"
	var file = FileAccess.open(save_file, FileAccess.WRITE)
	
	if file:
		file.store_var(player_profile.to_dict())
		file.close()

func _setup_websocket():
	"""Setup WebSocket connection to matchmaking server"""
	websocket = WebSocketPeer.new()

func _connect_network_signals():
	"""Connect to network manager signals"""
	var network_manager = get_node("/root/NetworkManager")
	if network_manager:
		network_manager.connected_to_server.connect(_on_game_server_connected)
		network_manager.disconnected_from_server.connect(_on_game_server_disconnected)

func _process(delta):
	if websocket:
		websocket.poll()
		
		match websocket.get_ready_state():
			WebSocketPeer.STATE_OPEN:
				if not connection_established:
					connection_established = true
					_authenticate_with_matchmaking_server()
				
				# Process incoming messages
				while websocket.get_available_packet_count() > 0:
					var packet = websocket.get_packet()
					_process_matchmaking_message(packet)
			
			WebSocketPeer.STATE_CLOSED:
				if connection_established:
					connection_established = false
					if is_searching:
						matchmaking_failed.emit("Connection lost")
						_stop_matchmaking()
	
	# Update search progress
	if is_searching:
		_update_search_progress(delta)

## Public API

func start_matchmaking(queue_type: String, party_ids: Array[int] = []):
	"""Start matchmaking for specified queue"""
	if is_searching:
		print("⚠️ Already searching for match")
		return false
	
	if not queue_type in available_queues:
		matchmaking_failed.emit("Invalid queue type: " + queue_type)
		return false
	
	# Connect to matchmaking server if not connected
	if not connection_established:
		var error = websocket.connect_to_url(matchmaking_server_url)
		if error != OK:
			matchmaking_failed.emit("Failed to connect to matchmaking server")
			return false
	
	current_queue = queue_type
	party_members = party_ids
	is_searching = true
	search_start_time = Time.get_time_dict_from_system()["unix"]
	
	# Calculate initial skill range
	var base_rating = player_profile.get_skill_rating(queue_type)
	skill_range_min = base_rating - 100
	skill_range_max = base_rating + 100
	
	# Send matchmaking request
	var request = {
		"action": "start_matchmaking",
		"queue_type": queue_type,
		"player_profile": player_profile.to_dict(),
		"party_members": party_members,
		"preferred_regions": _get_preferred_regions()
	}
	
	_send_matchmaking_message(request)
	matchmaking_started.emit(queue_type)
	
	print("🔍 Started matchmaking for %s" % queue_type)
	return true

func cancel_matchmaking():
	"""Cancel current matchmaking search"""
	if not is_searching:
		return
	
	var request = {
		"action": "cancel_matchmaking",
		"player_id": player_profile.player_id
	}
	
	_send_matchmaking_message(request)
	_stop_matchmaking()
	matchmaking_cancelled.emit()
	
	print("❌ Cancelled matchmaking")

func accept_match(match_id: String):
	"""Accept found match"""
	var request = {
		"action": "accept_match",
		"match_id": match_id,
		"player_id": player_profile.player_id
	}
	
	_send_matchmaking_message(request)
	print("✅ Accepted match: %s" % match_id)

func decline_match(match_id: String):
	"""Decline found match"""
	var request = {
		"action": "decline_match",
		"match_id": match_id,
		"player_id": player_profile.player_id
	}
	
	_send_matchmaking_message(request)
	_stop_matchmaking()
	print("❌ Declined match: %s" % match_id)

func report_match_result(match_id: String, won: bool, stats: Dictionary):
	"""Report match result for skill rating update"""
	var request = {
		"action": "match_result",
		"match_id": match_id,
		"player_id": player_profile.player_id,
		"won": won,
		"stats": stats
	}
	
	_send_matchmaking_message(request)
	
	# Update local stats
	player_profile.total_matches += 1
	if won:
		player_profile.win_rate = (player_profile.win_rate * (player_profile.total_matches - 1) + 1.0) / player_profile.total_matches
	else:
		player_profile.win_rate = (player_profile.win_rate * (player_profile.total_matches - 1)) / player_profile.total_matches
	
	player_profile.last_match_time = Time.get_time_dict_from_system()["unix"]
	_save_player_profile()

func set_preferred_region(region: String):
	"""Set preferred region for matchmaking"""
	player_profile.region = region
	_save_player_profile()

func get_queue_stats(queue_type: String) -> Dictionary:
	"""Get statistics for specific queue"""
	return {
		"skill_rating": player_profile.get_skill_rating(queue_type),
		"total_matches": player_profile.total_matches,
		"win_rate": player_profile.win_rate,
		"average_ping": player_profile.average_ping,
		"reputation_score": player_profile.reputation_score
	}

## Private Methods

func _stop_matchmaking():
	"""Stop matchmaking search"""
	is_searching = false
	current_queue = ""
	search_start_time = 0.0
	queue_position = 0

func _update_search_progress(delta: float):
	"""Update matchmaking search progress"""
	var search_time = Time.get_time_dict_from_system()["unix"] - search_start_time
	
	# Expand skill range over time
	var expansion = skill_expansion_rate * search_time
	var base_rating = player_profile.get_skill_rating(current_queue)
	skill_range_min = base_rating - 100 - expansion
	skill_range_max = base_rating + 100 + expansion
	
	# Cancel search if taking too long
	if search_time > max_search_time:
		matchmaking_failed.emit("Search timeout")
		_stop_matchmaking()

func _get_preferred_regions() -> Array[String]:
	"""Get preferred regions based on ping"""
	# This would ping different regions and return sorted by latency
	return [player_profile.region, "us-west", "eu-west", "asia-east"]

func _authenticate_with_matchmaking_server():
	"""Authenticate with matchmaking server"""
	var auth_request = {
		"action": "authenticate",
		"player_profile": player_profile.to_dict(),
		"client_version": "1.0.0",
		"platform": OS.get_name()
	}
	
	_send_matchmaking_message(auth_request)

func _send_matchmaking_message(message: Dictionary):
	"""Send message to matchmaking server"""
	if websocket and websocket.get_ready_state() == WebSocketPeer.STATE_OPEN:
		var json_string = JSON.stringify(message)
		websocket.send_text(json_string)

func _process_matchmaking_message(packet: PackedByteArray):
	"""Process incoming matchmaking message"""
	var json_string = packet.get_string_from_utf8()
	var json = JSON.new()
	var parse_result = json.parse(json_string)
	
	if parse_result != OK:
		print("⚠️ Failed to parse matchmaking message")
		return
	
	var message = json.data
	var action = message.get("action", "")
	
	match action:
		"authentication_success":
			print("✅ Authenticated with matchmaking server")
		
		"authentication_failed":
			var reason = message.get("reason", "Unknown error")
			matchmaking_failed.emit("Authentication failed: " + reason)
		
		"queue_update":
			queue_position = message.get("position", 0)
			var estimated_time = message.get("estimated_time", 0.0)
			queue_status_updated.emit(queue_position, estimated_time)
		
		"match_found":
			var match_data = MatchData.new(message.get("match_data", {}))
			match_found.emit(match_data.to_dict())
			print("🎮 Match found: %s" % match_data.match_id)
		
		"match_ready":
			var server_info = message.get("server_info", {})
			_connect_to_game_server(server_info)
		
		"skill_rating_update":
			var queue_type = message.get("queue_type", "")
			var new_rating = message.get("new_rating", 1000)
			var old_rating = player_profile.get_skill_rating(queue_type)
			var change = player_profile.update_skill_rating(queue_type, new_rating)
			
			skill_rating_updated.emit(new_rating, change)
			_save_player_profile()
			
			print("📊 Skill rating updated: %s %d (%+d)" % [queue_type, new_rating, change])
		
		"matchmaking_failed":
			var reason = message.get("reason", "Unknown error")
			matchmaking_failed.emit(reason)
			_stop_matchmaking()
		
		"player_penalty":
			var penalty_type = message.get("penalty_type", "")
			var duration = message.get("duration", 0)
			_handle_player_penalty(penalty_type, duration)

func _connect_to_game_server(server_info: Dictionary):
	"""Connect to assigned game server"""
	var network_manager = get_node("/root/NetworkManager")
	if network_manager:
		var address = server_info.get("address", "")
		var port = server_info.get("port", 7777)
		
		if network_manager.connect_to_server(address, port):
			_stop_matchmaking()
		else:
			matchmaking_failed.emit("Failed to connect to game server")

func _handle_player_penalty(penalty_type: String, duration: int):
	"""Handle player penalty (leaving, cheating, etc.)"""
	match penalty_type:
		"leaving":
			player_profile.consecutive_leaves += 1
			print("⚠️ Penalty for leaving: %d minutes" % duration)
		
		"cheating":
			player_profile.reputation_score = max(0, player_profile.reputation_score - 50)
			print("⚠️ Cheating penalty: %d minutes" % duration)
		
		"toxicity":
			player_profile.reputation_score = max(0, player_profile.reputation_score - 25)
			print("⚠️ Toxicity penalty: %d minutes" % duration)
	
	_save_player_profile()

## Event Handlers

func _on_game_server_connected():
	"""Handle successful game server connection"""
	print("🎮 Connected to game server")

func _on_game_server_disconnected(reason: String):
	"""Handle game server disconnection"""
	print("🔌 Disconnected from game server: %s" % reason)
	
	# If disconnected during match, report as leave
	if player_profile.last_match_time > 0:
		var current_time = Time.get_time_dict_from_system()["unix"]
		if current_time - player_profile.last_match_time < 300:  # Within 5 minutes
			player_profile.consecutive_leaves += 1
			_save_player_profile()

## Utility Methods

func get_matchmaking_stats() -> Dictionary:
	"""Get current matchmaking statistics"""
	return {
		"is_searching": is_searching,
		"current_queue": current_queue,
		"search_time": Time.get_time_dict_from_system()["unix"] - search_start_time if is_searching else 0.0,
		"queue_position": queue_position,
		"skill_range": [skill_range_min, skill_range_max],
		"connection_established": connection_established,
		"player_profile": player_profile.to_dict()
	}

func is_matchmaking_available() -> bool:
	"""Check if matchmaking is available"""
	return connection_established and not is_searching

func get_estimated_wait_time(queue_type: String) -> float:
	"""Get estimated wait time for queue"""
	# This would be provided by the matchmaking server
	# For now, return a simple estimate based on queue type
	match queue_type:
		"casual":
			return 30.0
		"ranked":
			return 60.0
		_:
			return 45.0
