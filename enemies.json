{"enemies": {"drone_swarm": {"id": "E-01", "name": "Drone Swarm", "class": "trash", "base_hp": 20, "speed": 3.0, "attack_pattern": "swarm_circle", "drop_rate": 5, "color": "#666666", "wave_unlock": 1}, "scout_bot": {"id": "E-02", "name": "<PERSON> Bot", "class": "trash", "base_hp": 15, "speed": 4.0, "attack_pattern": "hit_run", "drop_rate": 3, "color": "#888888", "wave_unlock": 1}, "repair_drone": {"id": "E-03", "name": "Repair <PERSON>", "class": "support", "base_hp": 25, "speed": 2.0, "attack_pattern": "heal_others", "drop_rate": 8, "color": "#00AA00", "wave_unlock": 2}, "shock_trooper": {"id": "E-04", "name": "Shock Trooper", "class": "shooter", "base_hp": 60, "speed": 2.4, "attack_pattern": "burst_rifle", "drop_rate": 12, "color": "#FF4444", "wave_unlock": 3, "affixes": ["reflective_bullets", "rapid_fire"]}, "plasma_gunner": {"id": "E-05", "name": "Plasma Gunner", "class": "shooter", "base_hp": 55, "speed": 2.2, "attack_pattern": "plasma_bolt", "drop_rate": 10, "color": "#44FF44", "wave_unlock": 3, "affixes": ["explosive_rounds", "piercing"]}, "sniper_unit": {"id": "E-06", "name": "Sniper Unit", "class": "shooter", "base_hp": 40, "speed": 1.8, "attack_pattern": "long_range", "drop_rate": 15, "color": "#4444FF", "wave_unlock": 4, "affixes": ["critical_shots", "stealth"]}, "phase_specter": {"id": "E-07", "name": "Phase Specter", "class": "assassin", "base_hp": 40, "speed": 4.2, "attack_pattern": "teleport_stab", "drop_rate": 15, "color": "#AA44AA", "wave_unlock": 5, "affixes": ["invisibility", "poison_blade"]}, "shadow_stalker": {"id": "E-08", "name": "<PERSON>", "class": "assassin", "base_hp": 35, "speed": 4.5, "attack_pattern": "backstab", "drop_rate": 18, "color": "#222222", "wave_unlock": 6, "affixes": ["stealth", "critical_strikes"]}, "void_hunter": {"id": "E-09", "name": "Void Hunter", "class": "assassin", "base_hp": 50, "speed": 3.8, "attack_pattern": "void_dash", "drop_rate": 20, "color": "#440044", "wave_unlock": 7, "affixes": ["phase_through", "energy_drain"]}, "obliterator_bot": {"id": "E-10", "name": "Obliterator <PERSON><PERSON>", "class": "heavy", "base_hp": 200, "speed": 1.2, "attack_pattern": "laser_sweep", "drop_rate": 20, "color": "#FF8800", "wave_unlock": 8, "affixes": ["explosive_death", "shielded"]}, "siege_mech": {"id": "E-11", "name": "Siege Mech", "class": "heavy", "base_hp": 250, "speed": 1.0, "attack_pattern": "missile_barrage", "drop_rate": 25, "color": "#888800", "wave_unlock": 9, "affixes": ["armor_plated", "area_damage"]}, "titan_crusher": {"id": "E-12", "name": "Titan Crusher", "class": "heavy", "base_hp": 300, "speed": 0.8, "attack_pattern": "ground_pound", "drop_rate": 30, "color": "#AA0000", "wave_unlock": 10, "affixes": ["knockback", "damage_reduction"]}, "elite_commander": {"id": "E-13", "name": "Elite Commander", "class": "elite", "base_hp": 150, "speed": 2.5, "attack_pattern": "command_aura", "drop_rate": 35, "color": "#FFD700", "wave_unlock": 10, "affixes": ["buff_allies", "tactical_retreat"]}, "cyber_wraith": {"id": "E-14", "name": "<PERSON><PERSON> Wraith", "class": "elite", "base_hp": 120, "speed": 3.5, "attack_pattern": "digital_storm", "drop_rate": 40, "color": "#00FFFF", "wave_unlock": 11, "affixes": ["emp_burst", "system_hack"]}, "quantum_soldier": {"id": "E-15", "name": "Quantum Soldier", "class": "elite", "base_hp": 180, "speed": 2.8, "attack_pattern": "quantum_rifle", "drop_rate": 45, "color": "#FF00FF", "wave_unlock": 12, "affixes": ["probability_shield", "quantum_leap"]}, "medic_drone": {"id": "E-16", "name": "Medic Drone", "class": "support", "base_hp": 80, "speed": 2.0, "attack_pattern": "heal_beam", "drop_rate": 15, "color": "#00FF00", "wave_unlock": 12, "affixes": ["mass_heal", "shield_generator"]}, "engineer_bot": {"id": "E-17", "name": "Engineer <PERSON><PERSON>", "class": "support", "base_hp": 100, "speed": 1.5, "attack_pattern": "deploy_turret", "drop_rate": 20, "color": "#FFAA00", "wave_unlock": 13, "affixes": ["repair_others", "build_barriers"]}, "hacker_unit": {"id": "E-18", "name": "Hacker Unit", "class": "support", "base_hp": 60, "speed": 2.5, "attack_pattern": "system_virus", "drop_rate": 25, "color": "#00AAFF", "wave_unlock": 14, "affixes": ["disable_weapons", "corrupt_hud"]}, "plasma_elemental": {"id": "E-19", "name": "Plasma Elemental", "class": "elemental", "base_hp": 90, "speed": 3.0, "attack_pattern": "plasma_wave", "drop_rate": 30, "color": "#FFFF00", "wave_unlock": 15, "affixes": ["fire_immunity", "burning_aura"]}, "ice_construct": {"id": "E-20", "name": "Ice Construct", "class": "elemental", "base_hp": 120, "speed": 1.8, "attack_pattern": "freeze_ray", "drop_rate": 28, "color": "#88DDFF", "wave_unlock": 16, "affixes": ["slow_enemies", "ice_armor"]}, "storm_caller": {"id": "E-21", "name": "<PERSON>", "class": "elemental", "base_hp": 85, "speed": 2.2, "attack_pattern": "lightning_bolt", "drop_rate": 32, "color": "#AAAAFF", "wave_unlock": 17, "affixes": ["chain_lightning", "static_field"]}, "void_spawn": {"id": "E-22", "name": "Void Spawn", "class": "exotic", "base_hp": 70, "speed": 4.0, "attack_pattern": "void_burst", "drop_rate": 35, "color": "#330033", "wave_unlock": 18, "affixes": ["phase_shift", "reality_tear"]}, "time_distorter": {"id": "E-23", "name": "Time Distorter", "class": "exotic", "base_hp": 110, "speed": 2.0, "attack_pattern": "time_slow", "drop_rate": 40, "color": "#6600CC", "wave_unlock": 19, "affixes": ["temporal_shield", "age_weapons"]}, "dimension_ripper": {"id": "E-24", "name": "Dimension Ripper", "class": "exotic", "base_hp": 95, "speed": 3.2, "attack_pattern": "portal_attack", "drop_rate": 38, "color": "#CC0066", "wave_unlock": 20, "affixes": ["dimensional_door", "reality_warp"]}, "nano_swarm": {"id": "E-25", "name": "Nano Swarm", "class": "swarm", "base_hp": 5, "speed": 5.0, "attack_pattern": "overwhelm", "drop_rate": 2, "color": "#999999", "wave_unlock": 1, "affixes": ["multiply", "collective_mind"]}, "micro_hunter": {"id": "E-26", "name": "Micro Hunter", "class": "swarm", "base_hp": 8, "speed": 4.5, "attack_pattern": "micro_bite", "drop_rate": 3, "color": "#666666", "wave_unlock": 2, "affixes": ["poison_sting", "swarm_tactics"]}, "shield_guardian": {"id": "E-27", "name": "Shield Guardian", "class": "guardian", "base_hp": 400, "speed": 1.0, "attack_pattern": "shield_bash", "drop_rate": 50, "color": "#0088FF", "wave_unlock": 20, "affixes": ["energy_shield", "reflect_damage"]}, "fortress_core": {"id": "E-28", "name": "Fortress Core", "class": "guardian", "base_hp": 500, "speed": 0.5, "attack_pattern": "fortress_mode", "drop_rate": 60, "color": "#888888", "wave_unlock": 22, "affixes": ["immobile", "area_denial"]}, "apex_predator": {"id": "E-29", "name": "Apex Predator", "class": "legendary", "base_hp": 300, "speed": 3.5, "attack_pattern": "adaptive_combat", "drop_rate": 75, "color": "#FF0000", "wave_unlock": 25, "affixes": ["learn_patterns", "evolve_abilities"]}, "omega_construct": {"id": "E-30", "name": "Omega Construct", "class": "legendary", "base_hp": 450, "speed": 2.0, "attack_pattern": "omega_beam", "drop_rate": 80, "color": "#FFFFFF", "wave_unlock": 30, "affixes": ["all_immunities", "perfect_aim"]}}, "affixes": {"reflective_bullets": {"name": "Reflective Bullets", "description": "Bullets bounce back at player", "effect": "reflect_projectiles"}, "rapid_fire": {"name": "Rapid Fire", "description": "Increased attack speed", "effect": "fire_rate_x2"}, "explosive_rounds": {"name": "Explosive Rounds", "description": "Bullets explode on impact", "effect": "area_damage"}, "piercing": {"name": "Piercing", "description": "Bullets pass through targets", "effect": "penetration"}, "critical_shots": {"name": "Critical Shots", "description": "Chance for critical damage", "effect": "crit_chance_25"}, "stealth": {"name": "Stealth", "description": "Becomes invisible periodically", "effect": "invisibility_cycles"}, "invisibility": {"name": "Invisibility", "description": "Permanently invisible", "effect": "permanent_stealth"}, "poison_blade": {"name": "Poison Blade", "description": "Attacks apply poison damage", "effect": "poison_dot"}, "explosive_death": {"name": "Explosive Death", "description": "Explodes when killed", "effect": "death_explosion"}, "shielded": {"name": "Shielded", "description": "Reduced damage from frontal attacks", "effect": "damage_reduction_front"}}}