#!/bin/bash

# Galaxy Guns 3D - Post-Create Development Container Setup
# This script runs after the development container is created

set -e

echo "🌌 Galaxy Guns 3D - Post-Create Setup"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the correct directory
if [ ! -f "client/project.godot" ]; then
    print_error "project.godot not found. Make sure you're in the project root."
    exit 1
fi

print_info "Setting up Galaxy Guns 3D development environment..."

# Start virtual display for headless operations
print_info "Starting virtual display..."
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
export DISPLAY=:99

# Wait for display to be ready
sleep 2

# Import Godot project
print_info "Importing Godot project..."
cd client
godot --headless --import --quit
cd ..
print_status "Godot project imported successfully"

# Create necessary directories
print_info "Creating build directories..."
mkdir -p builds/{ios,desktop,web}
mkdir -p logs
mkdir -p temp
print_status "Build directories created"

# Set up Git hooks
print_info "Setting up Git hooks..."
mkdir -p .git/hooks

# Pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Galaxy Guns 3D - Pre-commit hook

echo "🔍 Running pre-commit checks..."

# Check for debug prints in GDScript files
if git diff --cached --name-only | grep -E '\.(gd)$' | xargs grep -l "print(" 2>/dev/null; then
    echo "❌ Debug print statements found in staged files. Please remove them."
    exit 1
fi

# Check for TODO/FIXME comments in staged files
if git diff --cached --name-only | xargs grep -l "TODO\|FIXME" 2>/dev/null; then
    echo "⚠️ TODO/FIXME comments found in staged files. Consider addressing them."
fi

echo "✅ Pre-commit checks passed"
EOF

chmod +x .git/hooks/pre-commit
print_status "Git hooks configured"

# Install additional Python packages for development
print_info "Installing Python development packages..."
python3 -m pip install --user --quiet \
    black \
    flake8 \
    pytest \
    requests \
    pillow \
    pyyaml
print_status "Python packages installed"

# Install additional Ruby gems
print_info "Installing Ruby gems..."
gem install --user-install --quiet \
    bundler \
    cocoapods \
    xcpretty
print_status "Ruby gems installed"

# Create development configuration files
print_info "Creating development configuration..."

# Create local environment file
cat > .env.local << EOF
# Galaxy Guns 3D - Local Development Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=debug

# Server configuration
SERVER_HOST=localhost
SERVER_PORT=8080
DATABASE_URL=****************************************************/galaxy_guns_dev
REDIS_URL=redis://redis-dev:6379

# Godot configuration
GODOT_HEADLESS=true
GODOT_VERBOSE=false

# Build configuration
BUILD_TYPE=debug
EXPORT_DEBUG=true
EOF

print_status "Development configuration created"

# Set up asset processing pipeline
print_info "Setting up asset processing pipeline..."

# Create asset processing script
cat > scripts/process_assets.sh << 'EOF'
#!/bin/bash
# Galaxy Guns 3D - Asset Processing Pipeline

echo "🎨 Processing assets..."

# Process sprites
if [ -d "assets/sprites/raw" ]; then
    echo "Processing sprites..."
    # Add sprite processing logic here
fi

# Process audio
if [ -d "assets/audio/raw" ]; then
    echo "Processing audio..."
    # Add audio processing logic here
fi

echo "✅ Asset processing complete"
EOF

chmod +x scripts/process_assets.sh
print_status "Asset processing pipeline configured"

# Create development database schema
print_info "Setting up development database..."
cat > .devcontainer/init-db.sql << 'EOF'
-- Galaxy Guns 3D - Development Database Schema

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Player stats table
CREATE TABLE IF NOT EXISTS player_stats (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    level INTEGER DEFAULT 1,
    experience INTEGER DEFAULT 0,
    kills INTEGER DEFAULT 0,
    deaths INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    losses INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Game sessions table
CREATE TABLE IF NOT EXISTS game_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    game_mode VARCHAR(50) NOT NULL,
    duration INTEGER NOT NULL,
    kills INTEGER DEFAULT 0,
    deaths INTEGER DEFAULT 0,
    score INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert test data
INSERT INTO users (username, email, password_hash) VALUES
    ('testplayer1', '<EMAIL>', 'hashed_password_1'),
    ('testplayer2', '<EMAIL>', 'hashed_password_2')
ON CONFLICT (username) DO NOTHING;

INSERT INTO player_stats (user_id, level, experience, kills, deaths, wins, losses) VALUES
    (1, 5, 1250, 45, 23, 12, 8),
    (2, 3, 750, 28, 31, 7, 13)
ON CONFLICT DO NOTHING;
EOF

print_status "Development database schema created"

# Create useful development aliases
print_info "Setting up development aliases..."
cat >> ~/.bashrc << 'EOF'

# Galaxy Guns 3D Development Aliases
alias gg-godot='cd /workspace/client && godot'
alias gg-build='cd /workspace && ./scripts/build.sh'
alias gg-test='cd /workspace && ./scripts/run_tests.sh'
alias gg-server='cd /workspace/server && cargo run'
alias gg-logs='tail -f /workspace/logs/*.log'
alias gg-clean='cd /workspace && rm -rf builds/* logs/* temp/*'

# Git aliases
alias gs='git status'
alias ga='git add'
alias gc='git commit'
alias gp='git push'
alias gl='git log --oneline -10'

# Docker aliases
alias dc='docker-compose'
alias dps='docker ps'
alias dlogs='docker-compose logs -f'
EOF

print_status "Development aliases configured"

# Create VS Code settings for the project
print_info "Configuring VS Code settings..."
mkdir -p .vscode

cat > .vscode/settings.json << 'EOF'
{
    "godot_tools.editor_path": "/usr/local/bin/godot",
    "godot_tools.gdscript_lsp_server_port": 6005,
    "files.associations": {
        "*.gd": "gdscript",
        "*.tres": "godot-resource",
        "*.tscn": "godot-scene"
    },
    "editor.rulers": [100],
    "editor.tabSize": 4,
    "editor.insertSpaces": false,
    "[gdscript]": {
        "editor.tabSize": 4,
        "editor.insertSpaces": false
    },
    "files.exclude": {
        "**/.godot": true,
        "**/.import": true,
        "**/builds": true
    }
}
EOF

print_status "VS Code settings configured"

# Create development documentation
print_info "Creating development documentation..."
cat > docs/development-setup.md << 'EOF'
# Galaxy Guns 3D - Development Setup

## Quick Start

1. Open the project in VS Code with the Dev Container extension
2. The container will automatically build and configure the environment
3. Open `client/project.godot` in Godot to start development

## Available Commands

- `gg-godot` - Open Godot editor
- `gg-build` - Build the project
- `gg-test` - Run tests
- `gg-server` - Start development server
- `gg-clean` - Clean build artifacts

## Development Workflow

1. Make changes to the code
2. Test locally using `gg-test`
3. Build using `gg-build`
4. Commit changes with semantic commit messages
5. Push to trigger CI/CD pipeline

## Debugging

- Use VS Code's integrated debugger for GDScript
- Check logs with `gg-logs`
- Monitor containers with `dps` and `dlogs`

## Database Access

- PostgreSQL: `localhost:5432`
- Redis: `localhost:6379`
- Credentials in `.env.local`
EOF

print_status "Development documentation created"

# Final setup steps
print_info "Finalizing setup..."

# Make scripts executable
find scripts -name "*.sh" -exec chmod +x {} \;

# Create initial commit if this is a new repository
if [ ! -d ".git" ]; then
    git init
    git add .
    git commit -m "feat: initial project setup with development container"
fi

print_status "Development environment setup complete!"

echo ""
echo "🎉 Galaxy Guns 3D development environment is ready!"
echo ""
echo "Next steps:"
echo "1. Open client/project.godot in Godot"
echo "2. Configure iOS export templates"
echo "3. Start developing!"
echo ""
echo "Useful commands:"
echo "- gg-godot  : Open Godot editor"
echo "- gg-build  : Build project"
echo "- gg-test   : Run tests"
echo "- gg-clean  : Clean artifacts"
echo ""
