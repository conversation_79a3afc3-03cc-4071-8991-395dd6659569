/*
Galaxy Guns 3D - Animated Water Surface Shader
Pixel-art compatible water effects with normal mapping and reflection
Optimized for mobile performance
*/

shader_type canvas_item;

// Texture inputs
uniform sampler2D water_texture : source_color, filter_nearest, repeat_enable;
uniform sampler2D normal_texture : hint_normal, filter_nearest, repeat_enable;
uniform sampler2D foam_texture : source_color, filter_nearest, repeat_enable;

// Water properties
uniform vec4 water_color : source_color = vec4(0.2, 0.4, 0.8, 0.7);
uniform vec4 deep_water_color : source_color = vec4(0.1, 0.2, 0.6, 0.9);
uniform float water_speed : hint_range(0.0, 2.0) = 0.5;
uniform float wave_strength : hint_range(0.0, 0.1) = 0.02;
uniform float foam_intensity : hint_range(0.0, 2.0) = 1.0;

// Animation parameters
uniform vec2 wave_direction_1 = vec2(1.0, 0.3);
uniform vec2 wave_direction_2 = vec2(-0.5, 1.0);
uniform float wave_frequency_1 : hint_range(0.1, 5.0) = 1.0;
uniform float wave_frequency_2 : hint_range(0.1, 5.0) = 1.5;

// Reflection and refraction
uniform float reflection_strength : hint_range(0.0, 1.0) = 0.3;
uniform float refraction_strength : hint_range(0.0, 0.1) = 0.02;
uniform bool enable_pixel_snap = true;

varying vec2 world_position;

vec2 snap_to_pixel(vec2 uv) {
    if (enable_pixel_snap) {
        float pixel_size = 1.0;
        return floor(uv * pixel_size) / pixel_size;
    }
    return uv;
}

void vertex() {
    world_position = (MODEL_MATRIX * vec4(VERTEX, 0.0, 1.0)).xy;
}

void fragment() {
    float time = TIME * water_speed;
    
    // Calculate animated UV coordinates
    vec2 wave_uv_1 = UV + normalize(wave_direction_1) * time * wave_frequency_1;
    vec2 wave_uv_2 = UV + normalize(wave_direction_2) * time * wave_frequency_2;
    
    // Snap to pixel grid for pixel art aesthetic
    wave_uv_1 = snap_to_pixel(wave_uv_1);
    wave_uv_2 = snap_to_pixel(wave_uv_2);
    
    // Sample water textures
    vec4 water_sample_1 = texture(water_texture, wave_uv_1);
    vec4 water_sample_2 = texture(water_texture, wave_uv_2);
    
    // Combine wave samples
    vec4 combined_water = mix(water_sample_1, water_sample_2, 0.5);
    
    // Sample normal maps for surface detail
    vec3 normal_1 = texture(normal_texture, wave_uv_1).rgb * 2.0 - 1.0;
    vec3 normal_2 = texture(normal_texture, wave_uv_2).rgb * 2.0 - 1.0;
    vec3 combined_normal = normalize(normal_1 + normal_2);
    
    // Calculate wave displacement
    vec2 wave_offset = combined_normal.xy * wave_strength;
    
    // Apply refraction effect
    vec2 refracted_uv = UV + wave_offset * refraction_strength;
    refracted_uv = snap_to_pixel(refracted_uv);
    
    // Sample foam texture for surface detail
    vec2 foam_uv = refracted_uv + time * 0.1;
    foam_uv = snap_to_pixel(foam_uv);
    vec4 foam_sample = texture(foam_texture, foam_uv);
    
    // Calculate depth-based color mixing
    float depth_factor = combined_water.r; // Use red channel as depth
    vec4 final_water_color = mix(water_color, deep_water_color, depth_factor);
    
    // Add foam highlights
    final_water_color.rgb += foam_sample.rgb * foam_intensity * (1.0 - depth_factor);
    
    // Simple reflection effect (would be enhanced with screen-space reflections)
    float reflection_factor = reflection_strength * (1.0 - depth_factor);
    final_water_color.rgb += vec3(reflection_factor);
    
    // Apply wave animation to alpha for surface ripples
    float wave_alpha = sin(time * 3.0 + world_position.x * 0.1 + world_position.y * 0.1) * 0.1 + 0.9;
    final_water_color.a *= wave_alpha;
    
    COLOR = final_water_color;
}
