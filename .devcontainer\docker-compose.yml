version: '3.8'

services:
  godot-dev:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        GODOT_VERSION: "4.3-stable"
    
    container_name: galaxy-guns-dev
    hostname: galaxy-guns-dev
    
    volumes:
      - ../:/workspace:cached
      - godot-cache:/home/<USER>/.cache/godot
      - vscode-extensions:/home/<USER>/.vscode-server/extensions
      - /var/run/docker.sock:/var/run/docker.sock
    
    ports:
      - "6005:6005"  # Godot LSP
      - "8080:8080"  # Development server
      - "3000:3000"  # Web preview
    
    environment:
      - DISPLAY=:99
      - GODOT_VERSION=4.3-stable
      - PROJECT_NAME=Galaxy Guns 3D
      - DEBIAN_FRONTEND=noninteractive
    
    # Keep container running
    command: sleep infinity
    
    # Enable X11 forwarding for GUI applications
    stdin_open: true
    tty: true
    
    # Security settings
    cap_add:
      - SYS_PTRACE
    security_opt:
      - seccomp:unconfined
    
    # Network configuration
    networks:
      - galaxy-guns-network

  # Development database for testing
  postgres-dev:
    image: postgres:15-alpine
    container_name: galaxy-guns-db-dev
    
    environment:
      POSTGRES_DB: galaxy_guns_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    
    ports:
      - "5432:5432"
    
    networks:
      - galaxy-guns-network

  # Redis for caching and sessions
  redis-dev:
    image: redis:7-alpine
    container_name: galaxy-guns-redis-dev
    
    command: redis-server --appendonly yes
    
    volumes:
      - redis-data:/data
    
    ports:
      - "6379:6379"
    
    networks:
      - galaxy-guns-network

volumes:
  godot-cache:
    name: galaxy-guns-godot-cache
  vscode-extensions:
    name: galaxy-guns-vscode-extensions
  postgres-data:
    name: galaxy-guns-postgres-data
  redis-data:
    name: galaxy-guns-redis-data

networks:
  galaxy-guns-network:
    name: galaxy-guns-network
    driver: bridge
