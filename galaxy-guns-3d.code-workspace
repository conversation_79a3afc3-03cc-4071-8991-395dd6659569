{
  "folders": [
    {
      "name": "Client (Godot)",
      "path": "./client"
    },
    {
      "name": "Server",
      "path": "./server"
    },
    {
      "name": "Shared",
      "path": "./shared"
    },
    {
      "name": "Marketing",
      "path": "./marketing"
    },
    {
      "name": "CI/CD",
      "path": "./ci"
    },
    {
      "name": "Documentation",
      "path": "./docs"
    },
    {
      "name": "<PERSON>rip<PERSON>",
      "path": "./scripts"
    },
    {
      "name": "Root",
      "path": "."
    }
  ],
  "settings": {
    // Godot settings
    "godot_tools.editor_path": "godot",
    "godot_tools.gdscript_lsp_server_port": 6005,
    
    // File associations
    "files.associations": {
      "*.gd": "gdscript",
      "*.cs": "csharp",
      "*.tres": "godot-resource",
      "*.tscn": "godot-scene",
      "*.cfg": "ini",
      "*.import": "ini",
      "Fastfile": "ruby",
      "Appfile": "ruby",
      "Matchfile": "ruby"
    },
    
    // Editor settings
    "editor.insertSpaces": true,
    "editor.tabSize": 4,
    "editor.detectIndentation": true,
    "editor.rulers": [80, 120],
    "editor.wordWrap": "wordWrapColumn",
    "editor.wordWrapColumn": 120,
    
    // GDScript specific settings
    "[gdscript]": {
      "editor.tabSize": 4,
      "editor.insertSpaces": false,
      "editor.rulers": [100]
    },
    
    // JSON settings
    "[json]": {
      "editor.tabSize": 2,
      "editor.insertSpaces": true
    },
    
    // YAML settings
    "[yaml]": {
      "editor.tabSize": 2,
      "editor.insertSpaces": true
    },
    
    // Markdown settings
    "[markdown]": {
      "editor.wordWrap": "on",
      "editor.quickSuggestions": {
        "comments": "off",
        "strings": "off",
        "other": "off"
      }
    },
    
    // File exclusions
    "files.exclude": {
      "**/.godot": true,
      "**/.import": true,
      "**/builds": true,
      "**/node_modules": true,
      "**/target": true,
      "**/.DS_Store": true,
      "**/Thumbs.db": true
    },
    
    // Search exclusions
    "search.exclude": {
      "**/.godot": true,
      "**/.import": true,
      "**/builds": true,
      "**/node_modules": true,
      "**/target": true
    },
    
    // Git settings
    "git.ignoreLimitWarning": true,
    "git.autofetch": true,
    "git.confirmSync": false,
    
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.defaultProfile.osx": "zsh",
    "terminal.integrated.defaultProfile.linux": "bash",
    
    // Formatting
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "explicit"
    },
    
    // IntelliSense
    "editor.quickSuggestions": {
      "comments": "on",
      "strings": "on",
      "other": "on"
    },
    "editor.suggestSelection": "first",
    "editor.acceptSuggestionOnCommitCharacter": true,
    
    // Debugging
    "debug.allowBreakpointsEverywhere": true,
    "debug.inlineValues": "on",
    
    // Extensions
    "extensions.ignoreRecommendations": false,
    
    // Spell checking
    "cSpell.words": [
      "Godot",
      "GDScript",
      "Fastlane",
      "TestFlight",
      "Xcode",
      "iOS",
      "macOS",
      "Orvyn",
      "galaxyguns",
      "multiplayer",
      "netcode",
      "ASTC",
      "framerate"
    ],
    
    // Theme and appearance
    "workbench.colorTheme": "Default Dark+",
    "workbench.iconTheme": "vs-seti",
    
    // Performance
    "files.watcherExclude": {
      "**/.godot/**": true,
      "**/builds/**": true,
      "**/node_modules/**": true,
      "**/target/**": true
    }
  },
  "extensions": {
    "recommendations": [
      // Godot development
      "geequlim.godot-tools",
      
      // General development
      "ms-vscode.vscode-json",
      "redhat.vscode-yaml",
      "ms-python.python",
      "rust-lang.rust-analyzer",
      
      // Git and version control
      "eamodio.gitlens",
      "github.vscode-pull-request-github",
      
      // Markdown and documentation
      "yzhang.markdown-all-in-one",
      "davidanson.vscode-markdownlint",
      
      // Code quality
      "streetsidesoftware.code-spell-checker",
      "editorconfig.editorconfig",
      
      // iOS and mobile development
      "ms-vscode.vscode-ios-web-debug",
      
      // CI/CD and DevOps
      "ms-vscode.vscode-github-actions",
      "ms-azuretools.vscode-docker",
      
      // Utilities
      "formulahendry.auto-rename-tag",
      "christian-kohler.path-intellisense",
      "ms-vscode.hexeditor"
    ]
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "Godot: Import Project",
        "type": "shell",
        "command": "godot",
        "args": ["--headless", "--import"],
        "options": {
          "cwd": "${workspaceFolder}/client"
        },
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        },
        "problemMatcher": []
      },
      {
        "label": "Godot: Export iOS",
        "type": "shell",
        "command": "godot",
        "args": ["--headless", "--export-release", "iOS", "../builds/ios/GalaxyGuns.ipa"],
        "options": {
          "cwd": "${workspaceFolder}/client"
        },
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        },
        "problemMatcher": []
      },
      {
        "label": "Fastlane: Beta Deploy",
        "type": "shell",
        "command": "fastlane",
        "args": ["ios", "beta"],
        "options": {
          "cwd": "${workspaceFolder}/ci"
        },
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        },
        "problemMatcher": []
      },
      {
        "label": "Setup: Development Environment",
        "type": "shell",
        "command": "powershell",
        "args": ["-ExecutionPolicy", "Bypass", "-File", "./scripts/setup_dev_environment.ps1"],
        "options": {
          "cwd": "${workspaceFolder}"
        },
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "shared"
        },
        "problemMatcher": []
      }
    ]
  },
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Launch Godot Editor",
        "type": "godot",
        "request": "launch",
        "project": "${workspaceFolder}/client",
        "port": 6005,
        "address": "127.0.0.1",
        "launch_game_instance": false,
        "launch_scene": false
      },
      {
        "name": "Launch Game Scene",
        "type": "godot",
        "request": "launch",
        "project": "${workspaceFolder}/client",
        "port": 6005,
        "address": "127.0.0.1",
        "launch_game_instance": true,
        "launch_scene": true,
        "scene_file": "res://scenes/main_menu.tscn"
      }
    ]
  }
}
