# Simple test script to validate game setup
extends SceneTree

func _init():
	print("🧪 Testing Galaxy Guns 3D setup...")
	
	# Test scene loading
	test_scene_loading()
	
	# Test script compilation
	test_script_compilation()
	
	print("✅ Basic setup test completed")
	quit()

func test_scene_loading():
	print("📁 Testing scene loading...")
	
	var scenes_to_test = [
		"res://scenes/main_menu.tscn",
		"res://scenes/game.tscn", 
		"res://scenes/player.tscn",
		"res://scenes/enemy.tscn",
		"res://scenes/ui/game_ui.tscn",
		"res://scenes/weapons/basic_rifle.tscn"
	]
	
	for scene_path in scenes_to_test:
		if ResourceLoader.exists(scene_path):
			var scene = load(scene_path)
			if scene:
				print("  ✅ %s - OK" % scene_path)
			else:
				print("  ❌ %s - Failed to load" % scene_path)
		else:
			print("  ❌ %s - File not found" % scene_path)

func test_script_compilation():
	print("📜 Testing script compilation...")
	
	var scripts_to_test = [
		"res://scripts/ui/main_menu.gd",
		"res://scripts/ui/game_ui.gd",
		"res://scripts/game_manager.gd",
		"res://scripts/player/player_controller.gd",
		"res://scripts/ai/enemy_ai.gd",
		"res://scripts/weapons/weapon_base.gd"
	]
	
	for script_path in scripts_to_test:
		if ResourceLoader.exists(script_path):
			var script = load(script_path)
			if script:
				print("  ✅ %s - OK" % script_path)
			else:
				print("  ❌ %s - Failed to compile" % script_path)
		else:
			print("  ❌ %s - File not found" % script_path)
