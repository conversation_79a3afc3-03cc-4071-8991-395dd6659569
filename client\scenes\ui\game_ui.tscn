[gd_scene load_steps=2 format=3 uid="uid://ui_scene"]

[ext_resource type="Script" path="res://scripts/ui/game_ui.gd" id="1_game_ui"]

[node name="GameUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_game_ui")

[node name="TouchControls" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MovementJoystick" type="Control" parent="TouchControls"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 50.0
offset_top = -200.0
offset_right = 200.0
offset_bottom = -50.0

[node name="JoystickBase" type="TextureRect" parent="TouchControls/MovementJoystick"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.2, 0.2, 0.2, 0.5)

[node name="JoystickKnob" type="TextureRect" parent="TouchControls/MovementJoystick"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -25.0
offset_top = -25.0
offset_right = 25.0
offset_bottom = 25.0
color = Color(0.8, 0.8, 0.8, 0.8)

[node name="LookArea" type="Control" parent="TouchControls"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="FireButton" type="Button" parent="TouchControls"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -150.0
offset_top = -150.0
offset_right = -50.0
offset_bottom = -50.0
text = "FIRE"

[node name="ReloadButton" type="Button" parent="TouchControls"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -150.0
offset_top = -250.0
offset_right = -50.0
offset_bottom = -170.0
text = "RELOAD"

[node name="JumpButton" type="Button" parent="TouchControls"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -250.0
offset_top = -150.0
offset_right = -170.0
offset_bottom = -50.0
text = "JUMP"

[node name="HUD" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="HealthBar" type="ProgressBar" parent="HUD"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -80.0
offset_right = 220.0
offset_bottom = -50.0
value = 100.0
show_percentage = false

[node name="HealthLabel" type="Label" parent="HUD"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -100.0
offset_right = 120.0
offset_bottom = -80.0
text = "Health: 100"

[node name="AmmoLabel" type="Label" parent="HUD"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -200.0
offset_top = -100.0
offset_right = -20.0
offset_bottom = -80.0
text = "Ammo: 30/120"
horizontal_alignment = 2

[node name="Crosshair" type="Control" parent="HUD"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -10.0
offset_top = -10.0
offset_right = 10.0
offset_bottom = 10.0

[node name="CrosshairH" type="ColorRect" parent="HUD/Crosshair"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -8.0
offset_top = -1.0
offset_right = 8.0
offset_bottom = 1.0
color = Color(1, 1, 1, 0.8)

[node name="CrosshairV" type="ColorRect" parent="HUD/Crosshair"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -1.0
offset_top = -8.0
offset_right = 1.0
offset_bottom = 8.0
color = Color(1, 1, 1, 0.8)

[node name="DebugOverlay" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("2_debug")

[connection signal="button_down" from="TouchControls/FireButton" to="." method="_on_fire_button_down"]
[connection signal="button_up" from="TouchControls/FireButton" to="." method="_on_fire_button_up"]
[connection signal="pressed" from="TouchControls/ReloadButton" to="." method="_on_reload_button_pressed"]
[connection signal="pressed" from="TouchControls/JumpButton" to="." method="_on_jump_button_pressed"]
