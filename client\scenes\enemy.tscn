[gd_scene load_steps=4 format=3 uid="uid://enemy_scene"]

[ext_resource type="Script" path="res://scripts/ai/enemy_ai.gd" id="1_enemy_ai"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_1"]
radius = 0.4
height = 1.6

[sub_resource type="CapsuleMesh" id="CapsuleMesh_1"]
radius = 0.4
height = 1.6

[node name="Enemy" type="CharacterBody3D" groups=["enemies"]]
script = ExtResource("1_enemy_ai")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.8, 0)
shape = SubResource("CapsuleShape3D_1")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.8, 0)
mesh = SubResource("CapsuleMesh_1")
surface_material_override/0 = SubResource("StandardMaterial3D_red")

[node name="NavigationAgent3D" type="NavigationAgent3D" parent="."]

[node name="VisionRaycast" type="RayCast3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.5, 0)
target_position = Vector3(0, 0, -50)
collision_mask = 2

[node name="WeaponPoint" type="Marker3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.2, -0.5)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_red"]
albedo_color = Color(0.8, 0.2, 0.2, 1)
