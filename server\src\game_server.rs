// Galaxy Guns 3D - Dedicated Game Server
// High-performance authoritative server with lag compensation
// Rust implementation for optimal performance and security

use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::mpsc;
use tokio_tungstenite::{accept_async, WebSocketStream};
use tungstenite::Message;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// Server configuration
const TICK_RATE: u64 = 60; // 60 Hz server simulation
const MAX_PLAYERS: usize = 8;
const HEARTBEAT_INTERVAL: Duration = Duration::from_secs(1);
const CLIENT_TIMEOUT: Duration = Duration::from_secs(10);

// Player data structure
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Player {
    pub id: Uuid,
    pub username: String,
    pub position: Vector3,
    pub rotation: Vector3,
    pub velocity: Vector3,
    pub health: i32,
    pub weapon_state: WeaponState,
    pub last_update: Instant,
    pub ping: f32,
    pub connected: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vector3 {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WeaponState {
    pub weapon_type: String,
    pub ammo: i32,
    pub is_firing: bool,
    pub last_shot_time: Instant,
}

// Input snapshot for client prediction
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InputSnapshot {
    pub tick: u64,
    pub timestamp: f64,
    pub movement_input: Vector2,
    pub look_input: Vector2,
    pub actions: HashMap<String, bool>,
    pub sequence_number: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vector2 {
    pub x: f32,
    pub y: f32,
}

// Game state snapshot for rollback
#[derive(Debug, Clone)]
pub struct GameStateSnapshot {
    pub tick: u64,
    pub timestamp: Instant,
    pub players: HashMap<Uuid, Player>,
    pub projectiles: Vec<Projectile>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Projectile {
    pub id: Uuid,
    pub owner_id: Uuid,
    pub position: Vector3,
    pub velocity: Vector3,
    pub spawn_time: Instant,
    pub lifetime: Duration,
}

// Network messages
#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ClientMessage {
    Join { username: String },
    Input { input: InputSnapshot },
    Ping { timestamp: f64 },
    Disconnect,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ServerMessage {
    Welcome { player_id: Uuid },
    GameState { tick: u64, players: HashMap<Uuid, Player> },
    PlayerJoined { player: Player },
    PlayerLeft { player_id: Uuid },
    Pong { timestamp: f64 },
    Error { message: String },
}

// Main game server structure
pub struct GameServer {
    players: Arc<Mutex<HashMap<Uuid, Player>>>,
    game_state_history: Arc<Mutex<Vec<GameStateSnapshot>>>,
    current_tick: Arc<Mutex<u64>>,
    running: Arc<Mutex<bool>>,
}

impl GameServer {
    pub fn new() -> Self {
        Self {
            players: Arc::new(Mutex::new(HashMap::new())),
            game_state_history: Arc::new(Mutex::new(Vec::new())),
            current_tick: Arc::new(Mutex::new(0)),
            running: Arc::new(Mutex::new(false)),
        }
    }

    pub async fn start(&self, addr: &str) -> Result<(), Box<dyn std::error::Error>> {
        let listener = TcpListener::bind(addr).await?;
        println!("🖥️ Game server listening on: {}", addr);

        // Set server as running
        *self.running.lock().unwrap() = true;

        // Start game simulation loop
        let server_clone = self.clone();
        tokio::spawn(async move {
            server_clone.game_loop().await;
        });

        // Start state history cleanup
        let server_clone = self.clone();
        tokio::spawn(async move {
            server_clone.cleanup_loop().await;
        });

        // Accept client connections
        while *self.running.lock().unwrap() {
            match listener.accept().await {
                Ok((stream, addr)) => {
                    let server_clone = self.clone();
                    tokio::spawn(async move {
                        if let Err(e) = server_clone.handle_client(stream, addr).await {
                            eprintln!("❌ Error handling client {}: {}", addr, e);
                        }
                    });
                }
                Err(e) => {
                    eprintln!("❌ Error accepting connection: {}", e);
                }
            }
        }

        Ok(())
    }

    async fn handle_client(&self, stream: TcpStream, addr: SocketAddr) -> Result<(), Box<dyn std::error::Error>> {
        let ws_stream = accept_async(stream).await?;
        println("👤 Client connected: {}", addr);

        let (tx, mut rx) = mpsc::unbounded_channel();
        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        let server_clone = self.clone();
        let tx_clone = tx.clone();

        // Handle incoming messages
        let receive_task = tokio::spawn(async move {
            while let Some(msg) = ws_receiver.next().await {
                match msg {
                    Ok(Message::Text(text)) => {
                        if let Ok(client_msg) = serde_json::from_str::<ClientMessage>(&text) {
                            server_clone.process_client_message(client_msg, tx_clone.clone()).await;
                        }
                    }
                    Ok(Message::Close(_)) => break,
                    Err(e) => {
                        eprintln!("❌ WebSocket error: {}", e);
                        break;
                    }
                    _ => {}
                }
            }
        });

        // Handle outgoing messages
        let send_task = tokio::spawn(async move {
            while let Some(msg) = rx.recv().await {
                if let Ok(json) = serde_json::to_string(&msg) {
                    if ws_sender.send(Message::Text(json)).await.is_err() {
                        break;
                    }
                }
            }
        });

        // Wait for either task to complete
        tokio::select! {
            _ = receive_task => {},
            _ = send_task => {},
        }

        println!("👋 Client disconnected: {}", addr);
        Ok(())
    }

    async fn process_client_message(&self, message: ClientMessage, tx: mpsc::UnboundedSender<ServerMessage>) {
        match message {
            ClientMessage::Join { username } => {
                let player_id = Uuid::new_v4();
                let player = Player {
                    id: player_id,
                    username: username.clone(),
                    position: Vector3 { x: 0.0, y: 0.0, z: 0.0 },
                    rotation: Vector3 { x: 0.0, y: 0.0, z: 0.0 },
                    velocity: Vector3 { x: 0.0, y: 0.0, z: 0.0 },
                    health: 100,
                    weapon_state: WeaponState {
                        weapon_type: "assault_rifle".to_string(),
                        ammo: 30,
                        is_firing: false,
                        last_shot_time: Instant::now(),
                    },
                    last_update: Instant::now(),
                    ping: 0.0,
                    connected: true,
                };

                // Add player to game
                {
                    let mut players = self.players.lock().unwrap();
                    if players.len() < MAX_PLAYERS {
                        players.insert(player_id, player.clone());
                        
                        // Send welcome message
                        let _ = tx.send(ServerMessage::Welcome { player_id });
                        
                        // Notify other players
                        self.broadcast_to_others(ServerMessage::PlayerJoined { player: player.clone() }, player_id).await;
                        
                        println!("✅ Player joined: {} ({})", username, player_id);
                    } else {
                        let _ = tx.send(ServerMessage::Error { 
                            message: "Server full".to_string() 
                        });
                    }
                }
            }

            ClientMessage::Input { input } => {
                self.process_player_input(input).await;
            }

            ClientMessage::Ping { timestamp } => {
                let _ = tx.send(ServerMessage::Pong { timestamp });
            }

            ClientMessage::Disconnect => {
                // Handle disconnect
            }
        }
    }

    async fn process_player_input(&self, input: InputSnapshot) {
        // Validate input (anti-cheat)
        if !self.validate_input(&input) {
            println!("⚠️ Invalid input detected from player");
            return;
        }

        // Apply input to player state
        // This would integrate with the game simulation
        // For now, simplified implementation
    }

    fn validate_input(&self, input: &InputSnapshot) -> bool {
        // Basic input validation
        if input.movement_input.x.abs() > 1.0 || input.movement_input.y.abs() > 1.0 {
            return false;
        }

        // Check for reasonable look input
        if input.look_input.x.abs() > 10.0 || input.look_input.y.abs() > 10.0 {
            return false;
        }

        true
    }

    async fn game_loop(&self) {
        let mut interval = tokio::time::interval(Duration::from_millis(1000 / TICK_RATE));
        
        while *self.running.lock().unwrap() {
            interval.tick().await;
            
            let current_tick = {
                let mut tick = self.current_tick.lock().unwrap();
                *tick += 1;
                *tick
            };

            // Update game simulation
            self.update_game_state(current_tick).await;
            
            // Send state updates to clients
            self.send_state_updates(current_tick).await;
            
            // Record state for rollback
            self.record_game_state(current_tick).await;
        }
    }

    async fn update_game_state(&self, tick: u64) {
        let mut players = self.players.lock().unwrap();
        
        // Update player physics
        for player in players.values_mut() {
            // Apply velocity to position
            player.position.x += player.velocity.x / TICK_RATE as f32;
            player.position.y += player.velocity.y / TICK_RATE as f32;
            player.position.z += player.velocity.z / TICK_RATE as f32;
            
            // Apply friction
            player.velocity.x *= 0.9;
            player.velocity.z *= 0.9;
            
            // Apply gravity
            player.velocity.y -= 9.81 / TICK_RATE as f32;
            
            // Ground collision (simplified)
            if player.position.y < 0.0 {
                player.position.y = 0.0;
                player.velocity.y = 0.0;
            }
        }
        
        // Update projectiles
        // Update game objects
        // Process collisions
        // Handle weapon firing
    }

    async fn send_state_updates(&self, tick: u64) {
        let players = self.players.lock().unwrap().clone();
        
        let state_message = ServerMessage::GameState {
            tick,
            players: players.clone(),
        };
        
        self.broadcast_to_all(state_message).await;
    }

    async fn record_game_state(&self, tick: u64) {
        let players = self.players.lock().unwrap().clone();
        
        let snapshot = GameStateSnapshot {
            tick,
            timestamp: Instant::now(),
            players,
            projectiles: Vec::new(), // Would include actual projectiles
        };
        
        let mut history = self.game_state_history.lock().unwrap();
        history.push(snapshot);
        
        // Limit history size (keep last 5 seconds at 60Hz = 300 snapshots)
        if history.len() > 300 {
            history.remove(0);
        }
    }

    async fn cleanup_loop(&self) {
        let mut interval = tokio::time::interval(Duration::from_secs(5));
        
        while *self.running.lock().unwrap() {
            interval.tick().await;
            
            // Remove disconnected players
            let mut players = self.players.lock().unwrap();
            let mut to_remove = Vec::new();
            
            for (id, player) in players.iter() {
                if player.last_update.elapsed() > CLIENT_TIMEOUT {
                    to_remove.push(*id);
                }
            }
            
            for id in to_remove {
                players.remove(&id);
                println!("🧹 Removed inactive player: {}", id);
                
                // Notify other players
                drop(players); // Release lock before async call
                self.broadcast_to_all(ServerMessage::PlayerLeft { player_id: id }).await;
                players = self.players.lock().unwrap(); // Re-acquire lock
            }
        }
    }

    async fn broadcast_to_all(&self, message: ServerMessage) {
        // This would send to all connected clients
        // Implementation depends on how client connections are stored
    }

    async fn broadcast_to_others(&self, message: ServerMessage, exclude_id: Uuid) {
        // This would send to all clients except the excluded one
        // Implementation depends on how client connections are stored
    }

    pub fn stop(&self) {
        *self.running.lock().unwrap() = false;
    }
}

impl Clone for GameServer {
    fn clone(&self) -> Self {
        Self {
            players: Arc::clone(&self.players),
            game_state_history: Arc::clone(&self.game_state_history),
            current_tick: Arc::clone(&self.current_tick),
            running: Arc::clone(&self.running),
        }
    }
}

// Lag compensation implementation
pub struct LagCompensation {
    state_history: Vec<GameStateSnapshot>,
    max_rollback_time: Duration,
}

impl LagCompensation {
    pub fn new() -> Self {
        Self {
            state_history: Vec::new(),
            max_rollback_time: Duration::from_millis(200), // 200ms max rollback
        }
    }

    pub fn validate_shot(&mut self, player_id: Uuid, shot_time: Instant, target_pos: Vector3) -> bool {
        // Find game state at shot time
        let target_time = shot_time;
        let mut closest_state = None;
        let mut closest_distance = Duration::from_secs(1);

        for state in &self.state_history {
            let distance = if state.timestamp > target_time {
                state.timestamp.duration_since(target_time)
            } else {
                target_time.duration_since(state.timestamp)
            };

            if distance < closest_distance {
                closest_distance = distance;
                closest_state = Some(state);
            }
        }

        if let Some(state) = closest_state {
            // Validate shot against rolled-back state
            return self.validate_shot_against_state(player_id, target_pos, state);
        }

        false
    }

    fn validate_shot_against_state(&self, player_id: Uuid, target_pos: Vector3, state: &GameStateSnapshot) -> bool {
        // Check if target was actually at that position in the rolled-back state
        for (id, player) in &state.players {
            if *id != player_id {
                let distance = self.distance_3d(&player.position, &target_pos);
                if distance < 1.0 { // 1 meter tolerance
                    return true;
                }
            }
        }
        false
    }

    fn distance_3d(&self, a: &Vector3, b: &Vector3) -> f32 {
        let dx = a.x - b.x;
        let dy = a.y - b.y;
        let dz = a.z - b.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    pub fn cleanup_old_states(&mut self) {
        let cutoff_time = Instant::now() - self.max_rollback_time;
        self.state_history.retain(|state| state.timestamp > cutoff_time);
    }
}

// Main server entry point
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Starting Galaxy Guns 3D Game Server");
    
    let server = GameServer::new();
    server.start("0.0.0.0:7777").await?;
    
    Ok(())
}

// Unit tests
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_input_validation() {
        let server = GameServer::new();
        
        let valid_input = InputSnapshot {
            tick: 1,
            timestamp: 0.0,
            movement_input: Vector2 { x: 0.5, y: 0.8 },
            look_input: Vector2 { x: 1.0, y: -0.5 },
            actions: HashMap::new(),
            sequence_number: 1,
        };
        
        assert!(server.validate_input(&valid_input));
        
        let invalid_input = InputSnapshot {
            tick: 1,
            timestamp: 0.0,
            movement_input: Vector2 { x: 2.0, y: 0.8 }, // Invalid: > 1.0
            look_input: Vector2 { x: 1.0, y: -0.5 },
            actions: HashMap::new(),
            sequence_number: 1,
        };
        
        assert!(!server.validate_input(&invalid_input));
    }

    #[test]
    fn test_lag_compensation() {
        let mut lag_comp = LagCompensation::new();
        
        // Test shot validation would go here
        // This would require setting up mock game states
    }
}
