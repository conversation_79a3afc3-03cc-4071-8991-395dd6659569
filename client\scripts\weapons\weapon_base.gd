# Galaxy Guns 3D - Base Weapon Class
# Abstract weapon class supporting both bullet and hitscan weapons
# JSON-driven configuration for easy balancing and modding

class_name WeaponBase
extends Node3D

## Signals for weapon events
signal weapon_fired(weapon: WeaponBase)
signal weapon_reloaded(weapon: WeaponBase)
signal ammo_changed(current_ammo: int, reserve_ammo: int)
signal weapon_empty()

## Weapon types for different firing mechanics
enum WeaponType {
	HITSCAN,    # Instant hit (rifles, pistols)
	PROJECTILE, # Physical bullets (rockets, grenades)
	BEAM,       # Continuous beam (laser weapons)
	MELEE       # Close combat weapons
}

## Firing modes
enum FiringMode {
	SEMI_AUTO,  # Single shot per trigger pull
	FULL_AUTO,  # Continuous fire while held
	BURST,      # Fixed burst of shots
	CHARGE      # Charge up before firing
}

## Weapon configuration (loaded from JSON)
var weapon_name: String = ""
var weapon_type: WeaponType = WeaponType.HITSCAN
var firing_mode: FiringMode = FiringMode.SEMI_AUTO

## Damage and range
var damage: float = 25.0
var damage_falloff_start: float = 50.0
var damage_falloff_end: float = 200.0
var max_range: float = 500.0

## Firing mechanics
var fire_rate: float = 600.0  # Rounds per minute
var burst_count: int = 3
var burst_delay: float = 0.1
var charge_time: float = 1.0

## Accuracy and recoil
var base_accuracy: float = 0.95  # 1.0 = perfect accuracy
var moving_accuracy_penalty: float = 0.3
var recoil_pattern: Array[Vector2] = []
var recoil_recovery_speed: float = 5.0

## Ammo system
var magazine_size: int = 30
var current_ammo: int = 30
var reserve_ammo: int = 120
var max_reserve_ammo: int = 240
var reload_time: float = 2.0

## Audio and visual effects
var muzzle_flash_scene: PackedScene
var shell_casing_scene: PackedScene
var fire_sound: AudioStream
var reload_sound: AudioStream
var empty_sound: AudioStream

## Node references
@onready var muzzle_point: Marker3D = $MuzzlePoint
@onready var shell_eject_point: Marker3D = $ShellEjectPoint
@onready var weapon_mesh: MeshInstance3D = $WeaponMesh
@onready var animation_player: AnimationPlayer = $AnimationPlayer
@onready var audio_player: AudioStreamPlayer3D = $AudioPlayer

## Internal state
var is_firing: bool = false
var is_reloading: bool = false
var is_aiming: bool = false
var last_fire_time: float = 0.0
var current_recoil_index: int = 0
var recoil_accumulation: Vector2 = Vector2.ZERO

## Timers
var fire_timer: Timer
var reload_timer: Timer
var burst_timer: Timer

## Player reference
var player: PlayerController

func _ready():
	# Create timers
	fire_timer = Timer.new()
	fire_timer.wait_time = 60.0 / fire_rate
	fire_timer.one_shot = true
	fire_timer.timeout.connect(_on_fire_timer_timeout)
	add_child(fire_timer)
	
	reload_timer = Timer.new()
	reload_timer.wait_time = reload_time
	reload_timer.one_shot = true
	reload_timer.timeout.connect(_on_reload_complete)
	add_child(reload_timer)
	
	burst_timer = Timer.new()
	burst_timer.wait_time = burst_delay
	burst_timer.one_shot = true
	burst_timer.timeout.connect(_on_burst_timer_timeout)
	add_child(burst_timer)
	
	# Get player reference - weapon is child of WeaponHolder -> Camera3D -> CameraPivot -> Player
	player = get_node("../../../..") as PlayerController

func load_weapon_config(config: Dictionary):
	"""Load weapon configuration from JSON data"""
	weapon_name = config.get("name", "Unknown Weapon")
	weapon_type = config.get("type", WeaponType.HITSCAN)
	firing_mode = config.get("firing_mode", FiringMode.SEMI_AUTO)
	
	# Damage and range
	damage = config.get("damage", 25.0)
	damage_falloff_start = config.get("damage_falloff_start", 50.0)
	damage_falloff_end = config.get("damage_falloff_end", 200.0)
	max_range = config.get("max_range", 500.0)
	
	# Firing mechanics
	fire_rate = config.get("fire_rate", 600.0)
	burst_count = config.get("burst_count", 3)
	burst_delay = config.get("burst_delay", 0.1)
	charge_time = config.get("charge_time", 1.0)
	
	# Accuracy and recoil
	base_accuracy = config.get("base_accuracy", 0.95)
	moving_accuracy_penalty = config.get("moving_accuracy_penalty", 0.3)
	recoil_recovery_speed = config.get("recoil_recovery_speed", 5.0)
	
	# Load recoil pattern
	var recoil_data = config.get("recoil_pattern", [])
	recoil_pattern.clear()
	for point in recoil_data:
		recoil_pattern.append(Vector2(point[0], point[1]))
	
	# Ammo system
	magazine_size = config.get("magazine_size", 30)
	current_ammo = magazine_size
	reserve_ammo = config.get("reserve_ammo", 120)
	max_reserve_ammo = config.get("max_reserve_ammo", 240)
	reload_time = config.get("reload_time", 2.0)
	
	# Update timer
	fire_timer.wait_time = 60.0 / fire_rate
	reload_timer.wait_time = reload_time
	burst_timer.wait_time = burst_delay
	
	print("🔫 Loaded weapon config: %s" % weapon_name)

## Public API

func start_firing():
	"""Start firing the weapon"""
	if is_reloading or current_ammo <= 0:
		if current_ammo <= 0:
			_play_empty_sound()
		return
	
	is_firing = true
	
	match firing_mode:
		FiringMode.SEMI_AUTO:
			if fire_timer.is_stopped():
				_fire_shot()
		FiringMode.FULL_AUTO:
			if fire_timer.is_stopped():
				_fire_shot()
		FiringMode.BURST:
			if fire_timer.is_stopped():
				_start_burst()
		FiringMode.CHARGE:
			_start_charge()

func stop_firing():
	"""Stop firing the weapon"""
	is_firing = false

func reload():
	"""Reload the weapon"""
	if is_reloading or current_ammo >= magazine_size or reserve_ammo <= 0:
		return
	
	is_reloading = true
	_play_reload_sound()
	
	if animation_player and animation_player.has_animation("reload"):
		animation_player.play("reload")
	
	reload_timer.start()

func can_fire() -> bool:
	"""Check if weapon can fire"""
	return not is_reloading and current_ammo > 0 and fire_timer.is_stopped()

func get_current_accuracy() -> float:
	"""Calculate current accuracy based on movement and recoil"""
	var accuracy = base_accuracy
	
	# Apply movement penalty
	if player and player.velocity.length() > 0.1:
		accuracy -= moving_accuracy_penalty
	
	# Apply recoil penalty
	var recoil_penalty = recoil_accumulation.length() * 0.1
	accuracy -= recoil_penalty
	
	return clamp(accuracy, 0.1, 1.0)

func get_damage_at_distance(distance: float) -> float:
	"""Calculate damage with falloff"""
	if distance <= damage_falloff_start:
		return damage
	elif distance >= damage_falloff_end:
		return damage * 0.3  # Minimum 30% damage
	else:
		var falloff_factor = (distance - damage_falloff_start) / (damage_falloff_end - damage_falloff_start)
		return lerp(damage, damage * 0.3, falloff_factor)

## Private methods

func _fire_shot():
	"""Fire a single shot"""
	if current_ammo <= 0:
		weapon_empty.emit()
		return
	
	current_ammo -= 1
	last_fire_time = Time.get_time_dict_from_system()["unix"]
	
	# Apply recoil
	_apply_recoil()
	
	# Perform actual firing based on weapon type
	match weapon_type:
		WeaponType.HITSCAN:
			_fire_hitscan()
		WeaponType.PROJECTILE:
			_fire_projectile()
		WeaponType.BEAM:
			_fire_beam()
		WeaponType.MELEE:
			_fire_melee()
	
	# Play effects
	_play_fire_effects()
	
	# Start fire rate timer
	fire_timer.start()
	
	# Emit signals
	weapon_fired.emit(self)
	ammo_changed.emit(current_ammo, reserve_ammo)

func _fire_hitscan():
	"""Fire hitscan weapon (instant hit)"""
	var space_state = get_world_3d().direct_space_state
	var from = muzzle_point.global_position
	var accuracy = get_current_accuracy()
	
	# Calculate spread based on accuracy
	var spread_angle = (1.0 - accuracy) * 5.0  # Max 5 degrees spread
	var spread_x = randf_range(-spread_angle, spread_angle)
	var spread_y = randf_range(-spread_angle, spread_angle)
	
	# Get aim direction with spread
	var aim_direction = player.get_aim_direction()
	var spread_rotation = Basis.from_euler(Vector3(deg_to_rad(spread_y), deg_to_rad(spread_x), 0))
	var final_direction = spread_rotation * aim_direction
	
	var to = from + final_direction * max_range
	
	# Perform raycast
	var query = PhysicsRayQueryParameters3D.create(from, to)
	query.collision_mask = 1 | 4  # World and enemies
	query.exclude = [player]
	
	var result = space_state.intersect_ray(query)
	
	if result:
		var hit_point = result.position
		var hit_normal = result.normal
		var hit_object = result.collider
		
		# Calculate damage with distance falloff
		var distance = from.distance_to(hit_point)
		var final_damage = get_damage_at_distance(distance)
		
		# Apply damage if target can take damage
		if hit_object.has_method("take_damage"):
			hit_object.take_damage(final_damage, self)
		
		# Create impact effect
		_create_impact_effect(hit_point, hit_normal)
		
		print("🎯 Hit %s for %d damage at %dm" % [hit_object.name, final_damage, distance])

func _fire_projectile():
	"""Fire projectile weapon (physical bullet)"""
	# This would spawn a bullet projectile
	print("🚀 Projectile fired (not implemented)")

func _fire_beam():
	"""Fire beam weapon (continuous laser)"""
	# This would create a continuous beam effect
	print("⚡ Beam fired (not implemented)")

func _fire_melee():
	"""Fire melee weapon (close combat)"""
	# This would perform melee attack
	print("⚔️ Melee attack (not implemented)")

func _apply_recoil():
	"""Apply recoil pattern to weapon"""
	if recoil_pattern.size() > 0:
		var recoil_vector = recoil_pattern[current_recoil_index % recoil_pattern.size()]
		recoil_accumulation += recoil_vector
		current_recoil_index += 1
		
		# Apply recoil to camera (would need camera reference)
		if player:
			# This would apply recoil to player camera
			pass

func _play_fire_effects():
	"""Play visual and audio effects for firing"""
	# Play fire sound
	if fire_sound and audio_player:
		audio_player.stream = fire_sound
		audio_player.play()
	
	# Create muzzle flash
	if muzzle_flash_scene and muzzle_point:
		var muzzle_flash = muzzle_flash_scene.instantiate()
		muzzle_point.add_child(muzzle_flash)
	
	# Eject shell casing
	if shell_casing_scene and shell_eject_point:
		var shell = shell_casing_scene.instantiate()
		get_tree().current_scene.add_child(shell)
		shell.global_position = shell_eject_point.global_position
		# Add random velocity to shell
		if shell.has_method("set_velocity"):
			var eject_velocity = Vector3(randf_range(-2, 2), randf_range(1, 3), randf_range(-1, 1))
			shell.set_velocity(eject_velocity)
	
	# Play fire animation
	if animation_player and animation_player.has_animation("fire"):
		animation_player.play("fire")

func _create_impact_effect(position: Vector3, normal: Vector3):
	"""Create impact effect at hit location"""
	# This would spawn impact particles, decals, etc.
	print("💥 Impact effect at %s" % position)

func _play_empty_sound():
	"""Play empty weapon sound"""
	if empty_sound and audio_player:
		audio_player.stream = empty_sound
		audio_player.play()

func _play_reload_sound():
	"""Play reload sound"""
	if reload_sound and audio_player:
		audio_player.stream = reload_sound
		audio_player.play()

func _start_burst():
	"""Start burst fire sequence"""
	var shots_fired = 0
	_fire_burst_shot(shots_fired)

func _fire_burst_shot(shot_number: int):
	"""Fire a shot in burst sequence"""
	_fire_shot()
	
	if shot_number < burst_count - 1:
		burst_timer.start()
		burst_timer.timeout.connect(_fire_burst_shot.bind(shot_number + 1), CONNECT_ONE_SHOT)

func _start_charge():
	"""Start charge sequence"""
	# This would implement charge-up mechanics
	print("⚡ Charging weapon...")

## Timer callbacks

func _on_fire_timer_timeout():
	"""Handle fire rate timer timeout"""
	if is_firing and firing_mode == FiringMode.FULL_AUTO:
		if current_ammo > 0:
			_fire_shot()
		else:
			stop_firing()

func _on_reload_complete():
	"""Handle reload completion"""
	var ammo_needed = magazine_size - current_ammo
	var ammo_to_reload = min(ammo_needed, reserve_ammo)
	
	current_ammo += ammo_to_reload
	reserve_ammo -= ammo_to_reload
	
	is_reloading = false
	current_recoil_index = 0  # Reset recoil pattern
	recoil_accumulation = Vector2.ZERO
	
	weapon_reloaded.emit(self)
	ammo_changed.emit(current_ammo, reserve_ammo)
	
	print("🔄 Reload complete: %d/%d" % [current_ammo, reserve_ammo])

func _on_burst_timer_timeout():
	"""Handle burst timer timeout"""
	# Handled in _fire_burst_shot
	pass

## Update loop

func _process(delta):
	# Recover from recoil over time
	if recoil_accumulation.length() > 0:
		recoil_accumulation = recoil_accumulation.move_toward(Vector2.ZERO, recoil_recovery_speed * delta)

## Debug methods

func get_weapon_stats() -> Dictionary:
	"""Get current weapon statistics"""
	return {
		"name": weapon_name,
		"type": WeaponType.keys()[weapon_type],
		"firing_mode": FiringMode.keys()[firing_mode],
		"current_ammo": current_ammo,
		"reserve_ammo": reserve_ammo,
		"is_firing": is_firing,
		"is_reloading": is_reloading,
		"accuracy": get_current_accuracy(),
		"recoil": recoil_accumulation
	}
