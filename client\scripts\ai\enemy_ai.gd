# Galaxy Guns 3D - Enemy AI System
# A* pathfinding with squad behaviors and state machine
# Optimized for 60fps with multiple AI agents

class_name EnemyAI
extends CharacterBody3D

## Signals for AI events
signal enemy_died(enemy: EnemyAI)
signal enemy_spotted_player(enemy: EnemyAI, player: Node3D)
signal enemy_lost_player(enemy: EnemyAI)
signal squad_member_died(enemy: EnemyAI)

## AI States for behavior management
enum AIState {
	IDLE,           # Standing around, basic patrol
	PATROL,         # Following patrol route
	ALERT,          # Heard something, investigating
	COMBAT,         # Actively fighting player
	SEARCH,         # Lost player, searching last known position
	RETREAT,        # Low health, falling back
	DEAD            # Eliminated
}

## Combat behaviors
enum CombatBehavior {
	AGGRESSIVE,     # Rush player, high aggression
	DEFENSIVE,      # Take cover, defensive positioning
	SUPPORT,        # Stay back, provide covering fire
	FLANKING        # Attempt to flank player position
}

## Enemy configuration
@export_group("AI Settings")
@export var ai_type: String = "soldier"
@export var combat_behavior: CombatBehavior = CombatBehavior.AGGRESSIVE
@export var detection_range: float = 50.0
@export var attack_range: float = 30.0
@export var hearing_range: float = 25.0
@export var field_of_view: float = 90.0

@export_group("Movement")
@export var move_speed: float = 3.0
@export var run_speed: float = 6.0
@export var rotation_speed: float = 5.0
@export var stopping_distance: float = 2.0

@export_group("Combat")
@export var max_health: int = 80
@export var current_health: int = 80
@export var damage: float = 20.0
@export var fire_rate: float = 2.0
@export var accuracy: float = 0.7

@export_group("Squad Behavior")
@export var squad_id: String = ""
@export var is_squad_leader: bool = false
@export var max_squad_size: int = 4

## Node references
@onready var navigation_agent: NavigationAgent3D = $NavigationAgent3D
@onready var vision_raycast: RayCast3D = $VisionRaycast
@onready var weapon_point: Marker3D = $WeaponPoint
@onready var animation_player: AnimationPlayer = $AnimationPlayer
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D
@onready var collision_shape: CollisionShape3D = $CollisionShape3D

## AI State
var current_state: AIState = AIState.IDLE
var previous_state: AIState = AIState.IDLE
var state_timer: float = 0.0

## Target tracking
var player_target: Node3D = null
var last_known_player_position: Vector3 = Vector3.ZERO
var time_since_player_seen: float = 0.0
var can_see_player: bool = false

## Pathfinding
var current_path: PackedVector3Array = []
var path_index: int = 0
var is_path_valid: bool = false
var recalculate_path_timer: float = 0.0

## Combat
var last_shot_time: float = 0.0
var is_firing: bool = false
var cover_position: Vector3 = Vector3.ZERO
var has_cover: bool = false

## Squad management
var squad_members: Array[EnemyAI] = []
var squad_leader: EnemyAI = null
var squad_formation_offset: Vector3 = Vector3.ZERO

## Patrol system
var patrol_points: Array[Vector3] = []
var current_patrol_index: int = 0
var patrol_wait_time: float = 2.0
var patrol_timer: float = 0.0

## Performance optimization
var update_frequency: float = 0.1  # Update AI every 0.1 seconds
var update_timer: float = 0.0
var is_active: bool = true  # Disable AI when far from player

func _ready():
	# Initialize AI systems
	_setup_navigation()
	_setup_vision()
	_initialize_squad()
	_load_ai_config()
	
	# Start in idle state
	_change_state(AIState.IDLE)
	
	print("🤖 Enemy AI initialized: %s" % ai_type)

func _setup_navigation():
	"""Configure navigation agent for pathfinding"""
	navigation_agent.target_desired_distance = stopping_distance
	navigation_agent.path_desired_distance = 1.0
	navigation_agent.navigation_finished.connect(_on_navigation_finished)
	navigation_agent.velocity_computed.connect(_on_velocity_computed)

func _setup_vision():
	"""Configure vision and detection systems"""
	vision_raycast.target_position = Vector3(0, 0, -detection_range)
	vision_raycast.collision_mask = 2  # Player layer

func _initialize_squad():
	"""Initialize squad behavior system"""
	if squad_id != "":
		_join_squad()

func _load_ai_config():
	"""Load AI configuration from JSON"""
	var config_path = "res://shared/data/ai/" + ai_type + ".json"
	var file = FileAccess.open(config_path, FileAccess.READ)
	
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			_apply_ai_config(json.data)

func _apply_ai_config(config: Dictionary):
	"""Apply AI configuration from JSON data"""
	max_health = config.get("max_health", max_health)
	current_health = max_health
	damage = config.get("damage", damage)
	fire_rate = config.get("fire_rate", fire_rate)
	accuracy = config.get("accuracy", accuracy)
	detection_range = config.get("detection_range", detection_range)
	attack_range = config.get("attack_range", attack_range)
	move_speed = config.get("move_speed", move_speed)
	run_speed = config.get("run_speed", run_speed)

func _physics_process(delta):
	# Performance optimization - update AI less frequently
	update_timer += delta
	if update_timer < update_frequency:
		return
	update_timer = 0.0
	
	# Skip AI processing if too far from player
	if not _is_player_nearby():
		is_active = false
		return
	
	is_active = true
	
	# Update AI state machine
	_update_ai_state(delta * (1.0 / update_frequency))
	
	# Update vision and detection
	_update_detection()
	
	# Update pathfinding
	_update_pathfinding(delta * (1.0 / update_frequency))
	
	# Apply movement
	move_and_slide()

func _update_ai_state(delta):
	"""Update AI state machine"""
	state_timer += delta
	
	match current_state:
		AIState.IDLE:
			_state_idle(delta)
		AIState.PATROL:
			_state_patrol(delta)
		AIState.ALERT:
			_state_alert(delta)
		AIState.COMBAT:
			_state_combat(delta)
		AIState.SEARCH:
			_state_search(delta)
		AIState.RETREAT:
			_state_retreat(delta)
		AIState.DEAD:
			_state_dead(delta)

func _state_idle(delta):
	"""Idle state - basic standing around"""
	# Look for player
	if can_see_player:
		_change_state(AIState.COMBAT)
		return
	
	# Start patrol if patrol points exist
	if patrol_points.size() > 0 and state_timer > 3.0:
		_change_state(AIState.PATROL)

func _state_patrol(delta):
	"""Patrol state - following patrol route"""
	if can_see_player:
		_change_state(AIState.COMBAT)
		return
	
	# Move to current patrol point
	if patrol_points.size() > 0:
		var target_point = patrol_points[current_patrol_index]
		_set_movement_target(target_point)
		
		# Check if reached patrol point
		if global_position.distance_to(target_point) < stopping_distance:
			patrol_timer += delta
			if patrol_timer >= patrol_wait_time:
				current_patrol_index = (current_patrol_index + 1) % patrol_points.size()
				patrol_timer = 0.0

func _state_alert(delta):
	"""Alert state - investigating disturbance"""
	if can_see_player:
		_change_state(AIState.COMBAT)
		return
	
	# Move to last known position
	_set_movement_target(last_known_player_position)
	
	# Return to patrol after timeout
	if state_timer > 10.0:
		_change_state(AIState.PATROL if patrol_points.size() > 0 else AIState.IDLE)

func _state_combat(delta):
	"""Combat state - actively fighting player"""
	if not can_see_player:
		time_since_player_seen += delta
		if time_since_player_seen > 5.0:
			_change_state(AIState.SEARCH)
			return
	else:
		time_since_player_seen = 0.0
		last_known_player_position = player_target.global_position
	
	# Check for retreat condition
	if current_health < max_health * 0.3:
		_change_state(AIState.RETREAT)
		return
	
	# Combat behavior
	match combat_behavior:
		CombatBehavior.AGGRESSIVE:
			_combat_aggressive(delta)
		CombatBehavior.DEFENSIVE:
			_combat_defensive(delta)
		CombatBehavior.SUPPORT:
			_combat_support(delta)
		CombatBehavior.FLANKING:
			_combat_flanking(delta)

func _state_search(delta):
	"""Search state - looking for lost player"""
	# Move to last known position
	_set_movement_target(last_known_player_position)
	
	if can_see_player:
		_change_state(AIState.COMBAT)
		return
	
	# Give up search after timeout
	if state_timer > 15.0:
		_change_state(AIState.PATROL if patrol_points.size() > 0 else AIState.IDLE)

func _state_retreat(delta):
	"""Retreat state - falling back to cover"""
	# Find cover position away from player
	if not has_cover:
		cover_position = _find_cover_position()
		has_cover = true
	
	_set_movement_target(cover_position)
	
	# Return to combat if health recovered or player gets too close
	if current_health > max_health * 0.6 or (player_target and global_position.distance_to(player_target.global_position) < 10.0):
		_change_state(AIState.COMBAT)

func _state_dead(delta):
	"""Dead state - enemy eliminated"""
	# Disable physics and collision
	set_physics_process(false)
	collision_shape.disabled = true

## Combat behaviors

func _combat_aggressive(delta):
	"""Aggressive combat - rush the player"""
	if player_target:
		var distance_to_player = global_position.distance_to(player_target.global_position)
		
		if distance_to_player > attack_range:
			# Move closer to player
			_set_movement_target(player_target.global_position)
		else:
			# Stop and shoot
			_stop_movement()
			_aim_at_target(player_target.global_position)
			_try_shoot()

func _combat_defensive(delta):
	"""Defensive combat - take cover and shoot"""
	if not has_cover:
		cover_position = _find_cover_position()
		has_cover = true
		_set_movement_target(cover_position)
	else:
		_stop_movement()
		if player_target:
			_aim_at_target(player_target.global_position)
			_try_shoot()

func _combat_support(delta):
	"""Support combat - stay back and provide covering fire"""
	if player_target:
		var distance_to_player = global_position.distance_to(player_target.global_position)
		
		if distance_to_player < attack_range * 1.5:
			# Move away to maintain distance
			var retreat_direction = (global_position - player_target.global_position).normalized()
			_set_movement_target(global_position + retreat_direction * 10.0)
		else:
			_stop_movement()
			_aim_at_target(player_target.global_position)
			_try_shoot()

func _combat_flanking(delta):
	"""Flanking combat - attempt to flank player position"""
	if player_target:
		# Calculate flanking position
		var to_player = (player_target.global_position - global_position).normalized()
		var flank_direction = Vector3(-to_player.z, 0, to_player.x)  # Perpendicular
		var flank_position = player_target.global_position + flank_direction * 15.0
		
		_set_movement_target(flank_position)
		_aim_at_target(player_target.global_position)
		_try_shoot()

## Utility methods

func _update_detection():
	"""Update player detection and vision"""
	player_target = _find_player()
	
	if player_target:
		can_see_player = _can_see_target(player_target)
		
		if can_see_player:
			last_known_player_position = player_target.global_position
			
			# Alert squad members
			if squad_members.size() > 0:
				_alert_squad()

func _find_player() -> Node3D:
	"""Find player in detection range"""
	# Simple approach - find player in group
	var player = get_tree().get_first_node_in_group("player")
	if player and global_position.distance_to(player.global_position) <= detection_range:
		return player
	return null

func _can_see_target(target: Node3D) -> bool:
	"""Check if target is visible with line of sight"""
	if not target:
		return false
	
	var to_target = target.global_position - global_position
	var distance = to_target.length()
	
	# Check distance
	if distance > detection_range:
		return false
	
	# Check field of view
	var forward = -global_transform.basis.z
	var angle = forward.angle_to(to_target.normalized())
	if angle > deg_to_rad(field_of_view / 2.0):
		return false
	
	# Check line of sight
	var space_state = get_world_3d().direct_space_state
	var query = PhysicsRayQueryParameters3D.create(
		global_position + Vector3(0, 1.5, 0),  # Eye level
		target.global_position + Vector3(0, 1.0, 0),  # Target center
		1 | 2  # World and player layers
	)
	
	var result = space_state.intersect_ray(query)
	
	if result and result.collider == target:
		return true
	
	return false

func _set_movement_target(target_position: Vector3):
	"""Set navigation target"""
	navigation_agent.target_position = target_position

func _stop_movement():
	"""Stop current movement"""
	velocity = Vector3.ZERO

func _aim_at_target(target_position: Vector3):
	"""Rotate to face target"""
	var to_target = (target_position - global_position).normalized()
	var target_rotation = atan2(to_target.x, to_target.z)
	rotation.y = lerp_angle(rotation.y, target_rotation, rotation_speed * get_physics_process_delta_time())

func _try_shoot():
	"""Attempt to shoot at player"""
	var current_time = Time.get_time_dict_from_system()["unix"]
	
	if current_time - last_shot_time >= 1.0 / fire_rate:
		_shoot_at_player()
		last_shot_time = current_time

func _shoot_at_player():
	"""Fire weapon at player"""
	if not player_target:
		return
	
	# Calculate shot accuracy
	var hit_chance = accuracy
	if randf() <= hit_chance:
		# Hit player
		if player_target.has_method("take_damage"):
			player_target.take_damage(damage, self)
		print("🎯 Enemy hit player for %d damage" % damage)
	else:
		print("❌ Enemy missed shot")
	
	# Play shoot effects
	_play_shoot_effects()

func _play_shoot_effects():
	"""Play visual and audio effects for shooting"""
	# This would play muzzle flash, sound, etc.
	pass

func _find_cover_position() -> Vector3:
	"""Find a cover position away from player"""
	if not player_target:
		return global_position
	
	# Simple cover finding - move away from player
	var away_from_player = (global_position - player_target.global_position).normalized()
	return global_position + away_from_player * 15.0

func _change_state(new_state: AIState):
	"""Change AI state with cleanup"""
	if new_state == current_state:
		return
	
	previous_state = current_state
	current_state = new_state
	state_timer = 0.0
	
	# State-specific cleanup
	match new_state:
		AIState.COMBAT:
			enemy_spotted_player.emit(self, player_target)
		AIState.SEARCH:
			enemy_lost_player.emit(self)
		AIState.DEAD:
			_handle_death()

func _handle_death():
	"""Handle enemy death"""
	enemy_died.emit(self)
	squad_member_died.emit(self)
	
	# Remove from squad
	if squad_leader:
		squad_leader.squad_members.erase(self)
	
	# Play death animation
	if animation_player and animation_player.has_animation("death"):
		animation_player.play("death")

## Squad behavior methods

func _join_squad():
	"""Join or create squad"""
	# Find existing squad members
	var enemies = get_tree().get_nodes_in_group("enemies")
	for enemy in enemies:
		if enemy != self and enemy.squad_id == squad_id:
			if enemy.is_squad_leader:
				squad_leader = enemy
				enemy.squad_members.append(self)
			else:
				squad_members.append(enemy)
	
	# Become leader if no leader exists
	if not squad_leader and squad_members.size() == 0:
		is_squad_leader = true

func _alert_squad():
	"""Alert squad members to player presence"""
	for member in squad_members:
		if member.current_state != AIState.COMBAT:
			member._change_state(AIState.ALERT)
			member.last_known_player_position = last_known_player_position

## Navigation callbacks

func _on_navigation_finished():
	"""Handle navigation completion"""
	is_path_valid = false

func _on_velocity_computed(safe_velocity: Vector3):
	"""Handle computed velocity from navigation"""
	velocity = safe_velocity

## Public API

func take_damage(damage_amount: int, source: Node = null):
	"""Apply damage to enemy"""
	current_health = max(0, current_health - damage_amount)
	
	if current_health <= 0:
		_change_state(AIState.DEAD)
	elif current_state == AIState.IDLE or current_state == AIState.PATROL:
		_change_state(AIState.ALERT)
		if source:
			last_known_player_position = source.global_position

func set_patrol_points(points: Array[Vector3]):
	"""Set patrol route"""
	patrol_points = points
	current_patrol_index = 0

func _is_player_nearby() -> bool:
	"""Check if player is nearby for performance optimization"""
	if not player_target:
		player_target = _find_player()
	
	if player_target:
		return global_position.distance_to(player_target.global_position) < detection_range * 2.0
	
	return false

func _update_pathfinding(delta):
	"""Update pathfinding system"""
	recalculate_path_timer += delta

	# Recalculate path periodically
	if recalculate_path_timer > 0.5:
		recalculate_path_timer = 0.0
		if navigation_agent.target_position != Vector3.ZERO:
			# Path will be automatically recalculated by NavigationAgent3D
			pass

## Debug methods

func get_ai_debug_info() -> Dictionary:
	"""Get AI debug information"""
	return {
		"state": AIState.keys()[current_state],
		"health": "%d/%d" % [current_health, max_health],
		"can_see_player": can_see_player,
		"distance_to_player": global_position.distance_to(player_target.global_position) if player_target else -1,
		"squad_size": squad_members.size(),
		"is_squad_leader": is_squad_leader,
		"is_active": is_active
	}
