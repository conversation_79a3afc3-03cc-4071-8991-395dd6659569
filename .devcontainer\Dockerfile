# Galaxy Guns 3D Development Container
# Based on Ubuntu 22.04 with Godot 4.3 and development tools

FROM ubuntu:22.04

# Build arguments
ARG GODOT_VERSION="4.3-stable"
ARG USERNAME=godot
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Avoid warnings by switching to noninteractive
ENV DEBIAN_FRONTEND=noninteractive

# Configure apt and install packages
RUN apt-get update \
    && apt-get -y install --no-install-recommends apt-utils dialog 2>&1 \
    && apt-get -y install \
        # Common utilities
        git \
        curl \
        wget \
        unzip \
        zip \
        ca-certificates \
        gnupg \
        lsb-release \
        software-properties-common \
        # Build tools
        build-essential \
        cmake \
        pkg-config \
        # Graphics and audio libraries
        libx11-dev \
        libxcursor-dev \
        libxinerama-dev \
        libgl1-mesa-dev \
        libglu1-mesa-dev \
        libasound2-dev \
        libpulse-dev \
        # Python and development tools
        python3 \
        python3-pip \
        python3-venv \
        # Ruby for Fastlane
        ruby \
        ruby-dev \
        # Node.js for web tools
        nodejs \
        npm \
        # Additional tools
        htop \
        tree \
        jq \
        vim \
        nano \
        # X11 and display
        xvfb \
        x11-utils \
        x11-xserver-utils \
    && apt-get autoremove -y \
    && apt-get clean -y \
    && rm -rf /var/lib/apt/lists/*

# Install Docker CLI
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli \
    && apt-get clean -y \
    && rm -rf /var/lib/apt/lists/*

# Create the user
RUN groupadd --gid $USER_GID $USERNAME \
    && useradd --uid $USER_UID --gid $USER_GID -m $USERNAME \
    && apt-get update \
    && apt-get install -y sudo \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME \
    && apt-get clean -y \
    && rm -rf /var/lib/apt/lists/*

# Add user to docker group
RUN usermod -aG docker $USERNAME

# Switch to user
USER $USERNAME
WORKDIR /home/<USER>

# Install Godot
RUN mkdir -p /home/<USER>/tools \
    && cd /home/<USER>/tools \
    && wget -q https://github.com/godotengine/godot/releases/download/${GODOT_VERSION}/Godot_v${GODOT_VERSION}_linux.x86_64.zip \
    && unzip Godot_v${GODOT_VERSION}_linux.x86_64.zip \
    && rm Godot_v${GODOT_VERSION}_linux.x86_64.zip \
    && mv Godot_v${GODOT_VERSION}_linux.x86_64 godot \
    && chmod +x godot

# Install Godot export templates
RUN cd /home/<USER>/tools \
    && wget -q https://github.com/godotengine/godot/releases/download/${GODOT_VERSION}/Godot_v${GODOT_VERSION}_export_templates.tpz \
    && mkdir -p /home/<USER>/.local/share/godot/export_templates/${GODOT_VERSION} \
    && unzip Godot_v${GODOT_VERSION}_export_templates.tpz -d /home/<USER>/.local/share/godot/export_templates/${GODOT_VERSION} \
    && rm Godot_v${GODOT_VERSION}_export_templates.tpz

# Create symlink for global access
USER root
RUN ln -s /home/<USER>/tools/godot /usr/local/bin/godot
USER $USERNAME

# Install Fastlane
RUN gem install fastlane --user-install \
    && echo 'export PATH="$HOME/.gem/ruby/$(ruby -e "puts RUBY_VERSION")/bin:$PATH"' >> /home/<USER>/.bashrc

# Install Python packages
RUN python3 -m pip install --user \
    requests \
    pillow \
    pyyaml \
    jinja2

# Install Node.js packages globally
RUN npm install -g \
    @aseprite/cli \
    imagemin-cli \
    svgo

# Setup Git configuration
RUN git config --global init.defaultBranch main \
    && git config --global pull.rebase false \
    && git config --global user.name "Galaxy Guns Dev" \
    && git config --global user.email "<EMAIL>"

# Create workspace directory
RUN mkdir -p /workspace

# Setup environment variables
ENV PATH="/home/<USER>/.gem/ruby/$(ruby -e "puts RUBY_VERSION")/bin:/home/<USER>/tools:$PATH"
ENV GODOT_PATH="/home/<USER>/tools/godot"
ENV DISPLAY=:99

# Create startup script
RUN echo '#!/bin/bash\n\
# Start virtual display for headless Godot\n\
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &\n\
\n\
# Print environment info\n\
echo "🌌 Galaxy Guns 3D Development Environment"\n\
echo "========================================="\n\
echo "Godot version: $(godot --version)"\n\
echo "Python version: $(python3 --version)"\n\
echo "Ruby version: $(ruby --version)"\n\
echo "Node.js version: $(node --version)"\n\
echo "Git version: $(git --version)"\n\
echo "Docker version: $(docker --version)"\n\
echo ""\n\
echo "Ready for development! 🚀"\n\
echo ""\n\
' > /home/<USER>/startup.sh \
    && chmod +x /home/<USER>/startup.sh

# Set the default command
CMD ["/home/<USER>/startup.sh"]

# Switch back to dialog for any ad-hoc use of apt-get
ENV DEBIAN_FRONTEND=dialog
