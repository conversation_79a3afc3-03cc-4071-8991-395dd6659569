; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="Galaxy Guns 3D"
config/description="Next-generation multiplayer pixel FPS for iOS"
config/version="0.1.0"
run/main_scene="res://scenes/main_menu.tscn"
config/features=PackedStringArray("4.3", "Mobile")
boot_splash/bg_color=Color(0.05, 0.05, 0.1, 1)
boot_splash/image="res://assets/ui/splash_logo.png"
boot_splash/fullsize=false
config/icon="res://assets/ui/app_icon.png"

[audio]

buses/default_bus_layout="res://assets/audio/default_bus_layout.tres"
driver/enable_input=true
driver/mix_rate=44100
driver/output_latency=15

[debug]

gdscript/warnings/enable=true
gdscript/warnings/treat_warnings_as_errors=false
gdscript/warnings/exclude_addons=true
settings/crash_handler/message="Galaxy Guns 3D has encountered an error. Please report this issue."

[display]

window/size/viewport_width=1080
window/size/viewport_height=1920
window/size/mode=3
window/size/resizable=false
window/stretch/mode="canvas_items"
window/stretch/aspect="expand"
window/handheld/orientation=1
window/ios/allow_high_refresh_rate=true
window/ios/hide_home_indicator=true

[gui]

common/drop_mouse_on_gui_input_disabled=true
theme/custom="res://assets/ui/main_theme.tres"
theme/custom_font="res://assets/ui/fonts/primary_ui.ttf"

[input]

move_forward={
"deadzone": 0.2,
"events": []
}
move_backward={
"deadzone": 0.2,
"events": []
}
move_left={
"deadzone": 0.2,
"events": []
}
move_right={
"deadzone": 0.2,
"events": []
}
look_up={
"deadzone": 0.1,
"events": []
}
look_down={
"deadzone": 0.1,
"events": []
}
look_left={
"deadzone": 0.1,
"events": []
}
look_right={
"deadzone": 0.1,
"events": []
}
fire={
"deadzone": 0.0,
"events": []
}
reload={
"deadzone": 0.0,
"events": []
}
jump={
"deadzone": 0.0,
"events": []
}
crouch={
"deadzone": 0.0,
"events": []
}
sprint={
"deadzone": 0.0,
"events": []
}
menu={
"deadzone": 0.0,
"events": []
}

[input_devices]

pointing/emulate_touch_from_mouse=true
pointing/emulate_mouse_from_touch=false

[layer_names]

2d_render/layer_1="Background"
2d_render/layer_2="Environment"
2d_render/layer_3="Props"
2d_render/layer_4="Characters"
2d_render/layer_5="Weapons"
2d_render/layer_6="Effects"
2d_render/layer_7="UI"
2d_physics/layer_1="World"
2d_physics/layer_2="Players"
2d_physics/layer_3="Enemies"
2d_physics/layer_4="Projectiles"
2d_physics/layer_5="Pickups"
2d_physics/layer_6="Triggers"

[network]

limits/debugger/remote_port=6007
limits/debugger/max_chars_per_second=32768
limits/debugger/max_queued_messages=2048
limits/debugger/max_errors_per_second=400
limits/debugger/max_warnings_per_second=400

[physics]

common/enable_pause_aware_picking=true
2d/default_gravity=980
2d/default_gravity_vector=Vector2(0, 1)
2d/default_linear_damp=0.1
2d/default_angular_damp=1.0

[rendering]

renderer/rendering_method="mobile"
renderer/rendering_method.mobile="forward_plus"
textures/canvas_textures/default_texture_filter=0
textures/decals/filter=0
textures/light_projectors/filter=0
anti_aliasing/quality/msaa_2d=1
anti_aliasing/quality/msaa_3d=1
anti_aliasing/quality/screen_space_aa=1
anti_aliasing/quality/use_taa=false
occlusion_culling/use_occlusion_culling=true
mesh_lod/lod_change/threshold_pixels=1.0
global_illumination/gi/use_half_resolution=true
environment/defaults/default_clear_color=Color(0.05, 0.05, 0.1, 1)
2d/use_pixel_snap=true
2d/snap_2d_transforms_to_pixel=true
2d/snap_2d_vertices_to_pixel=true

[xr]

openxr/enabled=false
shaders/enabled=false
