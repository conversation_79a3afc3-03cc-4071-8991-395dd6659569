# Galaxy Guns 3D - Game UI
# Mobile touch controls and HUD

extends Control

## Signals for input events
signal movement_input(input: Vector2)
signal look_input(input: Vector2)
signal fire_pressed()
signal fire_released()
signal reload_pressed()
signal jump_pressed()
signal crouch_pressed()

## Node references
@onready var movement_joystick = $TouchControls/MovementJoystick
@onready var joystick_base = $TouchControls/MovementJoystick/JoystickBase
@onready var joystick_knob = $TouchControls/MovementJoystick/JoystickKnob
@onready var look_area = $TouchControls/LookArea
@onready var health_bar = $HUD/HealthBar
@onready var health_label = $HUD/HealthLabel
@onready var ammo_label = $HUD/AmmoLabel

## Touch input state
var joystick_center: Vector2
var joystick_radius: float
var is_joystick_pressed: bool = false
var joystick_touch_index: int = -1

var is_look_pressed: bool = false
var look_touch_index: int = -1
var last_look_position: Vector2

func _ready():
	print("🎮 Game UI initialized")
	
	# Setup joystick
	joystick_center = joystick_base.size / 2
	joystick_radius = joystick_base.size.x / 2 - 25
	
	# Connect to player if available
	_connect_to_player()

func _connect_to_player():
	"""Connect UI signals to player"""
	var player = get_tree().get_first_node_in_group("player")
	if player:
		# Connect player signals to update UI
		if player.has_signal("health_changed"):
			player.health_changed.connect(_on_player_health_changed)
		if player.has_signal("ammo_changed"):
			player.ammo_changed.connect(_on_player_ammo_changed)
		print("🔗 UI connected to player")

func _input(event):
	if event is InputEventScreenTouch:
		_handle_touch_event(event)
	elif event is InputEventScreenDrag:
		_handle_drag_event(event)

func _handle_touch_event(event: InputEventScreenTouch):
	var touch_pos = event.position
	
	if event.pressed:
		# Check if touch is on joystick
		if _is_point_in_joystick(touch_pos):
			is_joystick_pressed = true
			joystick_touch_index = event.index
			_update_joystick(touch_pos)
		# Check if touch is in look area
		elif _is_point_in_look_area(touch_pos):
			is_look_pressed = true
			look_touch_index = event.index
			last_look_position = touch_pos
	else:
		# Release joystick
		if event.index == joystick_touch_index:
			is_joystick_pressed = false
			joystick_touch_index = -1
			_reset_joystick()
		# Release look
		elif event.index == look_touch_index:
			is_look_pressed = false
			look_touch_index = -1
			look_input.emit(Vector2.ZERO)

func _handle_drag_event(event: InputEventScreenDrag):
	# Handle joystick drag
	if event.index == joystick_touch_index and is_joystick_pressed:
		_update_joystick(event.position)
	# Handle look drag
	elif event.index == look_touch_index and is_look_pressed:
		var delta = event.position - last_look_position
		look_input.emit(delta * 0.01)  # Scale down for sensitivity
		last_look_position = event.position

func _is_point_in_joystick(point: Vector2) -> bool:
	var joystick_global_rect = Rect2(
		movement_joystick.global_position,
		movement_joystick.size
	)
	return joystick_global_rect.has_point(point)

func _is_point_in_look_area(point: Vector2) -> bool:
	var look_global_rect = Rect2(
		look_area.global_position,
		look_area.size
	)
	return look_global_rect.has_point(point)

func _update_joystick(touch_pos: Vector2):
	var local_pos = touch_pos - movement_joystick.global_position
	var offset = local_pos - joystick_center
	
	# Clamp to joystick radius
	if offset.length() > joystick_radius:
		offset = offset.normalized() * joystick_radius
	
	# Update knob position
	joystick_knob.position = joystick_center + offset - joystick_knob.size / 2
	
	# Emit movement input (normalized)
	var input_vector = offset / joystick_radius
	movement_input.emit(input_vector)

func _reset_joystick():
	joystick_knob.position = joystick_center - joystick_knob.size / 2
	movement_input.emit(Vector2.ZERO)

## Button event handlers

func _on_fire_button_down():
	fire_pressed.emit()

func _on_fire_button_up():
	fire_released.emit()

func _on_reload_button_pressed():
	reload_pressed.emit()

func _on_jump_button_pressed():
	jump_pressed.emit()

## Player event handlers

func _on_player_health_changed(current_health: int, max_health: int):
	health_bar.value = (float(current_health) / float(max_health)) * 100.0
	health_label.text = "Health: %d" % current_health

func _on_player_ammo_changed(current_ammo: int, reserve_ammo: int):
	ammo_label.text = "Ammo: %d/%d" % [current_ammo, reserve_ammo]
