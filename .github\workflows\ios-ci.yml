name: iOS CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  GODOT_VERSION: "4.3-stable"
  BUNDLE_ID: "com.orvyn.galaxyguns"
  APP_NAME: "Galaxy Guns 3D"

jobs:
  # ==========================================
  # GODOT BUILD AND EXPORT
  # ==========================================
  godot-build:
    name: Build Godot Project
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        lfs: true
    
    - name: Setup Godot
      uses: chickensoft-games/setup-godot@v1
      with:
        version: ${{ env.GODOT_VERSION }}
        use-dotnet: false
    
    - name: Verify Godot installation
      run: godot --version
    
    - name: Import Godot project
      run: |
        cd client
        godot --headless --import
    
    - name: Run Godot tests
      run: |
        cd client
        godot --headless --script res://tests/run_tests.gd
    
    - name: Export for iOS
      run: |
        cd client
        mkdir -p ../builds/ios
        godot --headless --export-release "iOS" ../builds/ios/GalaxyGuns.ipa
    
    - name: Upload iOS build artifact
      uses: actions/upload-artifact@v4
      with:
        name: ios-build
        path: builds/ios/GalaxyGuns.ipa
        retention-days: 7

  # ==========================================
  # IOS TESTING AND DEPLOYMENT
  # ==========================================
  ios-deploy:
    name: iOS Testing and Deployment
    runs-on: macos-latest
    needs: godot-build
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Download iOS build artifact
      uses: actions/download-artifact@v4
      with:
        name: ios-build
        path: builds/ios/
    
    - name: Setup Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: '3.2'
        bundler-cache: true
    
    - name: Install Fastlane
      run: |
        gem install fastlane
        fastlane --version
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: '15.4'
    
    - name: Install Apple certificates
      env:
        BUILD_CERTIFICATE_BASE64: ${{ secrets.BUILD_CERTIFICATE_BASE64 }}
        P12_PASSWORD: ${{ secrets.P12_PASSWORD }}
        BUILD_PROVISION_PROFILE_BASE64: ${{ secrets.BUILD_PROVISION_PROFILE_BASE64 }}
        KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
      run: |
        # Create variables
        CERTIFICATE_PATH=$RUNNER_TEMP/build_certificate.p12
        PP_PATH=$RUNNER_TEMP/build_pp.mobileprovision
        KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db
        
        # Import certificate and provisioning profile from secrets
        echo -n "$BUILD_CERTIFICATE_BASE64" | base64 --decode --output $CERTIFICATE_PATH
        echo -n "$BUILD_PROVISION_PROFILE_BASE64" | base64 --decode --output $PP_PATH
        
        # Create temporary keychain
        security create-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
        security set-keychain-settings -lut 21600 $KEYCHAIN_PATH
        security unlock-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
        
        # Import certificate to keychain
        security import $CERTIFICATE_PATH -P "$P12_PASSWORD" -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
        security list-keychain -d user -s $KEYCHAIN_PATH
        
        # Apply provisioning profile
        mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
        cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles
    
    - name: Run iOS tests
      run: |
        cd ci
        fastlane ios test
    
    - name: Deploy to TestFlight (develop branch)
      if: github.ref == 'refs/heads/develop'
      env:
        APP_STORE_CONNECT_API_KEY_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}
        APP_STORE_CONNECT_API_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_API_ISSUER_ID }}
        APP_STORE_CONNECT_API_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY }}
      run: |
        cd ci
        fastlane ios beta
    
    - name: Deploy to App Store (main branch)
      if: github.ref == 'refs/heads/main' && github.event_name == 'release'
      env:
        APP_STORE_CONNECT_API_KEY_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}
        APP_STORE_CONNECT_API_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_API_ISSUER_ID }}
        APP_STORE_CONNECT_API_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY }}
      run: |
        cd ci
        fastlane ios release

  # ==========================================
  # PERFORMANCE TESTING
  # ==========================================
  performance-test:
    name: Performance Testing
    runs-on: macos-latest
    needs: godot-build
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Download iOS build artifact
      uses: actions/download-artifact@v4
      with:
        name: ios-build
        path: builds/ios/
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: '15.4'
    
    - name: Run performance tests
      run: |
        cd ci
        fastlane ios performance_test
    
    - name: Upload performance results
      uses: actions/upload-artifact@v4
      with:
        name: performance-results
        path: test_output/
        retention-days: 30

  # ==========================================
  # SECURITY SCANNING
  # ==========================================
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

  # ==========================================
  # NOTIFICATION
  # ==========================================
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [godot-build, ios-deploy, performance-test]
    if: always()
    
    steps:
    - name: Notify Discord on success
      if: needs.godot-build.result == 'success' && needs.ios-deploy.result == 'success'
      uses: sarisia/actions-status-discord@v1
      with:
        webhook: ${{ secrets.DISCORD_WEBHOOK }}
        title: "✅ Galaxy Guns 3D Build Successful"
        description: "Build and deployment completed successfully!"
        color: 0x00ff00
    
    - name: Notify Discord on failure
      if: needs.godot-build.result == 'failure' || needs.ios-deploy.result == 'failure'
      uses: sarisia/actions-status-discord@v1
      with:
        webhook: ${{ secrets.DISCORD_WEBHOOK }}
        title: "❌ Galaxy Guns 3D Build Failed"
        description: "Build or deployment failed. Check the logs for details."
        color: 0xff0000
