# Galaxy Guns 3D - Development Environment Setup Script
# PowerShell script for Windows development setup

param(
    [switch]$SkipGodot,
    [switch]$SkipXcode,
    [switch]$SkipFastlane,
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

Write-Host "🌌 Galaxy Guns 3D - Development Environment Setup" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to install Chocolatey if not present
function Install-Chocolatey {
    if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Host "📦 Installing Chocolatey package manager..." -ForegroundColor Yellow
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        refreshenv
    } else {
        Write-Host "✅ Chocolatey already installed" -ForegroundColor Green
    }
}

# Function to install Git if not present
function Install-Git {
    if (!(Get-Command git -ErrorAction SilentlyContinue)) {
        Write-Host "📦 Installing Git..." -ForegroundColor Yellow
        choco install git -y
        refreshenv
    } else {
        Write-Host "✅ Git already installed: $(git --version)" -ForegroundColor Green
    }
}

# Function to install Godot 4.3
function Install-Godot {
    if ($SkipGodot) {
        Write-Host "⏭️ Skipping Godot installation" -ForegroundColor Yellow
        return
    }

    $godotPath = "$env:USERPROFILE\Tools\Godot"
    $godotExe = "$godotPath\Godot_v4.3-stable_win64.exe"
    
    if (!(Test-Path $godotExe)) {
        Write-Host "📦 Installing Godot 4.3-stable..." -ForegroundColor Yellow
        
        # Create tools directory
        New-Item -ItemType Directory -Path $godotPath -Force | Out-Null
        
        # Download Godot 4.3
        $godotUrl = "https://github.com/godotengine/godot/releases/download/4.3-stable/Godot_v4.3-stable_win64.exe.zip"
        $zipPath = "$godotPath\godot.zip"
        
        Write-Host "   Downloading from: $godotUrl" -ForegroundColor Gray
        Invoke-WebRequest -Uri $godotUrl -OutFile $zipPath
        
        # Extract Godot
        Expand-Archive -Path $zipPath -DestinationPath $godotPath -Force
        Remove-Item $zipPath
        
        # Add to PATH
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
        if ($currentPath -notlike "*$godotPath*") {
            [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$godotPath", "User")
        }
        
        Write-Host "✅ Godot 4.3 installed successfully" -ForegroundColor Green
    } else {
        Write-Host "✅ Godot 4.3 already installed" -ForegroundColor Green
    }
}

# Function to check for Xcode (macOS only, informational on Windows)
function Check-Xcode {
    if ($SkipXcode) {
        Write-Host "⏭️ Skipping Xcode check" -ForegroundColor Yellow
        return
    }

    Write-Host "ℹ️ Xcode 17.x required for iOS development (macOS only)" -ForegroundColor Blue
    Write-Host "   Please ensure Xcode is installed on your macOS build machine" -ForegroundColor Gray
    Write-Host "   Download from: https://developer.apple.com/xcode/" -ForegroundColor Gray
}

# Function to install Ruby and Fastlane
function Install-Fastlane {
    if ($SkipFastlane) {
        Write-Host "⏭️ Skipping Fastlane installation" -ForegroundColor Yellow
        return
    }

    # Install Ruby via Chocolatey
    if (!(Get-Command ruby -ErrorAction SilentlyContinue)) {
        Write-Host "📦 Installing Ruby..." -ForegroundColor Yellow
        choco install ruby -y
        refreshenv
    } else {
        Write-Host "✅ Ruby already installed: $(ruby --version)" -ForegroundColor Green
    }

    # Install Fastlane gem
    Write-Host "📦 Installing Fastlane..." -ForegroundColor Yellow
    gem install fastlane
    
    Write-Host "✅ Fastlane installed successfully" -ForegroundColor Green
}

# Function to install Docker Desktop
function Install-Docker {
    if (!(Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Host "📦 Installing Docker Desktop..." -ForegroundColor Yellow
        choco install docker-desktop -y
        Write-Host "⚠️ Docker Desktop requires restart to complete installation" -ForegroundColor Yellow
    } else {
        Write-Host "✅ Docker already installed: $(docker --version)" -ForegroundColor Green
    }
}

# Function to install additional development tools
function Install-DevTools {
    Write-Host "📦 Installing additional development tools..." -ForegroundColor Yellow
    
    # Install useful tools
    $tools = @(
        "vscode",
        "aseprite",
        "audacity",
        "7zip",
        "curl",
        "wget"
    )
    
    foreach ($tool in $tools) {
        try {
            choco install $tool -y --ignore-checksums
        } catch {
            Write-Host "⚠️ Failed to install $tool, continuing..." -ForegroundColor Yellow
        }
    }
}

# Function to setup project structure
function Setup-ProjectStructure {
    Write-Host "📁 Setting up project structure..." -ForegroundColor Yellow
    
    # Create build directories
    $buildDirs = @(
        "builds",
        "builds/ios",
        "builds/ios_sim",
        "builds/desktop"
    )
    
    foreach ($dir in $buildDirs) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
    
    Write-Host "✅ Project structure created" -ForegroundColor Green
}

# Function to initialize Git repository
function Initialize-Git {
    if (!(Test-Path ".git")) {
        Write-Host "📦 Initializing Git repository..." -ForegroundColor Yellow
        git init
        git add .
        git commit -m "feat: initial project setup with Godot 4.3 and iOS export configuration"
        Write-Host "✅ Git repository initialized" -ForegroundColor Green
    } else {
        Write-Host "✅ Git repository already exists" -ForegroundColor Green
    }
}

# Function to create VS Code workspace
function Create-VSCodeWorkspace {
    $workspaceConfig = @{
        folders = @(
            @{ path = "./client" }
            @{ path = "./server" }
            @{ path = "./shared" }
            @{ path = "./docs" }
        )
        settings = @{
            "godot_tools.editor_path" = "$env:USERPROFILE\Tools\Godot\Godot_v4.3-stable_win64.exe"
            "files.associations" = @{
                "*.gd" = "gdscript"
                "*.cs" = "csharp"
                "*.tres" = "godot-resource"
                "*.tscn" = "godot-scene"
            }
        }
        extensions = @{
            recommendations = @(
                "geequlim.godot-tools",
                "ms-vscode.csharp",
                "rust-lang.rust-analyzer",
                "ms-python.python"
            )
        }
    }
    
    $workspaceJson = $workspaceConfig | ConvertTo-Json -Depth 10
    $workspaceJson | Out-File -FilePath "galaxy-guns-3d.code-workspace" -Encoding UTF8
    
    Write-Host "✅ VS Code workspace created" -ForegroundColor Green
}

# Main execution
try {
    Write-Host ""
    
    # Check administrator privileges
    if (!(Test-Administrator)) {
        Write-Host "⚠️ Running without administrator privileges. Some installations may fail." -ForegroundColor Yellow
        Write-Host "   Consider running as administrator for best results." -ForegroundColor Gray
        Write-Host ""
    }
    
    # Install core tools
    Install-Chocolatey
    Install-Git
    Install-Godot
    Check-Xcode
    Install-Fastlane
    Install-Docker
    Install-DevTools
    
    # Setup project
    Setup-ProjectStructure
    Initialize-Git
    Create-VSCodeWorkspace
    
    Write-Host ""
    Write-Host "🎉 Development environment setup complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Open 'galaxy-guns-3d.code-workspace' in VS Code" -ForegroundColor White
    Write-Host "2. Install recommended VS Code extensions" -ForegroundColor White
    Write-Host "3. Open client/project.godot in Godot 4.3" -ForegroundColor White
    Write-Host "4. Configure iOS export templates in Godot" -ForegroundColor White
    Write-Host "5. Set up Apple Developer account and certificates" -ForegroundColor White
    Write-Host ""
    Write-Host "For iOS development, you'll need:" -ForegroundColor Yellow
    Write-Host "- macOS machine with Xcode 17.x" -ForegroundColor White
    Write-Host "- Apple Developer Program membership" -ForegroundColor White
    Write-Host "- iOS export templates for Godot 4.3" -ForegroundColor White
    Write-Host ""
    
} catch {
    Write-Host "❌ Setup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
