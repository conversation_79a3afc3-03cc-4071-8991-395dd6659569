[gd_resource type="AudioBusLayout" format=3]

[sub_resource type="AudioEffectCompressor" id="AudioEffectCompressor_1"]
resource_name = "Master Compressor"
threshold = -12.0
ratio = 4.0
gain = 2.0
attack_us = 20.0
release_ms = 250.0
mix = 1.0
sidechain = ""

[sub_resource type="AudioEffectLimiter" id="AudioEffectLimiter_1"]
resource_name = "Master Limiter"
ceiling_db = -0.1
threshold_db = -3.0
soft_clip_db = -6.0
soft_clip_ratio = 10.0

[sub_resource type="AudioEffectEQ10" id="AudioEffectEQ10_1"]
resource_name = "Music EQ"
band_60_hz/gain_db = 0.0
band_170_hz/gain_db = 0.0
band_350_hz/gain_db = 0.0
band_1000_hz/gain_db = 0.0
band_2700_hz/gain_db = 0.0
band_7200_hz/gain_db = 0.0
band_20500_hz/gain_db = 0.0

[sub_resource type="AudioEffectCompressor" id="AudioEffectCompressor_2"]
resource_name = "Music Compressor"
threshold = -18.0
ratio = 2.5
gain = 1.0
attack_us = 50.0
release_ms = 500.0
mix = 0.8
sidechain = ""

[sub_resource type="AudioEffectReverb" id="AudioEffectReverb_1"]
resource_name = "SFX Reverb"
predelay_ms = 15.0
predelay_feedback = 0.4
room_size = 0.8
damping = 0.5
spread = 1.0
hipass = 0.0
dry = 0.8
wet = 0.2

[sub_resource type="AudioEffectCompressor" id="AudioEffectCompressor_3"]
resource_name = "SFX Compressor"
threshold = -15.0
ratio = 3.0
gain = 1.5
attack_us = 10.0
release_ms = 100.0
mix = 0.9
sidechain = ""

[sub_resource type="AudioEffectHighPassFilter" id="AudioEffectHighPassFilter_1"]
resource_name = "Voice HPF"
cutoff_hz = 80.0
resonance = 1.0

[sub_resource type="AudioEffectCompressor" id="AudioEffectCompressor_4"]
resource_name = "Voice Compressor"
threshold = -20.0
ratio = 6.0
gain = 3.0
attack_us = 5.0
release_ms = 50.0
mix = 1.0
sidechain = ""

[sub_resource type="AudioEffectEQ6" id="AudioEffectEQ6_1"]
resource_name = "Voice EQ"
band_1/freq = 200.0
band_1/gain_db = -2.0
band_1/resonance = 1.0
band_2/freq = 1000.0
band_2/gain_db = 2.0
band_2/resonance = 1.0
band_3/freq = 3000.0
band_3/gain_db = 3.0
band_3/resonance = 1.0
band_4/freq = 8000.0
band_4/gain_db = 1.0
band_4/resonance = 1.0

[sub_resource type="AudioEffectLowPassFilter" id="AudioEffectLowPassFilter_1"]
resource_name = "Ambient LPF"
cutoff_hz = 8000.0
resonance = 1.0

[sub_resource type="AudioEffectReverb" id="AudioEffectReverb_2"]
resource_name = "Ambient Reverb"
predelay_ms = 50.0
predelay_feedback = 0.6
room_size = 1.0
damping = 0.3
spread = 1.0
hipass = 0.0
dry = 0.6
wet = 0.4

[resource]
bus/1/name = &"Music"
bus/1/solo = false
bus/1/mute = false
bus/1/bypass_fx = false
bus/1/volume_db = -5.0
bus/1/send = &"Master"
bus/1/effect/0/effect = SubResource("AudioEffectEQ10_1")
bus/1/effect/0/enabled = true
bus/1/effect/1/effect = SubResource("AudioEffectCompressor_2")
bus/1/effect/1/enabled = true

bus/2/name = &"SFX"
bus/2/solo = false
bus/2/mute = false
bus/2/bypass_fx = false
bus/2/volume_db = 0.0
bus/2/send = &"Master"
bus/2/effect/0/effect = SubResource("AudioEffectReverb_1")
bus/2/effect/0/enabled = true
bus/2/effect/1/effect = SubResource("AudioEffectCompressor_3")
bus/2/effect/1/enabled = true

bus/3/name = &"Voice"
bus/3/solo = false
bus/3/mute = false
bus/3/bypass_fx = false
bus/3/volume_db = 0.0
bus/3/send = &"Master"
bus/3/effect/0/effect = SubResource("AudioEffectHighPassFilter_1")
bus/3/effect/0/enabled = true
bus/3/effect/1/effect = SubResource("AudioEffectCompressor_4")
bus/3/effect/1/enabled = true
bus/3/effect/2/effect = SubResource("AudioEffectEQ6_1")
bus/3/effect/2/enabled = true

bus/4/name = &"Ambient"
bus/4/solo = false
bus/4/mute = false
bus/4/bypass_fx = false
bus/4/volume_db = -8.0
bus/4/send = &"Master"
bus/4/effect/0/effect = SubResource("AudioEffectLowPassFilter_1")
bus/4/effect/0/enabled = true
bus/4/effect/1/effect = SubResource("AudioEffectReverb_2")
bus/4/effect/1/enabled = true

bus/0/effect/0/effect = SubResource("AudioEffectCompressor_1")
bus/0/effect/0/enabled = true
bus/0/effect/1/effect = SubResource("AudioEffectLimiter_1")
bus/0/effect/1/enabled = true
