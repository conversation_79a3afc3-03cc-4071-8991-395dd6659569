{"bosses": {"warp_hydra": {"id": "BOSS-01", "name": "The Warp Hydra", "theme": "Time Distortion", "arena_shape": "hexagon", "base_hp": 2000, "speed": 1.5, "color": "#9900CC", "size": 40, "enrage_timer": 240, "taunt": "Time bends to my will, mortal!", "phases": [{"hp_threshold": 75, "name": "Temporal Assault", "description": "Basic time-based attacks", "mechanics": ["time_bolt", "chronos_dash"], "attack_rate": 2000, "movement_pattern": "circle_strafe"}, {"hp_threshold": 50, "name": "Reality Shift", "description": "Arena rotates every 20s, controls reversed", "mechanics": ["arena_rotate", "reverse_controls", "time_echo"], "attack_rate": 1500, "movement_pattern": "teleport_random"}, {"hp_threshold": 25, "name": "Temporal Collapse", "description": "Creates temporal duplicates, reality tears", "mechanics": ["time_storm", "duplicate_boss", "reality_tear"], "attack_rate": 1000, "movement_pattern": "phase_shift"}], "reward": {"type": "legendary", "item": "chrono_core", "description": "Chrono Core - Time manipulation device", "stats": {"damage_multiplier": 1.5, "special_ability": "time_slow"}}, "minions": ["hydra_spawn"], "immunities": ["time_effects"], "weaknesses": ["void_damage"]}, "gravemind_core": {"id": "BOSS-02", "name": "Gravemind Core", "theme": "Bio-mechanical Horror", "arena_shape": "circle", "base_hp": 2500, "speed": 1.0, "color": "#009900", "size": 45, "enrage_timer": 300, "taunt": "Flesh is weak. I am eternal.", "phases": [{"hp_threshold": 75, "name": "Awakening", "description": "Spawns bio-tendrils around arena", "mechanics": ["bio_pulse", "spawn_tendrils"], "attack_rate": 2500, "movement_pattern": "stationary"}, {"hp_threshold": 50, "name": "Infestation", "description": "Creates acid pools, healing pods", "mechanics": ["acid_pools", "healing_pods", "mass_spawn"], "attack_rate": 2000, "movement_pattern": "slow_crawl"}, {"hp_threshold": 25, "name": "Metamorphosis", "description": "Adapts to damage types, drains player health", "mechanics": ["bio_storm", "adaptive_armor", "life_drain"], "attack_rate": 1500, "movement_pattern": "aggressive_chase"}], "reward": {"type": "upgrade", "item": "weapon_slot", "description": "Unlocks +1 weapon slot", "stats": {"weapon_slots": 1}}, "minions": ["grave_tendril", "bio_spawn"], "immunities": ["poison", "bio_effects"], "weaknesses": ["fire_damage", "energy_weapons"]}, "void_empress": {"id": "BOSS-03", "name": "Void Empress", "theme": "Dimensional Chaos", "arena_shape": "octagon", "base_hp": 3000, "speed": 2.0, "color": "#330033", "size": 50, "enrage_timer": 360, "taunt": "Reality is mine to command!", "phases": [{"hp_threshold": 75, "name": "Void Awakening", "description": "Opens dimensional rifts", "mechanics": ["void_beam", "dimensional_rift"], "attack_rate": 1800, "movement_pattern": "dimensional_hop"}, {"hp_threshold": 50, "name": "Reality Storm", "description": "Creates phase walls and gravity wells", "mechanics": ["phase_walls", "gravity_wells", "void_spawn"], "attack_rate": 1500, "movement_pattern": "reality_warp"}, {"hp_threshold": 25, "name": "Dimensional Collapse", "description": "Inverts arena physics, locks dimensions", "mechanics": ["reality_inversion", "void_cascade", "dimension_lock"], "attack_rate": 1200, "movement_pattern": "omnipresent"}], "reward": {"type": "legendary", "item": "void_manipulator", "description": "Void Manipulator - Reality bending weapon", "stats": {"damage_multiplier": 2.0, "special_ability": "void_rift"}}, "minions": ["void_spawn", "reality_fragment"], "immunities": ["void_damage", "dimensional_effects"], "weaknesses": ["light_damage", "order_magic"]}, "quantum_overlord": {"id": "BOSS-04", "name": "Quantum Overlord", "theme": "Probability Control", "arena_shape": "square", "base_hp": 3500, "speed": 1.8, "color": "#FF00FF", "size": 55, "enrage_timer": 420, "taunt": "All possibilities lead to your defeat!", "phases": [{"hp_threshold": 75, "name": "Quantum State", "description": "Manipulates probability fields", "mechanics": ["probability_beam", "quantum_teleport"], "attack_rate": 1600, "movement_pattern": "quantum_superposition"}, {"hp_threshold": 50, "name": "Superposition", "description": "Exists in multiple states simultaneously", "mechanics": ["quantum_split", "probability_storm", "phase_shift"], "attack_rate": 1300, "movement_pattern": "multiple_instances"}, {"hp_threshold": 25, "name": "Quantum Collapse", "description": "Creates quantum singularities", "mechanics": ["reality_anchor", "probability_zero", "quantum_singularity"], "attack_rate": 1000, "movement_pattern": "probability_cloud"}], "reward": {"type": "legendary", "item": "quantum_processor", "description": "Quantum Processor - Probability manipulation core", "stats": {"crit_chance": 50, "special_ability": "quantum_leap"}}, "minions": ["quantum_echo", "probability_shard"], "immunities": ["probability_effects", "quantum_damage"], "weaknesses": ["deterministic_attacks", "chaos_magic"]}, "stellar_devourer": {"id": "BOSS-05", "name": "<PERSON><PERSON>", "theme": "Cosmic Horror", "arena_shape": "circle", "base_hp": 4000, "speed": 1.2, "color": "#FFAA00", "size": 60, "enrage_timer": 480, "taunt": "I have devoured countless worlds!", "phases": [{"hp_threshold": 75, "name": "Stellar Hunger", "description": "Devours light and energy", "mechanics": ["solar_flare", "gravity_pulse"], "attack_rate": 2200, "movement_pattern": "orbital_drift"}, {"hp_threshold": 50, "name": "Cosmic Storm", "description": "Creates black holes and stellar winds", "mechanics": ["black_hole", "stellar_wind", "energy_drain"], "attack_rate": 1800, "movement_pattern": "gravitational_pull"}, {"hp_threshold": 25, "name": "Galactic Annihilation", "description": "Threatens to consume the galaxy", "mechanics": ["supernova", "space_time_tear", "cosmic_void"], "attack_rate": 1400, "movement_pattern": "reality_consumption"}], "reward": {"type": "legendary", "item": "stellar_core", "description": "Stellar Core - Harnesses the power of stars", "stats": {"damage_multiplier": 3.0, "special_ability": "solar_burst"}}, "minions": ["star_fragment", "cosmic_horror"], "immunities": ["fire_damage", "energy_drain", "gravity_effects"], "weaknesses": ["cold_damage", "dark_matter"]}}, "mechanics": {"time_bolt": {"name": "Time Bolt", "description": "Fires temporal energy projectiles", "damage": 25, "effect": "slow_player", "cooldown": 1000}, "chronos_dash": {"name": "Chronos Dash", "description": "Teleports behind player", "damage": 30, "effect": "teleport_strike", "cooldown": 3000}, "arena_rotate": {"name": "Arena Rotate", "description": "Rotates the entire battlefield", "damage": 0, "effect": "rotate_arena", "cooldown": 20000}, "reverse_controls": {"name": "Reverse Controls", "description": "Inverts player movement", "damage": 0, "effect": "control_inversion", "cooldown": 15000}, "time_storm": {"name": "Time Storm", "description": "Area-wide temporal distortion", "damage": 40, "effect": "area_slow", "cooldown": 8000}, "duplicate_boss": {"name": "Duplicate Boss", "description": "Creates temporal echoes", "damage": 0, "effect": "spawn_duplicates", "cooldown": 25000}, "bio_pulse": {"name": "Bio Pulse", "description": "Organic energy wave", "damage": 20, "effect": "poison_dot", "cooldown": 2000}, "spawn_tendrils": {"name": "Spawn Tendrils", "description": "Creates bio-mechanical minions", "damage": 0, "effect": "summon_minions", "cooldown": 12000}, "acid_pools": {"name": "Acid Pools", "description": "Creates damaging floor hazards", "damage": 15, "effect": "persistent_hazard", "cooldown": 10000}, "life_drain": {"name": "Life Drain", "description": "Steals player health", "damage": 35, "effect": "heal_boss", "cooldown": 6000}, "void_beam": {"name": "Void Beam", "description": "Concentrated void energy", "damage": 45, "effect": "armor_pierce", "cooldown": 3000}, "dimensional_rift": {"name": "Dimensional Rift", "description": "Tears holes in reality", "damage": 30, "effect": "teleport_player", "cooldown": 8000}, "reality_inversion": {"name": "Reality Inversion", "description": "Inverts physics laws", "damage": 0, "effect": "invert_physics", "cooldown": 30000}, "quantum_split": {"name": "Quantum Split", "description": "Exists in multiple states", "damage": 25, "effect": "multiple_attacks", "cooldown": 5000}, "probability_zero": {"name": "Probability Zero", "description": "Makes attacks impossible to miss", "damage": 50, "effect": "guaranteed_hit", "cooldown": 15000}, "solar_flare": {"name": "Solar Flare", "description": "Massive energy explosion", "damage": 60, "effect": "screen_flash", "cooldown": 4000}, "black_hole": {"name": "Black Hole", "description": "Creates gravitational anomaly", "damage": 40, "effect": "pull_player", "cooldown": 12000}, "supernova": {"name": "Supernova", "description": "Ultimate stellar explosion", "damage": 80, "effect": "arena_wide_damage", "cooldown": 20000}}}