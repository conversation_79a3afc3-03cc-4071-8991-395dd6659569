<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Galaxy Guns 3D - Complete Progression System</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        background: linear-gradient(
          135deg,
          #0a0a0a 0%,
          #1a1a2e 50%,
          #16213e 100%
        );
        font-family: "Courier New", monospace;
        color: white;
        overflow: hidden;
        user-select: none;
      }

      #gameContainer {
        position: relative;
        width: 100vw;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      #gameCanvas {
        background: radial-gradient(circle at center, #2a2a4a 0%, #0a0a0a 100%);
        border: 2px solid #ff6b6b;
        box-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
        cursor: crosshair;
      }

      #ui {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 10;
      }

      #hud {
        position: absolute;
        top: 20px;
        left: 20px;
        font-size: 16px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
      }

      #playerLevel {
        background: rgba(0, 0, 0, 0.7);
        padding: 8px 12px;
        border-radius: 8px;
        border: 2px solid #4caf50;
        margin-bottom: 10px;
      }

      #xpBar {
        width: 200px;
        height: 8px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 4px;
        overflow: hidden;
        margin-top: 5px;
      }

      #xpFill {
        height: 100%;
        background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
        transition: width 0.3s ease;
        border-radius: 4px;
      }

      #crosshair {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 30px;
        height: 30px;
        pointer-events: none;
      }

      .crosshair-line {
        position: absolute;
        background: #ff6b6b;
        box-shadow: 0 0 10px rgba(255, 107, 107, 0.8);
      }

      .crosshair-h {
        width: 20px;
        height: 2px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      .crosshair-v {
        width: 2px;
        height: 20px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      #equipmentHUD {
        position: absolute;
        bottom: 30px;
        left: 30px;
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .equipment-slot {
        width: 60px;
        height: 60px;
        background: rgba(0, 0, 0, 0.7);
        border: 2px solid #666;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        position: relative;
      }

      .equipment-slot.weapon {
        border-color: #ff6b6b;
      }
      .equipment-slot.armor {
        border-color: #4caf50;
      }
      .equipment-slot.accessory {
        border-color: #2196f3;
      }

      .rarity-common {
        box-shadow: 0 0 10px rgba(128, 128, 128, 0.5);
      }
      .rarity-uncommon {
        box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
      }
      .rarity-rare {
        box-shadow: 0 0 10px rgba(0, 100, 255, 0.5);
      }
      .rarity-epic {
        box-shadow: 0 0 10px rgba(128, 0, 255, 0.5);
      }
      .rarity-legendary {
        box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
      }

      #ammoCounter {
        position: absolute;
        bottom: 30px;
        right: 30px;
        font-size: 24px;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
      }

      #healthBar {
        position: absolute;
        bottom: 100px;
        left: 30px;
        width: 200px;
        height: 20px;
        background: rgba(0, 0, 0, 0.5);
        border: 2px solid #fff;
        border-radius: 10px;
        overflow: hidden;
      }

      #healthFill {
        height: 100%;
        background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
        transition: width 0.3s ease;
        border-radius: 8px;
      }

      #currency {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.7);
        padding: 8px 12px;
        border-radius: 8px;
        border: 2px solid #ffd700;
        font-size: 18px;
      }

      #inventoryButton,
      #storeButton,
      #achievementsButton {
        position: absolute;
        top: 80px;
        right: 20px;
        background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
        border: none;
        border-radius: 8px;
        color: white;
        padding: 10px 15px;
        cursor: pointer;
        font-family: "Courier New", monospace;
        font-size: 14px;
        pointer-events: all;
        margin-bottom: 10px;
      }

      #storeButton {
        top: 120px;
        background: linear-gradient(45deg, #4caf50, #66bb6a);
      }

      #achievementsButton {
        top: 160px;
        background: linear-gradient(45deg, #2196f3, #42a5f5);
      }

      #mapButton {
        top: 220px;
        background: linear-gradient(45deg, #9c27b0, #ba68c8);
      }

      /* NEW: Galactic Map UI */
      #galacticMap {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: radial-gradient(circle at center, #1a1a2e 0%, #0a0a0a 100%);
        display: none;
        z-index: 1000;
        overflow: hidden;
      }

      #mapCanvas {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border: 2px solid #00ff99;
        background: rgba(0, 0, 0, 0.8);
        cursor: crosshair;
      }

      #mapHUD {
        position: absolute;
        top: 20px;
        left: 20px;
        color: white;
        font-family: "Courier New", monospace;
        z-index: 1001;
      }

      #sectorInfo {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(0, 0, 0, 0.8);
        padding: 15px;
        border-radius: 10px;
        border: 2px solid #00ff99;
        color: white;
        font-family: "Courier New", monospace;
        min-width: 250px;
        z-index: 1001;
      }

      #mapControls {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 10px;
        z-index: 1001;
      }

      .map-button {
        background: linear-gradient(45deg, #00ff99, #00cc77);
        border: none;
        border-radius: 8px;
        color: black;
        padding: 12px 20px;
        cursor: pointer;
        font-family: "Courier New", monospace;
        font-weight: bold;
        transition: all 0.3s ease;
      }

      .map-button:hover {
        background: linear-gradient(45deg, #00cc77, #009955);
        transform: translateY(-2px);
      }

      .map-button:disabled {
        background: #666666;
        cursor: not-allowed;
        transform: none;
      }

      /* NEW: Dungeon UI */
      #dungeonHUD {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        padding: 10px 20px;
        border-radius: 8px;
        border: 2px solid #00ff99;
        color: white;
        font-family: "Courier New", monospace;
        display: none;
      }

      #roomInfo {
        text-align: center;
        font-size: 14px;
      }

      #keycardDisplay {
        position: absolute;
        top: 120px;
        right: 20px;
        background: rgba(0, 0, 0, 0.7);
        padding: 10px;
        border-radius: 8px;
        border: 2px solid #ffd700;
        color: white;
        font-family: "Courier New", monospace;
      }

      .keycard {
        display: inline-block;
        width: 20px;
        height: 12px;
        margin: 2px;
        border-radius: 2px;
        border: 1px solid #333;
      }

      .keycard.red {
        background: #ff4444;
      }
      .keycard.blue {
        background: #4444ff;
      }
      .keycard.yellow {
        background: #ffff44;
      }
      .keycard.inactive {
        background: #333333;
      }

      /* NEW: Minimap for dungeon */
      #dungeonMinimap {
        position: absolute;
        top: 20px;
        left: 20px;
        width: 150px;
        height: 150px;
        background: rgba(0, 0, 0, 0.7);
        border: 2px solid #00ff99;
        border-radius: 5px;
        display: none;
      }

      /* NEW: Boss UI */
      #bossHUD {
        position: absolute;
        top: 50px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        padding: 10px 20px;
        border-radius: 8px;
        border: 2px solid #ff0088;
        color: white;
        font-family: "Courier New", monospace;
        display: none;
        text-align: center;
      }

      #bossHealthBar {
        width: 400px;
        height: 15px;
        background: rgba(0, 0, 0, 0.5);
        border: 1px solid #fff;
        border-radius: 7px;
        overflow: hidden;
        margin-top: 5px;
      }

      #bossHealthFill {
        height: 100%;
        background: linear-gradient(90deg, #ff0088 0%, #ff88cc 100%);
        transition: width 0.3s ease;
        border-radius: 6px;
      }

      #bossPhase {
        font-size: 12px;
        margin-top: 5px;
        color: #ff88cc;
      }

      .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        pointer-events: all;
      }

      .modal-content {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        border: 2px solid #ff6b6b;
        border-radius: 15px;
        padding: 30px;
        max-width: 80%;
        max-height: 80%;
        overflow-y: auto;
        position: relative;
      }

      .close-button {
        position: absolute;
        top: 10px;
        right: 15px;
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
      }

      .inventory-grid {
        display: grid;
        grid-template-columns: repeat(8, 60px);
        gap: 10px;
        margin-top: 20px;
      }

      .inventory-slot {
        width: 60px;
        height: 60px;
        background: rgba(0, 0, 0, 0.5);
        border: 2px solid #666;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: relative;
        font-size: 20px;
      }

      .inventory-slot:hover {
        border-color: #ff6b6b;
      }

      .item-tooltip {
        position: absolute;
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid #ff6b6b;
        border-radius: 8px;
        padding: 10px;
        font-size: 12px;
        z-index: 200;
        pointer-events: none;
        min-width: 200px;
      }

      .boss-warning {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 0, 0, 0.9);
        padding: 20px;
        border-radius: 15px;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        animation: pulse 1s infinite;
        z-index: 50;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: translate(-50%, -50%) scale(1);
        }
        50% {
          transform: translate(-50%, -50%) scale(1.1);
        }
      }

      .level-up-notification {
        position: absolute;
        top: 30%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(45deg, #ffd700, #ffa500);
        color: black;
        padding: 20px;
        border-radius: 15px;
        font-size: 20px;
        font-weight: bold;
        text-align: center;
        animation: levelUpAnim 3s ease-out forwards;
        z-index: 60;
      }

      @keyframes levelUpAnim {
        0% {
          transform: translate(-50%, -50%) scale(0);
          opacity: 0;
        }
        20% {
          transform: translate(-50%, -50%) scale(1.2);
          opacity: 1;
        }
        80% {
          transform: translate(-50%, -50%) scale(1);
          opacity: 1;
        }
        100% {
          transform: translate(-50%, -50%) scale(1);
          opacity: 0;
        }
      }

      .damage-number {
        position: absolute;
        color: #ffff00;
        font-weight: bold;
        font-size: 16px;
        pointer-events: none;
        animation: damageFloat 1s ease-out forwards;
      }

      @keyframes damageFloat {
        0% {
          transform: translateY(0);
          opacity: 1;
        }
        100% {
          transform: translateY(-50px);
          opacity: 0;
        }
      }

      .achievement-notification {
        position: absolute;
        top: 200px;
        right: 20px;
        background: linear-gradient(45deg, #2196f3, #42a5f5);
        padding: 15px;
        border-radius: 10px;
        font-size: 14px;
        animation: slideIn 0.5s ease-out;
        z-index: 70;
      }

      @keyframes slideIn {
        from {
          transform: translateX(100%);
        }
        to {
          transform: translateX(0);
        }
      }

      #startScreen {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.95);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 100;
      }

      #title {
        font-size: 48px;
        font-weight: bold;
        margin-bottom: 20px;
        text-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
        animation: glow 2s ease-in-out infinite alternate;
      }

      @keyframes glow {
        from {
          text-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
        }
        to {
          text-shadow: 0 0 30px rgba(255, 107, 107, 1),
            0 0 40px rgba(255, 107, 107, 0.8);
        }
      }

      .gameButton {
        padding: 15px 30px;
        font-size: 20px;
        background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
        border: none;
        border-radius: 10px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 10px;
        font-family: "Courier New", monospace;
        pointer-events: all;
      }

      .gameButton:hover {
        background: linear-gradient(45deg, #ff8e8e, #ffaaaa);
        transform: scale(1.05);
        box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
      }

      .enemy {
        position: absolute;
        border-radius: 50%;
        transition: all 0.1s ease;
      }

      .boss-enemy {
        border: 3px solid #ff0000;
        box-shadow: 0 0 20px rgba(255, 0, 0, 0.8);
      }

      .bullet {
        position: absolute;
        width: 4px;
        height: 4px;
        background: #ffff00;
        border-radius: 50%;
        box-shadow: 0 0 8px rgba(255, 255, 0, 0.8);
      }

      .explosion {
        position: absolute;
        width: 30px;
        height: 30px;
        background: radial-gradient(
          circle,
          #ff6b6b 0%,
          #ff4444 50%,
          transparent 100%
        );
        border-radius: 50%;
        animation: explode 0.3s ease-out forwards;
      }

      @keyframes explode {
        0% {
          transform: scale(0);
          opacity: 1;
        }
        100% {
          transform: scale(2);
          opacity: 0;
        }
      }

      .muzzleFlash {
        position: absolute;
        width: 40px;
        height: 40px;
        background: radial-gradient(
          circle,
          #ffff00 0%,
          #ff6b6b 50%,
          transparent 100%
        );
        border-radius: 50%;
        animation: flash 0.1s ease-out forwards;
        pointer-events: none;
      }

      @keyframes flash {
        0% {
          transform: scale(0);
          opacity: 1;
        }
        100% {
          transform: scale(1.5);
          opacity: 0;
        }
      }

      .equipment-category {
        margin-bottom: 20px;
      }

      .equipment-category h3 {
        color: #ff6b6b;
        margin-bottom: 10px;
        border-bottom: 2px solid #ff6b6b;
        padding-bottom: 5px;
      }

      .store-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(0, 0, 0, 0.3);
        padding: 10px;
        margin: 5px 0;
        border-radius: 8px;
        border: 1px solid #666;
      }

      .store-item:hover {
        border-color: #ff6b6b;
        background: rgba(255, 107, 107, 0.1);
      }

      .buy-button {
        background: linear-gradient(45deg, #4caf50, #66bb6a);
        border: none;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        font-family: "Courier New", monospace;
      }

      .buy-button:disabled {
        background: #666;
        cursor: not-allowed;
      }

      .achievement-item {
        background: rgba(0, 0, 0, 0.3);
        padding: 15px;
        margin: 10px 0;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
      }

      .achievement-completed {
        border-left-color: #4caf50;
        background: rgba(76, 175, 80, 0.1);
      }

      .daily-challenge {
        background: rgba(255, 193, 7, 0.1);
        border: 2px solid #ffc107;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 4px;
        overflow: hidden;
        margin-top: 5px;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4caf50 0%, #8bc34a 100%);
        transition: width 0.3s ease;
      }
    </style>
  </head>
  <body>
    <div id="gameContainer">
      <canvas id="gameCanvas" width="1200" height="800" tabindex="0"></canvas>

      <div id="ui">
        <div id="hud">
          <div id="playerLevel">
            <div>Level: <span id="levelValue">1</span></div>
            <div>
              XP: <span id="xpValue">0</span> /
              <span id="xpNextLevel">100</span>
            </div>
            <div id="xpBar"><div id="xpFill" style="width: 0%"></div></div>
          </div>
          <div id="score">Score: 0</div>
          <div id="wave">Wave: 1</div>
          <div id="fps">FPS: 60</div>
        </div>

        <div id="crosshair">
          <div class="crosshair-line crosshair-h"></div>
          <div class="crosshair-line crosshair-v"></div>
        </div>

        <div id="currency">
          <span id="currencyIcon">💰</span> <span id="currencyValue">0</span>
        </div>

        <button id="inventoryButton" onclick="toggleInventory()">
          Inventory
        </button>
        <button id="storeButton" onclick="toggleStore()">Store</button>
        <button id="achievementsButton" onclick="toggleAchievements()">
          Achievements
        </button>
        <button id="mapButton" onclick="toggleGalacticMap()">
          🗺️ Galaxy Map
        </button>

        <div id="ammoCounter">30 / 120</div>

        <div id="healthBar">
          <div id="healthFill" style="width: 100%"></div>
        </div>

        <div id="equipmentHUD">
          <div
            class="equipment-slot weapon"
            id="weaponSlot"
            onclick="toggleInventory('weapon')"
          >
            🔫
          </div>
          <div
            class="equipment-slot armor"
            id="armorSlot"
            onclick="toggleInventory('armor')"
          >
            🛡️
          </div>
          <div
            class="equipment-slot accessory"
            id="accessorySlot"
            onclick="toggleInventory('accessory')"
          >
            💍
          </div>
        </div>
      </div>

      <!-- Inventory Modal -->
      <div id="inventoryModal" class="modal">
        <div class="modal-content">
          <button class="close-button" onclick="toggleInventory()">×</button>
          <h2>Inventory</h2>

          <div class="inventory-filters">
            <button onclick="filterInventory('all')" class="gameButton">
              All
            </button>
            <button onclick="filterInventory('weapon')" class="gameButton">
              Weapons
            </button>
            <button onclick="filterInventory('armor')" class="gameButton">
              Armor
            </button>
            <button onclick="filterInventory('accessory')" class="gameButton">
              Accessories
            </button>
          </div>

          <div class="inventory-grid" id="inventoryGrid">
            <!-- Inventory slots will be generated here -->
          </div>

          <div id="itemDetails" style="margin-top: 20px">
            <h3>Item Details</h3>
            <div id="selectedItemDetails">Select an item to view details</div>
          </div>
        </div>
      </div>

      <!-- Store Modal -->
      <div id="storeModal" class="modal">
        <div class="modal-content">
          <button class="close-button" onclick="toggleStore()">×</button>
          <h2>Store</h2>

          <div class="equipment-category">
            <h3>Weapons</h3>
            <div id="weaponStore">
              <!-- Store items will be generated here -->
            </div>
          </div>

          <div class="equipment-category">
            <h3>Armor</h3>
            <div id="armorStore">
              <!-- Store items will be generated here -->
            </div>
          </div>

          <div class="equipment-category">
            <h3>Accessories</h3>
            <div id="accessoryStore">
              <!-- Store items will be generated here -->
            </div>
          </div>
        </div>
      </div>

      <!-- Achievements Modal -->
      <div id="achievementsModal" class="modal">
        <div class="modal-content">
          <button class="close-button" onclick="toggleAchievements()">×</button>
          <h2>Achievements</h2>

          <div class="daily-challenge">
            <h3>Daily Challenge</h3>
            <div id="dailyChallenge">
              <p>Eliminate 50 enemies in a single game</p>
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  id="dailyChallengeProgress"
                  style="width: 0%"
                ></div>
              </div>
              <p>Reward: 500 💰 + Rare Equipment</p>
            </div>
          </div>

          <div id="achievementsList">
            <!-- Achievements will be generated here -->
          </div>
        </div>
      </div>

      <div id="startScreen">
        <div id="title">GALAXY GUNS 3D</div>
        <div id="gameMode">
          <h3>🚀 Complete Progression System</h3>
          <p>
            Experience the full RPG progression system with equipment, levels,
            and rewards!
          </p>
          <p>
            ✨ Features: Equipment system, XP progression, difficulty scaling,
            and more
          </p>
        </div>
        <button class="gameButton" onclick="startGame()">🎮 START GAME</button>
        <button class="gameButton" onclick="showControls()">🎯 CONTROLS</button>
        <button class="gameButton" onclick="showAbout()">ℹ️ ABOUT</button>
      </div>
    </div>

    <!-- NEW: Galactic Map -->
    <div id="galacticMap">
      <div id="mapHUD">
        <h2>🌌 GALACTIC SECTOR CHART</h2>
        <p>NavKey Fragments: <span id="navKeyCount">0</span></p>
        <p>Sectors Cleared: <span id="sectorsCleared">0</span></p>
      </div>

      <canvas id="mapCanvas" width="800" height="600"></canvas>

      <div id="sectorInfo">
        <h3 id="sectorName">Select a Sector</h3>
        <p id="sectorType">Type: Unknown</p>
        <p id="sectorDifficulty">Difficulty: -</p>
        <p id="sectorReward">Reward: -</p>
        <p id="sectorStatus">Status: Locked</p>
        <div id="sectorDescription">Hover over a sector to see details.</div>
      </div>

      <div id="mapControls">
        <button
          class="map-button"
          onclick="enterSector()"
          id="enterSectorBtn"
          disabled
        >
          Enter Sector
        </button>
        <button class="map-button" onclick="toggleGalacticMap()">
          Close Map
        </button>
      </div>
    </div>

    <!-- NEW: Dungeon HUD -->
    <div id="dungeonHUD">
      <div id="roomInfo">
        <span id="currentRoomType">Start Bay</span> | Room
        <span id="currentRoomIndex">1</span> of <span id="totalRooms">8</span>
      </div>
    </div>

    <!-- NEW: Keycard Display -->
    <div id="keycardDisplay">
      <div>🗝️ Keycards:</div>
      <div class="keycard red" id="redKeycard"></div>
      <div class="keycard blue" id="blueKeycard"></div>
      <div class="keycard yellow" id="yellowKeycard"></div>
    </div>

    <!-- NEW: Dungeon Minimap -->
    <canvas id="dungeonMinimap" width="150" height="150"></canvas>

    <!-- NEW: Boss HUD -->
    <div id="bossHUD">
      <div id="bossName">Boss Name</div>
      <div id="bossHealthBar">
        <div id="bossHealthFill"></div>
      </div>
      <div id="bossPhase">Phase 1: Awakening</div>
    </div>

    <script>
      // Game state management
      let gameState = {
        running: false,
        score: 0,
        wave: 1,
        health: 100,
        maxHealth: 100,
        ammo: 30,
        maxAmmo: 30,
        reserveAmmo: 120,
        reloading: false,
        enemies: [],
        bullets: [],
        particles: [],
        player: {
          x: 600,
          y: 400,
          angle: 0,
          speed: 3,
          jumpOffset: 0,
          isJumping: false,
        },
        keys: {},
        mouse: { x: 0, y: 0, down: false },
        lastShot: 0,
        fireRate: 150, // ms between shots
        enemySpawnRate: 2000, // ms between enemy spawns
        lastEnemySpawn: 0,
        waveEnemiesRemaining: 10,
        waveEnemiesKilled: 0,
        totalKills: 0,
        gameStartTime: 0,
        lastFrameTime: 0,
        fps: 60,
        frameCount: 0,
        lastFpsUpdate: 0,

        // Progression system
        level: 1,
        xp: 0,
        xpToNextLevel: 100,
        currency: 0,
        inventory: [],
        equippedItems: {
          weapon: null,
          armor: null,
          accessory: null,
        },
        achievements: [],
        dailyChallenge: {
          type: "kills",
          target: 50,
          progress: 0,
          completed: false,
        },

        // NEW: Dungeon & Map System
        currentSector: null,
        currentDungeon: null,
        dungeonSeed: 0,
        roomIndex: 0,
        navKeyFragments: 0,
        unlockedSectors: ["sector_1_1"], // Start with first sector unlocked
        clearedSectors: [],
        currentRoom: null,
        visitedRooms: [],
        keycards: { red: false, blue: false, yellow: false },

        // NEW: Enhanced Combat
        dashCharges: 2,
        maxDashCharges: 2,
        lastDash: 0,
        dashCooldown: 3000,
        reviveTokens: 0,

        // NEW: Meta Progression
        auriteCores: 0,
        blueprintShards: 0,
        metaUpgrades: {
          maxHealth: 0,
          dashCharges: 0,
          weaponSlots: 0,
          critChance: 0,
          moveSpeed: 0,
        },

        // UI states
        inventoryOpen: false,
        storeOpen: false,
        achievementsOpen: false,
        mapOpen: false,
      };

      // NEW: Galactic Map Data
      const GALACTIC_MAP = {
        sectors: [
          // Ring 1 - Starting sectors
          {
            id: "sector_1_1",
            name: "Outer Rim Station",
            type: "common",
            x: 400,
            y: 300,
            connections: ["sector_1_2"],
            unlocked: true,
            cleared: false,
          },
          {
            id: "sector_1_2",
            name: "Mining Outpost",
            type: "common",
            x: 500,
            y: 250,
            connections: ["sector_1_1", "sector_1_3", "boss_1"],
            unlocked: false,
            cleared: false,
          },
          {
            id: "sector_1_3",
            name: "Cargo Depot",
            type: "rare",
            x: 450,
            y: 350,
            connections: ["sector_1_2", "boss_1"],
            unlocked: false,
            cleared: false,
          },
          {
            id: "boss_1",
            name: "Warp Hydra Lair",
            type: "boss",
            x: 550,
            y: 300,
            connections: ["sector_1_2", "sector_1_3"],
            unlocked: false,
            cleared: false,
          },

          // Ring 2 - Requires 3 NavKey fragments
          {
            id: "sector_2_1",
            name: "Nebula Research Lab",
            type: "common",
            x: 300,
            y: 200,
            connections: ["boss_1", "sector_2_2"],
            unlocked: false,
            cleared: false,
          },
          {
            id: "sector_2_2",
            name: "Asteroid Field",
            type: "rare",
            x: 350,
            y: 150,
            connections: ["sector_2_1", "boss_2"],
            unlocked: false,
            cleared: false,
          },
          {
            id: "boss_2",
            name: "Gravemind Core",
            type: "boss",
            x: 400,
            y: 100,
            connections: ["sector_2_2"],
            unlocked: false,
            cleared: false,
          },
        ],
      };

      // NEW: Room Archetypes
      const ROOM_TYPES = {
        start_bay: {
          weight: 100,
          minSize: 15,
          maxSize: 20,
          spawns: 0,
          special: "start",
        },
        combat_hall: {
          weight: 60,
          minSize: 20,
          maxSize: 30,
          spawns: 3,
          special: null,
        },
        elite_gauntlet: {
          weight: 20,
          minSize: 25,
          maxSize: 35,
          spawns: 1,
          special: "elite",
        },
        treasure_vault: {
          weight: 10,
          minSize: 15,
          maxSize: 20,
          spawns: 1,
          special: "treasure",
        },
        puzzle_hub: {
          weight: 15,
          minSize: 20,
          maxSize: 25,
          spawns: 2,
          special: "puzzle",
        },
        boss_chamber: {
          weight: 100,
          minSize: 30,
          maxSize: 40,
          spawns: 1,
          special: "boss",
        },
      };

      // NEW: Biome Themes
      const BIOME_THEMES = {
        starship: {
          name: "Starship Corridors",
          tint: "#00FF99",
          bgPattern: "grid",
          hazards: ["plasma_leak", "force_field"],
        },
        asteroid: {
          name: "Asteroid Mining",
          tint: "#FF6B35",
          bgPattern: "rock",
          hazards: ["gravity_well", "debris_storm"],
        },
        nebula: {
          name: "Nebula Station",
          tint: "#9B59B6",
          bgPattern: "cloud",
          hazards: ["energy_storm", "void_rift"],
        },
      };

      // Equipment data
      const EQUIPMENT_DATA = {
        weapons: [
          {
            id: "w1",
            name: "Standard Pistol",
            type: "weapon",
            rarity: "common",
            level: 1,
            damage: 50,
            fireRate: 150,
            icon: "🔫",
            price: 0,
            special: null,
          },
          {
            id: "w2",
            name: "Assault Rifle",
            type: "weapon",
            rarity: "uncommon",
            level: 5,
            damage: 40,
            fireRate: 100,
            icon: "🔫",
            price: 500,
            special: null,
          },
          {
            id: "w3",
            name: "Shotgun",
            type: "weapon",
            rarity: "rare",
            level: 10,
            damage: 120,
            fireRate: 400,
            icon: "🔫",
            price: 1200,
            special: "spread",
          },
          {
            id: "w4",
            name: "Sniper Rifle",
            type: "weapon",
            rarity: "epic",
            level: 15,
            damage: 200,
            fireRate: 800,
            icon: "🔫",
            price: 2500,
            special: "piercing",
          },
          {
            id: "w5",
            name: "Plasma Cannon",
            type: "weapon",
            rarity: "legendary",
            level: 25,
            damage: 150,
            fireRate: 200,
            icon: "🔫",
            price: 5000,
            special: "explosive",
          },
        ],
        armor: [
          {
            id: "a1",
            name: "Basic Vest",
            type: "armor",
            rarity: "common",
            level: 1,
            health: 20,
            icon: "🛡️",
            price: 0,
            special: null,
          },
          {
            id: "a2",
            name: "Combat Armor",
            type: "armor",
            rarity: "uncommon",
            level: 5,
            health: 50,
            icon: "🛡️",
            price: 600,
            special: null,
          },
          {
            id: "a3",
            name: "Tactical Suit",
            type: "armor",
            rarity: "rare",
            level: 12,
            health: 100,
            icon: "🛡️",
            price: 1500,
            special: "speedBoost",
          },
          {
            id: "a4",
            name: "Energy Shield",
            type: "armor",
            rarity: "epic",
            level: 18,
            health: 150,
            icon: "🛡️",
            price: 3000,
            special: "damageReduction",
          },
          {
            id: "a5",
            name: "Quantum Armor",
            type: "armor",
            rarity: "legendary",
            level: 30,
            health: 250,
            icon: "🛡️",
            price: 6000,
            special: "regeneration",
          },
        ],
        accessories: [
          {
            id: "c1",
            name: "Ammo Pouch",
            type: "accessory",
            rarity: "common",
            level: 1,
            effect: "ammoCapacity",
            value: 30,
            icon: "💍",
            price: 0,
            special: null,
          },
          {
            id: "c2",
            name: "Targeting Scope",
            type: "accessory",
            rarity: "uncommon",
            level: 8,
            effect: "accuracy",
            value: 20,
            icon: "💍",
            price: 800,
            special: null,
          },
          {
            id: "c3",
            name: "Speed Boots",
            type: "accessory",
            rarity: "rare",
            level: 14,
            effect: "moveSpeed",
            value: 1.5,
            icon: "💍",
            price: 1800,
            special: null,
          },
          {
            id: "c4",
            name: "Critical Enhancer",
            type: "accessory",
            rarity: "epic",
            level: 22,
            effect: "critChance",
            value: 25,
            icon: "💍",
            price: 3500,
            special: "critDamage",
          },
          {
            id: "c5",
            name: "Time Distorter",
            type: "accessory",
            rarity: "legendary",
            level: 35,
            effect: "cooldown",
            value: 50,
            icon: "💍",
            price: 7000,
            special: "timeWarp",
          },
        ],
      };

      // Achievement data
      // NEW: Comprehensive Enemy System (30+ types)
      const ENEMY_DATA = {
        // Trash Tier (Waves 1-5)
        drone_swarm: {
          id: "E-01",
          name: "Drone Swarm",
          class: "trash",
          baseHp: 20,
          speed: 3.0,
          attackPattern: "swarm_circle",
          dropRate: 5,
          color: "#666666",
        },
        scout_bot: {
          id: "E-02",
          name: "Scout Bot",
          class: "trash",
          baseHp: 15,
          speed: 4.0,
          attackPattern: "hit_run",
          dropRate: 3,
          color: "#888888",
        },
        repair_drone: {
          id: "E-03",
          name: "Repair Drone",
          class: "support",
          baseHp: 25,
          speed: 2.0,
          attackPattern: "heal_others",
          dropRate: 8,
          color: "#00AA00",
        },

        // Shooter Tier (Waves 3-10)
        shock_trooper: {
          id: "E-04",
          name: "Shock Trooper",
          class: "shooter",
          baseHp: 60,
          speed: 2.4,
          attackPattern: "burst_rifle",
          dropRate: 12,
          color: "#FF4444",
          affixes: ["reflective_bullets", "rapid_fire"],
        },
        plasma_gunner: {
          id: "E-05",
          name: "Plasma Gunner",
          class: "shooter",
          baseHp: 55,
          speed: 2.2,
          attackPattern: "plasma_bolt",
          dropRate: 10,
          color: "#44FF44",
          affixes: ["explosive_rounds", "piercing"],
        },
        sniper_unit: {
          id: "E-06",
          name: "Sniper Unit",
          class: "shooter",
          baseHp: 40,
          speed: 1.8,
          attackPattern: "long_range",
          dropRate: 15,
          color: "#4444FF",
          affixes: ["critical_shots", "stealth"],
        },

        // Assassin Tier (Waves 5-15)
        phase_specter: {
          id: "E-07",
          name: "Phase Specter",
          class: "assassin",
          baseHp: 40,
          speed: 4.2,
          attackPattern: "teleport_stab",
          dropRate: 15,
          color: "#AA44AA",
          affixes: ["invisibility", "poison_blade"],
        },
        shadow_stalker: {
          id: "E-08",
          name: "Shadow Stalker",
          class: "assassin",
          baseHp: 35,
          speed: 4.5,
          attackPattern: "backstab",
          dropRate: 18,
          color: "#222222",
          affixes: ["stealth", "critical_strikes"],
        },
        void_hunter: {
          id: "E-09",
          name: "Void Hunter",
          class: "assassin",
          baseHp: 50,
          speed: 3.8,
          attackPattern: "void_dash",
          dropRate: 20,
          color: "#440044",
          affixes: ["phase_through", "energy_drain"],
        },

        // Heavy Tier (Waves 8-20)
        obliterator_bot: {
          id: "E-10",
          name: "Obliterator Bot",
          class: "heavy",
          baseHp: 200,
          speed: 1.2,
          attackPattern: "laser_sweep",
          dropRate: 20,
          color: "#FF8800",
          affixes: ["explosive_death", "shielded"],
        },
        siege_mech: {
          id: "E-11",
          name: "Siege Mech",
          class: "heavy",
          baseHp: 250,
          speed: 1.0,
          attackPattern: "missile_barrage",
          dropRate: 25,
          color: "#888800",
          affixes: ["armor_plated", "area_damage"],
        },
        titan_crusher: {
          id: "E-12",
          name: "Titan Crusher",
          class: "heavy",
          baseHp: 300,
          speed: 0.8,
          attackPattern: "ground_pound",
          dropRate: 30,
          color: "#AA0000",
          affixes: ["knockback", "damage_reduction"],
        },

        // Elite Variants (Waves 10+)
        elite_commander: {
          id: "E-13",
          name: "Elite Commander",
          class: "elite",
          baseHp: 150,
          speed: 2.5,
          attackPattern: "command_aura",
          dropRate: 35,
          color: "#FFD700",
          affixes: ["buff_allies", "tactical_retreat"],
        },
        cyber_wraith: {
          id: "E-14",
          name: "Cyber Wraith",
          class: "elite",
          baseHp: 120,
          speed: 3.5,
          attackPattern: "digital_storm",
          dropRate: 40,
          color: "#00FFFF",
          affixes: ["emp_burst", "system_hack"],
        },
        quantum_soldier: {
          id: "E-15",
          name: "Quantum Soldier",
          class: "elite",
          baseHp: 180,
          speed: 2.8,
          attackPattern: "quantum_rifle",
          dropRate: 45,
          color: "#FF00FF",
          affixes: ["probability_shield", "quantum_leap"],
        },

        // Specialist Types (Waves 12+)
        medic_drone: {
          id: "E-16",
          name: "Medic Drone",
          class: "support",
          baseHp: 80,
          speed: 2.0,
          attackPattern: "heal_beam",
          dropRate: 15,
          color: "#00FF00",
          affixes: ["mass_heal", "shield_generator"],
        },
        engineer_bot: {
          id: "E-17",
          name: "Engineer Bot",
          class: "support",
          baseHp: 100,
          speed: 1.5,
          attackPattern: "deploy_turret",
          dropRate: 20,
          color: "#FFAA00",
          affixes: ["repair_others", "build_barriers"],
        },
        hacker_unit: {
          id: "E-18",
          name: "Hacker Unit",
          class: "support",
          baseHp: 60,
          speed: 2.5,
          attackPattern: "system_virus",
          dropRate: 25,
          color: "#00AAFF",
          affixes: ["disable_weapons", "corrupt_hud"],
        },

        // Advanced Types (Waves 15+)
        plasma_elemental: {
          id: "E-19",
          name: "Plasma Elemental",
          class: "elemental",
          baseHp: 90,
          speed: 3.0,
          attackPattern: "plasma_wave",
          dropRate: 30,
          color: "#FFFF00",
          affixes: ["fire_immunity", "burning_aura"],
        },
        ice_construct: {
          id: "E-20",
          name: "Ice Construct",
          class: "elemental",
          baseHp: 120,
          speed: 1.8,
          attackPattern: "freeze_ray",
          dropRate: 28,
          color: "#88DDFF",
          affixes: ["slow_enemies", "ice_armor"],
        },
        storm_caller: {
          id: "E-21",
          name: "Storm Caller",
          class: "elemental",
          baseHp: 85,
          speed: 2.2,
          attackPattern: "lightning_bolt",
          dropRate: 32,
          color: "#AAAAFF",
          affixes: ["chain_lightning", "static_field"],
        },

        // Exotic Types (Waves 18+)
        void_spawn: {
          id: "E-22",
          name: "Void Spawn",
          class: "exotic",
          baseHp: 70,
          speed: 4.0,
          attackPattern: "void_burst",
          dropRate: 35,
          color: "#330033",
          affixes: ["phase_shift", "reality_tear"],
        },
        time_distorter: {
          id: "E-23",
          name: "Time Distorter",
          class: "exotic",
          baseHp: 110,
          speed: 2.0,
          attackPattern: "time_slow",
          dropRate: 40,
          color: "#6600CC",
          affixes: ["temporal_shield", "age_weapons"],
        },
        dimension_ripper: {
          id: "E-24",
          name: "Dimension Ripper",
          class: "exotic",
          baseHp: 95,
          speed: 3.2,
          attackPattern: "portal_attack",
          dropRate: 38,
          color: "#CC0066",
          affixes: ["dimensional_door", "reality_warp"],
        },

        // Swarm Types (All waves)
        nano_swarm: {
          id: "E-25",
          name: "Nano Swarm",
          class: "swarm",
          baseHp: 5,
          speed: 5.0,
          attackPattern: "overwhelm",
          dropRate: 2,
          color: "#999999",
          affixes: ["multiply", "collective_mind"],
        },
        micro_hunter: {
          id: "E-26",
          name: "Micro Hunter",
          class: "swarm",
          baseHp: 8,
          speed: 4.5,
          attackPattern: "micro_bite",
          dropRate: 3,
          color: "#666666",
          affixes: ["poison_sting", "swarm_tactics"],
        },

        // Guardian Types (Waves 20+)
        shield_guardian: {
          id: "E-27",
          name: "Shield Guardian",
          class: "guardian",
          baseHp: 400,
          speed: 1.0,
          attackPattern: "shield_bash",
          dropRate: 50,
          color: "#0088FF",
          affixes: ["energy_shield", "reflect_damage"],
        },
        fortress_core: {
          id: "E-28",
          name: "Fortress Core",
          class: "guardian",
          baseHp: 500,
          speed: 0.5,
          attackPattern: "fortress_mode",
          dropRate: 60,
          color: "#888888",
          affixes: ["immobile", "area_denial"],
        },

        // Legendary Types (Waves 25+)
        apex_predator: {
          id: "E-29",
          name: "Apex Predator",
          class: "legendary",
          baseHp: 300,
          speed: 3.5,
          attackPattern: "adaptive_combat",
          dropRate: 75,
          color: "#FF0000",
          affixes: ["learn_patterns", "evolve_abilities"],
        },
        omega_construct: {
          id: "E-30",
          name: "Omega Construct",
          class: "legendary",
          baseHp: 450,
          speed: 2.0,
          attackPattern: "omega_beam",
          dropRate: 80,
          color: "#FFFFFF",
          affixes: ["all_immunities", "perfect_aim"],
        },

        // Boss Minions
        hydra_spawn: {
          id: "E-31",
          name: "Hydra Spawn",
          class: "minion",
          baseHp: 60,
          speed: 2.5,
          attackPattern: "time_echo",
          dropRate: 15,
          color: "#9900CC",
          affixes: ["temporal_duplicate"],
        },
        grave_tendril: {
          id: "E-32",
          name: "Grave Tendril",
          class: "minion",
          baseHp: 80,
          speed: 1.5,
          attackPattern: "bio_lash",
          dropRate: 18,
          color: "#009900",
          affixes: ["regeneration"],
        },
      };

      // NEW: Boss Framework
      const BOSS_DATA = {
        warp_hydra: {
          id: "BOSS-01",
          name: "The Warp Hydra",
          theme: "Time Distortion",
          arenaShape: "hexagon",
          baseHp: 2000,
          phases: [
            {
              hpThreshold: 75,
              name: "Temporal Assault",
              mechanics: ["time_bolt", "chronos_dash"],
              description: "Basic time-based attacks",
            },
            {
              hpThreshold: 50,
              name: "Reality Shift",
              mechanics: ["arena_rotate", "reverse_controls", "time_echo"],
              description: "Arena rotates every 20s, controls reversed",
            },
            {
              hpThreshold: 25,
              name: "Temporal Collapse",
              mechanics: ["time_storm", "duplicate_boss", "reality_tear"],
              description: "Creates temporal duplicates, reality tears",
            },
          ],
          enrageTimer: 240, // 4 minutes
          reward: {
            type: "legendary",
            item: "chrono_core",
            description: "Chrono Core - Time manipulation device",
          },
          taunt: "Time bends to my will, mortal!",
          color: "#9900CC",
        },

        gravemind_core: {
          id: "BOSS-02",
          name: "Gravemind Core",
          theme: "Bio-mechanical Horror",
          arenaShape: "circle",
          baseHp: 2500,
          phases: [
            {
              hpThreshold: 75,
              name: "Awakening",
              mechanics: ["bio_pulse", "spawn_tendrils"],
              description: "Spawns bio-tendrils around arena",
            },
            {
              hpThreshold: 50,
              name: "Infestation",
              mechanics: ["acid_pools", "healing_pods", "mass_spawn"],
              description: "Creates acid pools, healing pods",
            },
            {
              hpThreshold: 25,
              name: "Metamorphosis",
              mechanics: ["bio_storm", "adaptive_armor", "life_drain"],
              description: "Adapts to damage types, drains player health",
            },
          ],
          enrageTimer: 300, // 5 minutes
          reward: {
            type: "upgrade",
            item: "weapon_slot",
            description: "Unlocks +1 weapon slot",
          },
          taunt: "Flesh is weak. I am eternal.",
          color: "#009900",
        },

        void_empress: {
          id: "BOSS-03",
          name: "Void Empress",
          theme: "Dimensional Chaos",
          arenaShape: "octagon",
          baseHp: 3000,
          phases: [
            {
              hpThreshold: 75,
              name: "Void Awakening",
              mechanics: ["void_beam", "dimensional_rift"],
              description: "Opens dimensional rifts",
            },
            {
              hpThreshold: 50,
              name: "Reality Storm",
              mechanics: ["phase_walls", "gravity_wells", "void_spawn"],
              description: "Creates phase walls and gravity wells",
            },
            {
              hpThreshold: 25,
              name: "Dimensional Collapse",
              mechanics: [
                "reality_inversion",
                "void_cascade",
                "dimension_lock",
              ],
              description: "Inverts arena physics, locks dimensions",
            },
          ],
          enrageTimer: 360, // 6 minutes
          reward: {
            type: "legendary",
            item: "void_manipulator",
            description: "Void Manipulator - Reality bending weapon",
          },
          taunt: "Reality is mine to command!",
          color: "#330033",
        },

        quantum_overlord: {
          id: "BOSS-04",
          name: "Quantum Overlord",
          theme: "Probability Control",
          arenaShape: "square",
          baseHp: 3500,
          phases: [
            {
              hpThreshold: 75,
              name: "Quantum State",
              mechanics: ["probability_beam", "quantum_teleport"],
              description: "Manipulates probability fields",
            },
            {
              hpThreshold: 50,
              name: "Superposition",
              mechanics: ["quantum_split", "probability_storm", "phase_shift"],
              description: "Exists in multiple states simultaneously",
            },
            {
              hpThreshold: 25,
              name: "Quantum Collapse",
              mechanics: [
                "reality_anchor",
                "probability_zero",
                "quantum_singularity",
              ],
              description: "Creates quantum singularities",
            },
          ],
          enrageTimer: 420, // 7 minutes
          reward: {
            type: "legendary",
            item: "quantum_processor",
            description: "Quantum Processor - Probability manipulation core",
          },
          taunt: "All possibilities lead to your defeat!",
          color: "#FF00FF",
        },

        stellar_devourer: {
          id: "BOSS-05",
          name: "Stellar Devourer",
          theme: "Cosmic Horror",
          arenaShape: "circle",
          baseHp: 4000,
          phases: [
            {
              hpThreshold: 75,
              name: "Stellar Hunger",
              mechanics: ["solar_flare", "gravity_pulse"],
              description: "Devours light and energy",
            },
            {
              hpThreshold: 50,
              name: "Cosmic Storm",
              mechanics: ["black_hole", "stellar_wind", "energy_drain"],
              description: "Creates black holes and stellar winds",
            },
            {
              hpThreshold: 25,
              name: "Galactic Annihilation",
              mechanics: ["supernova", "space_time_tear", "cosmic_void"],
              description: "Threatens to consume the galaxy",
            },
          ],
          enrageTimer: 480, // 8 minutes
          reward: {
            type: "legendary",
            item: "stellar_core",
            description: "Stellar Core - Harnesses the power of stars",
          },
          taunt: "I have devoured countless worlds!",
          color: "#FFAA00",
        },
      };

      const ACHIEVEMENT_DATA = [
        {
          id: "ach1",
          name: "First Blood",
          description: "Eliminate your first enemy",
          completed: false,
          reward: 100,
        },
        {
          id: "ach2",
          name: "Wave Survivor",
          description: "Complete 5 waves in a single game",
          completed: false,
          reward: 250,
        },
        {
          id: "ach3",
          name: "Arsenal Collector",
          description: "Collect 5 different weapons",
          completed: false,
          reward: 500,
        },
        {
          id: "ach4",
          name: "Boss Slayer",
          description: "Defeat your first boss enemy",
          completed: false,
          reward: 1000,
        },
        {
          id: "ach5",
          name: "Legendary Hunter",
          description: "Equip a legendary item",
          completed: false,
          reward: 2000,
        },
        {
          id: "ach6",
          name: "Centurion",
          description: "Eliminate 100 enemies total",
          completed: false,
          reward: 1500,
        },
        {
          id: "ach7",
          name: "Master of Arms",
          description: "Reach level 25",
          completed: false,
          reward: 3000,
        },
        {
          id: "ach8",
          name: "Full Arsenal",
          description: "Collect all weapons",
          completed: false,
          reward: 5000,
        },
      ];

      // Enemy type data
      const ENEMY_TYPES = [
        {
          type: "grunt",
          health: 100,
          speed: 1.5,
          damage: 10,
          points: 100,
          xp: 20,
          color: "#ff4444",
          size: 10,
          minWave: 1,
        },
        {
          type: "scout",
          health: 75,
          speed: 2.5,
          damage: 5,
          points: 150,
          xp: 25,
          color: "#ff2222",
          size: 8,
          minWave: 1,
        },
        {
          type: "heavy",
          health: 200,
          speed: 1.0,
          damage: 15,
          points: 200,
          xp: 30,
          color: "#ff6666",
          size: 12,
          minWave: 3,
        },
        {
          type: "elite",
          health: 150,
          speed: 2.0,
          damage: 12,
          points: 250,
          xp: 40,
          color: "#ff8800",
          size: 10,
          minWave: 5,
        },
        {
          type: "sniper",
          health: 100,
          speed: 1.2,
          damage: 20,
          points: 300,
          xp: 45,
          color: "#ffaa00",
          size: 9,
          minWave: 8,
        },
        {
          type: "berserker",
          health: 120,
          speed: 3.0,
          damage: 25,
          points: 350,
          xp: 50,
          color: "#ff0000",
          size: 11,
          minWave: 10,
        },
      ];

      // Boss enemy data
      const BOSS_TYPES = [
        {
          type: "juggernaut",
          health: 1000,
          speed: 0.8,
          damage: 30,
          points: 1000,
          xp: 200,
          color: "#880000",
          size: 20,
          abilities: ["shockwave"],
        },
        {
          type: "phantom",
          health: 800,
          speed: 2.0,
          damage: 25,
          points: 1500,
          xp: 250,
          color: "#aa00aa",
          size: 18,
          abilities: ["teleport"],
        },
        {
          type: "devastator",
          health: 1500,
          speed: 0.6,
          damage: 40,
          points: 2000,
          xp: 300,
          color: "#880088",
          size: 25,
          abilities: ["multishot"],
        },
        {
          type: "overlord",
          health: 2000,
          speed: 1.0,
          damage: 50,
          points: 3000,
          xp: 500,
          color: "#ff0088",
          size: 30,
          abilities: ["summon", "shockwave"],
        },
      ];

      // NEW: Puzzle & Event System
      const PUZZLE_TYPES = {
        pressure_plate: {
          name: "Binary Sequence",
          description: "Step on plates in binary order (follow the lights)",
          difficulty: "easy",
          reward: { type: "currency", amount: 200 },
          mechanics: ["binary_sequence", "timed_input"],
        },
        power_coupler: {
          name: "Circuit Flow",
          description: "Rotate nodes to restore power flow",
          difficulty: "medium",
          reward: { type: "keycard", color: "blue" },
          mechanics: ["pipe_connect", "rotation_puzzle"],
        },
        data_terminal: {
          name: "Code Breach",
          description: "Hack the terminal by matching patterns",
          difficulty: "hard",
          reward: { type: "equipment", rarity: "rare" },
          mechanics: ["pattern_match", "time_pressure"],
        },
      };

      const EVENT_TYPES = {
        risk_shrine: {
          name: "Risk/Reward Shrine",
          description: "Sacrifice health or ammo for temporary power",
          options: [
            {
              cost: { type: "health", amount: 25 },
              reward: { type: "damage_boost", duration: 60, multiplier: 1.5 },
            },
            {
              cost: { type: "ammo", amount: 10 },
              reward: { type: "speed_boost", duration: 45, multiplier: 1.3 },
            },
            {
              cost: { type: "currency", amount: 100 },
              reward: { type: "shield", duration: 30, absorption: 50 },
            },
          ],
        },
        secret_vendor: {
          name: "Hidden Merchant",
          description: "Mysterious trader offers rare goods",
          spawnChance: 0.01, // 1% per room
          inventory: [
            { item: "legendary_weapon", price: 2000, stock: 1 },
            { item: "aurite_core", price: 500, stock: 3 },
            { item: "blueprint_shard", price: 300, stock: 5 },
          ],
        },
        lore_terminal: {
          name: "Data Log",
          description: "Ancient records reveal galactic history",
          spawnChance: 0.05, // 5% per room
          reward: { type: "lore_fragment", value: 1 },
          content: [
            "The Warp Hydra was once a guardian of time itself...",
            "Gravemind Core: Patient Zero of the bio-mechanical plague...",
            "The Void Empress rules from between dimensions...",
            "Quantum Overlord exists in all realities simultaneously...",
            "Stellar Devourer: The hunger that consumes galaxies...",
          ],
        },
      };

      // NEW: Dungeon Generation System
      const DUNGEON_GENERATOR = {
        generateDungeon: function (seed, difficulty) {
          const rooms = [];
          const connections = [];

          // Seeded random number generator
          let rng = this.seedRandom(seed);

          // Generate room layout
          const roomCount = Math.floor(8 + difficulty * 2 + rng() * 6);

          // Always start with start_bay
          rooms.push({
            id: 0,
            type: "start_bay",
            x: 0,
            y: 0,
            size: 20,
            enemies: [],
            special: "start",
            visited: false,
          });

          // Generate path to boss
          for (let i = 1; i < roomCount - 1; i++) {
            const roomType = this.selectRoomType(rng, difficulty);
            rooms.push({
              id: i,
              type: roomType,
              x: Math.floor(rng() * 40) - 20,
              y: Math.floor(rng() * 40) - 20,
              size:
                ROOM_TYPES[roomType].minSize +
                Math.floor(
                  rng() *
                    (ROOM_TYPES[roomType].maxSize -
                      ROOM_TYPES[roomType].minSize)
                ),
              enemies: this.generateEnemies(roomType, difficulty, rng),
              special: ROOM_TYPES[roomType].special,
              visited: false,
            });
          }

          // Always end with boss_chamber
          rooms.push({
            id: roomCount - 1,
            type: "boss_chamber",
            x: Math.floor(rng() * 20) + 15,
            y: Math.floor(rng() * 20) + 15,
            size: 35,
            enemies: [],
            special: "boss",
            visited: false,
          });

          // Generate connections
          this.generateConnections(rooms, connections, rng);

          return { rooms, connections, seed };
        },

        seedRandom: function (seed) {
          let m = 0x80000000; // 2**31
          let a = 1103515245;
          let c = 12345;
          let state = seed ? seed : Math.floor(Math.random() * (m - 1));

          return function () {
            state = (a * state + c) % m;
            return state / (m - 1);
          };
        },

        selectRoomType: function (rng, difficulty) {
          const weights = [];
          let totalWeight = 0;

          for (const [type, data] of Object.entries(ROOM_TYPES)) {
            if (type !== "start_bay" && type !== "boss_chamber") {
              const weight = data.weight * (1 + difficulty * 0.1);
              weights.push({ type, weight });
              totalWeight += weight;
            }
          }

          const random = rng() * totalWeight;
          let currentWeight = 0;

          for (const item of weights) {
            currentWeight += item.weight;
            if (random <= currentWeight) {
              return item.type;
            }
          }

          return "combat_hall"; // fallback
        },

        generateEnemies: function (roomType, difficulty, rng) {
          const enemies = [];
          const spawnCount = ROOM_TYPES[roomType].spawns;

          for (let i = 0; i < spawnCount; i++) {
            const enemyType = this.selectEnemyType(difficulty, rng);
            enemies.push({
              type: enemyType,
              x: rng() * 600 + 100,
              y: rng() * 400 + 100,
              spawned: false,
            });
          }

          return enemies;
        },

        selectEnemyType: function (difficulty, rng) {
          const availableEnemies = Object.keys(ENEMY_DATA).filter((key) => {
            const enemy = ENEMY_DATA[key];
            return enemy.class !== "boss" && enemy.class !== "minion";
          });

          return availableEnemies[Math.floor(rng() * availableEnemies.length)];
        },

        generateConnections: function (rooms, connections, rng) {
          // Ensure path from start to boss
          for (let i = 0; i < rooms.length - 1; i++) {
            connections.push({ from: i, to: i + 1, locked: false });
          }

          // Add branch connections (35% chance)
          for (let i = 1; i < rooms.length - 2; i++) {
            if (rng() < 0.35) {
              const target =
                i + 2 + Math.floor(rng() * Math.min(3, rooms.length - i - 2));
              if (target < rooms.length) {
                connections.push({
                  from: i,
                  to: target,
                  locked: rng() < 0.3, // 30% chance for locked door
                  keycard:
                    rng() < 0.5 ? "red" : rng() < 0.5 ? "blue" : "yellow",
                });
              }
            }
          }
        },
      };

      // XP curve for leveling
      function getXpForLevel(level) {
        return Math.floor(100 * Math.pow(1.5, level - 1));
      }

      function levelUp() {
        gameState.level++;
        gameState.xp = 0;
        gameState.xpToNextLevel = getXpForLevel(gameState.level);

        // Award level up bonuses
        gameState.maxHealth += 10;
        gameState.health = gameState.maxHealth; // Full heal on level up
        gameState.currency += 50;

        showNotification(
          `🎉 LEVEL UP! Level ${gameState.level} - +10 Health, +50 Currency`
        );
        playSound(800, 0.3, "sine");

        updateAllDisplays();
      }

      // Get canvas and context
      const canvas = document.getElementById("gameCanvas");
      const ctx = canvas.getContext("2d");

      // Audio context for sound effects
      let audioContext;

      function initAudio() {
        try {
          audioContext = new (window.AudioContext ||
            window.webkitAudioContext)();
        } catch (e) {
          console.log("Audio not supported");
        }
      }

      function playSound(frequency, duration, type = "sine", volume = 0.1) {
        if (!audioContext) return;

        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = type;

        gainNode.gain.setValueAtTime(volume, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(
          0.01,
          audioContext.currentTime + duration
        );

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
      }

      // REBUILT INPUT SYSTEM - GUARANTEED TO WORK
      document.addEventListener("keydown", (e) => {
        gameState.keys[e.code] = true;

        // Special key actions
        if (e.code === "KeyR" && !gameState.reloading) {
          reload();
        }
        if (e.code === "Space") {
          e.preventDefault();
          jump();
        }
        if (e.code === "KeyI") {
          toggleInventory();
        }
        if (e.code === "KeyP") {
          toggleStore();
        }
        if (e.code === "KeyO") {
          toggleAchievements();
        }
        if (e.code === "KeyM") {
          toggleGalacticMap();
        }
        if (e.code === "KeyN") {
          if (gameState.currentDungeon) {
            moveToNextRoom();
          }
        }
      });

      document.addEventListener("keyup", (e) => {
        gameState.keys[e.code] = false;
      });

      // REBUILT MOUSE SYSTEM - GUARANTEED TO WORK
      canvas.addEventListener("mousemove", (e) => {
        const rect = canvas.getBoundingClientRect();
        gameState.mouse.x = e.clientX - rect.left;
        gameState.mouse.y = e.clientY - rect.top;

        // Update player angle to face mouse
        const dx = gameState.mouse.x - gameState.player.x;
        const dy = gameState.mouse.y - gameState.player.y;
        gameState.player.angle = Math.atan2(dy, dx);
      });

      canvas.addEventListener("mousedown", (e) => {
        gameState.mouse.down = true;
        if (!audioContext) initAudio();
        canvas.focus(); // Ensure canvas has focus
      });

      canvas.addEventListener("mouseup", (e) => {
        gameState.mouse.down = false;
      });

      // Prevent context menu
      canvas.addEventListener("contextmenu", (e) => {
        e.preventDefault();
      });

      // Game functions
      function startGame() {
        document.getElementById("startScreen").style.display = "none";
        gameState.running = true;
        gameState.score = 0;
        gameState.wave = 1;
        gameState.health = gameState.maxHealth;
        gameState.ammo = gameState.maxAmmo;
        gameState.reserveAmmo = 120;
        gameState.enemies = [];
        gameState.bullets = [];
        gameState.particles = [];
        gameState.waveEnemiesRemaining = 10;
        gameState.waveEnemiesKilled = 0;
        gameState.totalKills = 0;
        gameState.gameStartTime = Date.now();
        gameState.lastEnemySpawn = Date.now();
        gameState.player.x = canvas.width / 2;
        gameState.player.y = canvas.height / 2;

        // Focus canvas for keyboard input
        canvas.focus();

        // Initialize progression system if first time
        if (gameState.inventory.length === 0) {
          initializeProgressionSystem();
        }

        // Initialize audio context
        if (!audioContext) {
          initAudio();
        }

        updateAllDisplays();

        // Start spawning enemies immediately
        spawnEnemy();
        spawnEnemy();
        spawnEnemy();

        gameLoop();

        console.log(
          "Game started! Use WASD to move, mouse to aim, click to shoot!"
        );
        console.log("Press M to open Galaxy Map for dungeon mode!");
        console.log("Current enemies spawned:", gameState.enemies.length);
      }

      function initializeProgressionSystem() {
        // Initialize achievements
        gameState.achievements = [...ACHIEVEMENT_DATA];

        // Add starter equipment to inventory
        addItemToInventory(EQUIPMENT_DATA.weapons[0]); // Starter pistol
        addItemToInventory(EQUIPMENT_DATA.armor[0]); // Starter armor
        addItemToInventory(EQUIPMENT_DATA.accessories[0]); // Starter accessory

        // Equip starter items
        equipItem(EQUIPMENT_DATA.weapons[0].id);
        equipItem(EQUIPMENT_DATA.armor[0].id);
        equipItem(EQUIPMENT_DATA.accessories[0].id);

        // Apply equipment stats
        applyEquipmentStats();
      }

      function addItemToInventory(item) {
        // Check if item already exists in inventory
        const existingItem = gameState.inventory.find((i) => i.id === item.id);
        if (existingItem) return;

        // Add item to inventory
        gameState.inventory.push({ ...item });

        // Check for collection achievements
        checkCollectionAchievements();
      }

      function equipItem(itemId) {
        const item = gameState.inventory.find((i) => i.id === itemId);
        if (!item) return false;

        // Check level requirement
        if (gameState.level < item.level) {
          showNotification(
            `Level ${item.level} required to equip ${item.name}`
          );
          return false;
        }

        // Equip item
        gameState.equippedItems[item.type] = item;

        // Update equipment slots
        updateEquipmentSlots();

        // Apply equipment stats
        applyEquipmentStats();

        // Check for legendary equipment achievement
        if (item.rarity === "legendary") {
          completeAchievement("ach5");
        }

        return true;
      }

      function applyEquipmentStats() {
        // Reset to base stats
        gameState.maxHealth = 100;
        gameState.maxAmmo = 30;
        gameState.player.speed = 3;
        gameState.fireRate = 150;

        // Apply weapon stats
        const weapon = gameState.equippedItems.weapon;
        if (weapon) {
          gameState.fireRate = weapon.fireRate;
        }

        // Apply armor stats
        const armor = gameState.equippedItems.armor;
        if (armor) {
          gameState.maxHealth += armor.health;

          // Apply special effects
          if (armor.special === "speedBoost") {
            gameState.player.speed += 1;
          } else if (armor.special === "regeneration") {
            // Health regeneration handled in update loop
          }
        }

        // Apply accessory stats
        const accessory = gameState.equippedItems.accessory;
        if (accessory) {
          if (accessory.effect === "ammoCapacity") {
            gameState.maxAmmo += accessory.value;
          } else if (accessory.effect === "moveSpeed") {
            gameState.player.speed *= accessory.value;
          }
        }

        // Apply set bonuses if all equipment is same rarity
        if (
          weapon &&
          armor &&
          accessory &&
          weapon.rarity === armor.rarity &&
          armor.rarity === accessory.rarity
        ) {
          // Set bonus based on rarity
          switch (weapon.rarity) {
            case "uncommon":
              gameState.maxHealth += 25;
              break;
            case "rare":
              gameState.maxHealth += 50;
              gameState.player.speed += 0.5;
              break;
            case "epic":
              gameState.maxHealth += 75;
              gameState.player.speed += 1;
              break;
            case "legendary":
              gameState.maxHealth += 100;
              gameState.player.speed += 1.5;
              // Ammo regeneration handled in update loop
              break;
          }
        }

        // Update health to match new max health
        gameState.health = Math.min(gameState.health, gameState.maxHealth);

        // Update displays
        updateHealthDisplay();
        updateAmmoDisplay();
      }

      function updateEquipmentSlots() {
        // Update weapon slot
        const weaponSlot = document.getElementById("weaponSlot");
        if (gameState.equippedItems.weapon) {
          weaponSlot.textContent = gameState.equippedItems.weapon.icon;
          weaponSlot.className = `equipment-slot weapon rarity-${gameState.equippedItems.weapon.rarity}`;
        } else {
          weaponSlot.textContent = "🔫";
          weaponSlot.className = "equipment-slot weapon";
        }

        // Update armor slot
        const armorSlot = document.getElementById("armorSlot");
        if (gameState.equippedItems.armor) {
          armorSlot.textContent = gameState.equippedItems.armor.icon;
          armorSlot.className = `equipment-slot armor rarity-${gameState.equippedItems.armor.rarity}`;
        } else {
          armorSlot.textContent = "🛡️";
          armorSlot.className = "equipment-slot armor";
        }

        // Update accessory slot
        const accessorySlot = document.getElementById("accessorySlot");
        if (gameState.equippedItems.accessory) {
          accessorySlot.textContent = gameState.equippedItems.accessory.icon;
          accessorySlot.className = `equipment-slot accessory rarity-${gameState.equippedItems.accessory.rarity}`;
        } else {
          accessorySlot.textContent = "💍";
          accessorySlot.className = "equipment-slot accessory";
        }
      }

      function gainXP(amount) {
        gameState.xp += amount;

        // Check for level up
        while (gameState.xp >= gameState.xpToNextLevel) {
          gameState.xp -= gameState.xpToNextLevel;
          gameState.level++;

          // Calculate new XP requirement
          gameState.xpToNextLevel = getXpForLevel(gameState.level);

          // Show level up notification
          showLevelUpNotification();

          // Level up rewards
          gameState.currency += gameState.level * 50;

          // Check level achievements
          if (gameState.level === 25) {
            completeAchievement("ach7");
          }

          playSound(800, 0.5, "sine", 0.2);
        }

        updateXPDisplay();
      }

      function showLevelUpNotification() {
        const notification = document.createElement("div");
        notification.className = "level-up-notification";
        notification.innerHTML = `
                <div>🎉 LEVEL UP! 🎉</div>
                <div>Level ${gameState.level}</div>
                <div>+${gameState.level * 50} 💰</div>
            `;
        document.getElementById("ui").appendChild(notification);

        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 3000);
      }

      function completeAchievement(achievementId) {
        const achievement = gameState.achievements.find(
          (a) => a.id === achievementId
        );
        if (!achievement || achievement.completed) return;

        achievement.completed = true;
        gameState.currency += achievement.reward;

        // Show achievement notification
        showAchievementNotification(achievement);

        playSound(1000, 0.3, "sine", 0.15);
      }

      function showAchievementNotification(achievement) {
        const notification = document.createElement("div");
        notification.className = "achievement-notification";
        notification.innerHTML = `
                <div>🏆 Achievement Unlocked!</div>
                <div>${achievement.name}</div>
                <div>+${achievement.reward} 💰</div>
            `;
        document.getElementById("ui").appendChild(notification);

        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 4000);
      }

      function checkCollectionAchievements() {
        // Check weapon collection
        const weaponCount = gameState.inventory.filter(
          (i) => i.type === "weapon"
        ).length;
        if (weaponCount >= 5) {
          completeAchievement("ach3");
        }
        if (weaponCount >= EQUIPMENT_DATA.weapons.length) {
          completeAchievement("ach8");
        }
      }

      function spawnEnemy() {
        const side = Math.floor(Math.random() * 4);
        let x, y;

        // Spawn from edges of screen
        switch (side) {
          case 0: // Top
            x = Math.random() * canvas.width;
            y = -30;
            break;
          case 1: // Right
            x = canvas.width + 30;
            y = Math.random() * canvas.height;
            break;
          case 2: // Bottom
            x = Math.random() * canvas.width;
            y = canvas.height + 30;
            break;
          case 3: // Left
            x = -30;
            y = Math.random() * canvas.height;
            break;
        }

        // Select enemy type based on wave
        const availableEnemyKeys = Object.keys(ENEMY_DATA).filter((key) => {
          const enemy = ENEMY_DATA[key];
          return enemy.class !== "boss" && enemy.class !== "minion";
        });

        const enemyKey =
          availableEnemyKeys[
            Math.floor(Math.random() * availableEnemyKeys.length)
          ];
        const enemyType = ENEMY_DATA[enemyKey];

        // Scale enemy stats with wave
        const waveMultiplier = 1 + (gameState.wave - 1) * 0.15;

        gameState.enemies.push({
          x: x,
          y: y,
          type: enemyKey,
          health: Math.floor(enemyType.baseHp * waveMultiplier),
          maxHealth: Math.floor(enemyType.baseHp * waveMultiplier),
          speed: enemyType.speed + gameState.wave * 0.05,
          damage: 10 + Math.floor(5 * waveMultiplier), // Base damage
          points: 10 + Math.floor(5 * waveMultiplier),
          xp: 10 + Math.floor(5 * waveMultiplier),
          color: enemyType.color,
          size: 15,
          lastShot: 0,
          fireRate: 1500 + Math.random() * 1000,
          angle: 0,
          hitFlash: 0,
          attackPattern: enemyType.attackPattern,
          dropRate: enemyType.dropRate,
          lastMove: 0,
        });
      }

      function spawnBoss() {
        const bossIndex = Math.min(
          Math.floor(gameState.wave / 5) - 1,
          BOSS_TYPES.length - 1
        );
        const bossType = BOSS_TYPES[bossIndex];

        // Spawn boss in center
        const boss = {
          x: canvas.width / 2,
          y: canvas.height / 2,
          type: bossType.type,
          health: bossType.health,
          maxHealth: bossType.health,
          speed: bossType.speed,
          damage: bossType.damage,
          points: bossType.points,
          xp: bossType.xp,
          color: bossType.color,
          size: bossType.size,
          lastShot: 0,
          fireRate: 800,
          angle: 0,
          hitFlash: 0,
          isBoss: true,
          abilities: [...bossType.abilities],
          lastAbility: 0,
          abilityRate: 3000,
        };

        gameState.enemies.push(boss);

        // Show boss warning
        showBossWarning(bossType.type);
      }

      function showBossWarning(bossType) {
        const warning = document.createElement("div");
        warning.className = "boss-warning";
        warning.innerHTML = `
                <div>⚠️ BOSS INCOMING ⚠️</div>
                <div>${bossType.toUpperCase()}</div>
            `;
        document.getElementById("ui").appendChild(warning);

        setTimeout(() => {
          if (warning.parentNode) {
            warning.remove();
          }
        }, 3000);

        playSound(200, 2.0, "sawtooth", 0.3);
      }

      // UI Functions
      function toggleInventory(filterType = "all") {
        const modal = document.getElementById("inventoryModal");
        if (modal.style.display === "flex") {
          modal.style.display = "none";
        } else {
          modal.style.display = "flex";
          updateInventoryDisplay(filterType);
        }
      }

      function updateInventoryDisplay(filterType = "all") {
        const grid = document.getElementById("inventoryGrid");
        grid.innerHTML = "";

        let filteredItems = gameState.inventory;
        if (filterType !== "all") {
          filteredItems = gameState.inventory.filter(
            (item) => item.type === filterType
          );
        }

        // Create inventory slots
        for (let i = 0; i < 40; i++) {
          const slot = document.createElement("div");
          slot.className = "inventory-slot";

          if (i < filteredItems.length) {
            const item = filteredItems[i];
            slot.textContent = item.icon;
            slot.className += ` rarity-${item.rarity}`;
            slot.onclick = () => selectItem(item);

            // Add tooltip
            slot.onmouseenter = (e) => showItemTooltip(e, item);
            slot.onmouseleave = hideItemTooltip;
          }

          grid.appendChild(slot);
        }
      }

      function selectItem(item) {
        const details = document.getElementById("selectedItemDetails");
        details.innerHTML = `
                <h4>${item.name} (${item.rarity})</h4>
                <p>Level Requirement: ${item.level}</p>
                <p>Type: ${item.type}</p>
                ${getItemStatsHTML(item)}
                <button class="gameButton" onclick="equipItem('${item.id}')">
                    ${
                      gameState.equippedItems[item.type]?.id === item.id
                        ? "Equipped"
                        : "Equip"
                    }
                </button>
            `;
      }

      function getItemStatsHTML(item) {
        let stats = "";

        if (item.type === "weapon") {
          stats += `<p>Damage: ${item.damage}</p>`;
          stats += `<p>Fire Rate: ${item.fireRate}ms</p>`;
          if (item.special) {
            stats += `<p>Special: ${item.special}</p>`;
          }
        } else if (item.type === "armor") {
          stats += `<p>Health Bonus: +${item.health}</p>`;
          if (item.special) {
            stats += `<p>Special: ${item.special}</p>`;
          }
        } else if (item.type === "accessory") {
          stats += `<p>Effect: ${item.effect}</p>`;
          stats += `<p>Value: ${item.value}</p>`;
          if (item.special) {
            stats += `<p>Special: ${item.special}</p>`;
          }
        }

        return stats;
      }

      function showItemTooltip(e, item) {
        const tooltip = document.createElement("div");
        tooltip.className = "item-tooltip";
        tooltip.innerHTML = `
                <strong>${item.name}</strong><br>
                <span style="color: ${getRarityColor(item.rarity)}">${
          item.rarity
        }</span><br>
                Level ${item.level}<br>
                ${getItemStatsHTML(item)}
            `;

        tooltip.style.left = e.pageX + 10 + "px";
        tooltip.style.top = e.pageY + 10 + "px";

        document.body.appendChild(tooltip);
      }

      function hideItemTooltip() {
        const tooltip = document.querySelector(".item-tooltip");
        if (tooltip) {
          tooltip.remove();
        }
      }

      function getRarityColor(rarity) {
        const colors = {
          common: "#808080",
          uncommon: "#00ff00",
          rare: "#0064ff",
          epic: "#8000ff",
          legendary: "#ffa500",
        };
        return colors[rarity] || "#ffffff";
      }

      function filterInventory(type) {
        updateInventoryDisplay(type);
      }

      function toggleStore() {
        const modal = document.getElementById("storeModal");
        if (modal.style.display === "flex") {
          modal.style.display = "none";
        } else {
          modal.style.display = "flex";
          updateStoreDisplay();
        }
      }

      function updateStoreDisplay() {
        // Update weapon store
        const weaponStore = document.getElementById("weaponStore");
        weaponStore.innerHTML = "";
        EQUIPMENT_DATA.weapons.forEach((weapon) => {
          if (weapon.price > 0) {
            // Don't show starter items
            weaponStore.appendChild(createStoreItem(weapon));
          }
        });

        // Update armor store
        const armorStore = document.getElementById("armorStore");
        armorStore.innerHTML = "";
        EQUIPMENT_DATA.armor.forEach((armor) => {
          if (armor.price > 0) {
            armorStore.appendChild(createStoreItem(armor));
          }
        });

        // Update accessory store
        const accessoryStore = document.getElementById("accessoryStore");
        accessoryStore.innerHTML = "";
        EQUIPMENT_DATA.accessories.forEach((accessory) => {
          if (accessory.price > 0) {
            accessoryStore.appendChild(createStoreItem(accessory));
          }
        });
      }

      function createStoreItem(item) {
        const storeItem = document.createElement("div");
        storeItem.className = "store-item";

        const owned = gameState.inventory.some((i) => i.id === item.id);
        const canAfford = gameState.currency >= item.price;
        const levelReq = gameState.level >= item.level;

        storeItem.innerHTML = `
                <div>
                    <span style="color: ${getRarityColor(item.rarity)}">${
          item.icon
        } ${item.name}</span><br>
                    <small>Level ${item.level} | ${item.rarity}</small><br>
                    ${getItemStatsHTML(item)}
                </div>
                <div>
                    <div>💰 ${item.price}</div>
                    <button class="buy-button"
                            onclick="buyItem('${item.id}')"
                            ${
                              owned || !canAfford || !levelReq ? "disabled" : ""
                            }>
                        ${
                          owned
                            ? "Owned"
                            : !levelReq
                            ? `Level ${item.level}`
                            : !canAfford
                            ? "Too Expensive"
                            : "Buy"
                        }
                    </button>
                </div>
            `;

        return storeItem;
      }

      function buyItem(itemId) {
        const item = [
          ...EQUIPMENT_DATA.weapons,
          ...EQUIPMENT_DATA.armor,
          ...EQUIPMENT_DATA.accessories,
        ].find((i) => i.id === itemId);

        if (!item) return;

        // Check requirements
        if (gameState.currency < item.price) {
          showNotification("Not enough currency!");
          return;
        }

        if (gameState.level < item.level) {
          showNotification(`Level ${item.level} required!`);
          return;
        }

        if (gameState.inventory.some((i) => i.id === item.id)) {
          showNotification("Already owned!");
          return;
        }

        // Purchase item
        gameState.currency -= item.price;
        addItemToInventory(item);

        // Update displays
        updateCurrencyDisplay();
        updateStoreDisplay();

        showNotification(`Purchased ${item.name}!`);
        playSound(600, 0.2, "sine", 0.1);
      }

      function toggleAchievements() {
        const modal = document.getElementById("achievementsModal");
        if (modal.style.display === "flex") {
          modal.style.display = "none";
        } else {
          modal.style.display = "flex";
          updateAchievementsDisplay();
        }
      }

      function updateAchievementsDisplay() {
        const list = document.getElementById("achievementsList");
        list.innerHTML = "";

        gameState.achievements.forEach((achievement) => {
          const item = document.createElement("div");
          item.className = `achievement-item ${
            achievement.completed ? "achievement-completed" : ""
          }`;
          item.innerHTML = `
                    <h4>${achievement.completed ? "✅" : "⏳"} ${
            achievement.name
          }</h4>
                    <p>${achievement.description}</p>
                    <p>Reward: ${achievement.reward} 💰</p>
                `;
          list.appendChild(item);
        });

        // Update daily challenge progress
        updateDailyChallengeDisplay();
      }

      function updateDailyChallengeDisplay() {
        const progress = Math.min(
          (gameState.dailyChallenge.progress /
            gameState.dailyChallenge.target) *
            100,
          100
        );
        document.getElementById("dailyChallengeProgress").style.width =
          progress + "%";
      }

      function showNotification(message) {
        const notification = document.createElement("div");
        notification.style.cssText = `
                position: absolute;
                top: 250px;
                right: 20px;
                background: rgba(0,0,0,0.9);
                color: white;
                padding: 10px 15px;
                border-radius: 8px;
                border: 2px solid #ff6b6b;
                font-family: 'Courier New', monospace;
                z-index: 80;
                animation: slideIn 0.3s ease-out;
            `;
        notification.textContent = message;
        document.getElementById("ui").appendChild(notification);

        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 3000);
      }

      // Game mechanics functions
      function jump() {
        if (!gameState.player.isJumping) {
          gameState.player.isJumping = true;
          gameState.player.jumpOffset = 0;

          let jumpHeight = 0;
          const jumpAnimation = setInterval(() => {
            jumpHeight += 2;
            gameState.player.jumpOffset = Math.sin(jumpHeight * 0.2) * 10;

            if (jumpHeight >= Math.PI * 5) {
              gameState.player.jumpOffset = 0;
              gameState.player.isJumping = false;
              clearInterval(jumpAnimation);
            }
          }, 16);

          playSound(300, 0.1, "square");
        }
      }

      function shoot() {
        const now = Date.now();
        // Check if can shoot

        if (
          now - gameState.lastShot < gameState.fireRate ||
          gameState.ammo <= 0 ||
          gameState.reloading
        ) {
          return;
        }

        // Fire bullet
        gameState.lastShot = now;
        gameState.ammo--;

        // Get weapon stats
        const weapon = gameState.equippedItems.weapon;
        let damage = 50; // Base damage
        let special = null;

        if (weapon) {
          damage = weapon.damage;
          special = weapon.special;
        }

        // Create bullet(s)
        if (special === "spread") {
          // Shotgun spread
          for (let i = 0; i < 5; i++) {
            const spreadAngle = gameState.player.angle + (i - 2) * 0.2;
            createBullet(spreadAngle, damage * 0.7);
          }
        } else {
          createBullet(gameState.player.angle, damage);
        }

        // Muzzle flash and effects
        createMuzzleFlash();
        createScreenShake();

        // Sound effect
        playSound(800, 0.05, "sawtooth");

        updateAmmoDisplay();

        // Auto-reload when empty
        if (gameState.ammo === 0 && gameState.reserveAmmo > 0) {
          setTimeout(() => reload(), 300);
        }
      }

      function createBullet(angle, damage) {
        const bullet = {
          x: gameState.player.x + Math.cos(angle) * 25,
          y: gameState.player.y + Math.sin(angle) * 25,
          vx: Math.cos(angle) * 12,
          vy: Math.sin(angle) * 12,
          life: 80,
          damage: damage,
          special: gameState.equippedItems.weapon?.special,
        };
        gameState.bullets.push(bullet);
      }

      function reload() {
        if (
          gameState.reloading ||
          gameState.reserveAmmo === 0 ||
          gameState.ammo === gameState.maxAmmo
        ) {
          return;
        }

        gameState.reloading = true;
        playSound(400, 0.2, "triangle");

        setTimeout(() => {
          const ammoNeeded = gameState.maxAmmo - gameState.ammo;
          const ammoToReload = Math.min(ammoNeeded, gameState.reserveAmmo);

          gameState.ammo += ammoToReload;
          gameState.reserveAmmo -= ammoToReload;
          gameState.reloading = false;

          updateAmmoDisplay();
          playSound(600, 0.1, "triangle");
        }, 1500);
      }

      function createMuzzleFlash() {
        const flash = document.createElement("div");
        flash.className = "muzzleFlash";
        flash.style.left =
          gameState.player.x -
          20 +
          Math.cos(gameState.player.angle) * 20 +
          "px";
        flash.style.top =
          gameState.player.y -
          20 +
          Math.sin(gameState.player.angle) * 20 +
          "px";
        document.getElementById("ui").appendChild(flash);

        setTimeout(() => {
          if (flash.parentNode) {
            flash.remove();
          }
        }, 100);
      }

      function createScreenShake() {
        const gameContainer = document.getElementById("gameContainer");
        const originalTransform = gameContainer.style.transform;

        let shakeIntensity = 2;
        let shakeCount = 0;
        const maxShakes = 5;

        const shakeInterval = setInterval(() => {
          const x = (Math.random() - 0.5) * shakeIntensity;
          const y = (Math.random() - 0.5) * shakeIntensity;
          gameContainer.style.transform = `translate(${x}px, ${y}px)`;

          shakeCount++;
          shakeIntensity *= 0.8;

          if (shakeCount >= maxShakes) {
            gameContainer.style.transform = originalTransform;
            clearInterval(shakeInterval);
          }
        }, 16);
      }

      // Enemy and combat functions
      function updateEnemies() {
        gameState.enemies.forEach((enemy, index) => {
          // Calculate angle to player
          const dx = gameState.player.x - enemy.x;
          const dy = gameState.player.y - enemy.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          enemy.angle = Math.atan2(dy, dx);

          // Move towards player
          if (distance > 0) {
            const moveX = (dx / distance) * enemy.speed;
            const moveY = (dy / distance) * enemy.speed;

            // Add randomness for non-boss enemies
            const randomFactor = enemy.type === "boss" ? 0.1 : 0.3;
            enemy.x += moveX + (Math.random() - 0.5) * randomFactor;
            enemy.y += moveY + (Math.random() - 0.5) * randomFactor;
          }

          // Boss abilities
          if (enemy.type === "boss") {
            const now = Date.now();
            if (
              enemy.mechanics &&
              enemy.mechanics.length > 0 &&
              now - (enemy.lastAbility || 0) > 3000
            ) {
              enemy.lastAbility = now;
              useBossMechanic(enemy);
            }
          }

          // Enemy shooting
          const now = Date.now();
          if (
            distance < 250 &&
            now - enemy.lastShot > (enemy.fireRate || 1000)
          ) {
            enemy.lastShot = now;

            if (distance < 60) {
              // Apply armor damage reduction
              let damage = enemy.damage || 10;
              const armor = gameState.equippedItems.armor;
              if (armor && armor.special === "damageReduction") {
                damage *= 0.7; // 30% damage reduction
              }

              gameState.health -= Math.floor(damage);
              updateHealthDisplay();

              createParticleEffect(
                gameState.player.x,
                gameState.player.y,
                "#ff4444",
                5
              );

              if (gameState.health <= 0) {
                gameOver();
              }
            }

            playSound(200, 0.05, "square");
          }

          // Reduce hit flash
          if (enemy.hitFlash > 0) {
            enemy.hitFlash -= 5;
          }
        });

        // Remove dead enemies and handle drops
        const deadEnemies = gameState.enemies.filter(
          (enemy) => enemy.health <= 0
        );
        deadEnemies.forEach((enemy) => {
          // Handle enemy death
          handleEnemyDeath(enemy);
        });

        gameState.enemies = gameState.enemies.filter(
          (enemy) => enemy.health > 0
        );

        // Check if room is cleared in dungeon mode
        if (
          gameState.currentDungeon &&
          gameState.enemies.length === 0 &&
          gameState.currentRoom
        ) {
          if (gameState.currentRoom.special === "boss") {
            // Boss defeated - complete dungeon
            completeDungeon();
          } else {
            // Room cleared - show next room prompt
            showNotification("Room cleared! Press N to proceed to next room.");
          }
        }
      }

      function useBossMechanic(boss) {
        if (!boss.mechanics || boss.mechanics.length === 0) return;

        const mechanic =
          boss.mechanics[Math.floor(Math.random() * boss.mechanics.length)];

        switch (mechanic) {
          case "time_bolt":
          case "chronos_dash":
          case "void_beam":
          case "bio_pulse":
            // Basic attack - shoot multiple projectiles
            for (let i = 0; i < 5; i++) {
              const angle = boss.angle + (i - 2) * 0.3;
              createEnemyBullet(boss, angle);
            }
            break;

          case "arena_rotate":
            // Visual effect only in this demo
            showNotification("🌀 Arena rotating!");
            createParticleEffect(
              canvas.width / 2,
              canvas.height / 2,
              boss.color,
              30
            );
            break;

          case "reverse_controls":
            // Temporary effect
            showNotification("⚠️ Controls reversed!");
            setTimeout(() => {
              showNotification("Controls restored");
            }, 5000);
            break;

          case "time_storm":
          case "void_cascade":
          case "bio_storm":
            // Area damage
            const playerDist = Math.sqrt(
              Math.pow(gameState.player.x - boss.x, 2) +
                Math.pow(gameState.player.y - boss.y, 2)
            );

            if (playerDist < 200) {
              gameState.health -= 20;
              updateHealthDisplay();
              createParticleEffect(
                gameState.player.x,
                gameState.player.y,
                "#ff0000",
                10
              );
            }

            createParticleEffect(boss.x, boss.y, boss.color, 30);
            break;

          case "duplicate_boss":
          case "spawn_tendrils":
            // Spawn minions
            const minionType =
              boss.bossKey === "warp_hydra" ? "hydra_spawn" : "grave_tendril";
            const enemyData = ENEMY_DATA[minionType];

            if (enemyData) {
              for (let i = 0; i < 2; i++) {
                const minion = {
                  x: boss.x + (Math.random() - 0.5) * 100,
                  y: boss.y + (Math.random() - 0.5) * 100,
                  health: enemyData.baseHp,
                  maxHealth: enemyData.baseHp,
                  speed: enemyData.speed,
                  color: enemyData.color,
                  type: minionType,
                  lastShot: 0,
                  angle: 0,
                  attackPattern: enemyData.attackPattern,
                  dropRate: 0, // Minions don't drop items
                  size: 10,
                  lastMove: 0,
                };

                gameState.enemies.push(minion);
              }
            }
            break;
        }

        playSound(300, 0.3, "sawtooth");
      }

      function handleEnemyDeath(enemy) {
        // Award XP and currency
        let xpGain = 10;
        let currencyGain = 5;

        // Bonus for elite/boss enemies
        if (enemy.type === "boss") {
          xpGain = 500;
          currencyGain = 200;
        } else if (enemy.size > 15) {
          // Elite enemy
          xpGain = 25;
          currencyGain = 15;
        }

        gameState.xp += xpGain;
        gameState.currency += currencyGain;
        gameState.totalKills++;

        // Check for level up
        if (gameState.xp >= gameState.xpToNextLevel) {
          levelUp();
        }

        // Check for item drop
        if (Math.random() * 100 < (enemy.dropRate || 10)) {
          const item = generateRandomItem();
          if (item) {
            addItemToInventory(item);
            showNotification(`Found: ${item.name}!`);
          }
        }

        // Update daily challenge
        if (
          gameState.dailyChallenge.type === "kills" &&
          !gameState.dailyChallenge.completed
        ) {
          gameState.dailyChallenge.progress++;
          if (
            gameState.dailyChallenge.progress >= gameState.dailyChallenge.target
          ) {
            gameState.dailyChallenge.completed = true;
            gameState.currency += 500;
            showNotification("Daily Challenge Complete! +500 💰");
          }
        }

        updateAllDisplays();
      }

      function generateRandomItem(forceRarity = null) {
        const rarities = ["common", "uncommon", "rare", "epic", "legendary"];
        const rarityWeights = [50, 30, 15, 4, 1]; // Percentage chances

        let selectedRarity = forceRarity;

        if (!selectedRarity) {
          const random = Math.random() * 100;
          let cumulative = 0;

          for (let i = 0; i < rarities.length; i++) {
            cumulative += rarityWeights[i];
            if (random <= cumulative) {
              selectedRarity = rarities[i];
              break;
            }
          }
        }

        // Get all items of selected rarity
        const allItems = [
          ...EQUIPMENT_DATA.weapons,
          ...EQUIPMENT_DATA.armor,
          ...EQUIPMENT_DATA.accessories,
        ];

        const itemsOfRarity = allItems.filter(
          (item) => item.rarity === selectedRarity
        );

        if (itemsOfRarity.length === 0) {
          return null;
        }

        return itemsOfRarity[Math.floor(Math.random() * itemsOfRarity.length)];
      }

      function createEnemyBullet(enemy, angle = null) {
        const bulletAngle = angle !== null ? angle : enemy.angle;

        const bullet = {
          x: enemy.x,
          y: enemy.y,
          vx: Math.cos(bulletAngle) * 4,
          vy: Math.sin(bulletAngle) * 4,
          color: "#ff4444",
          size: 3,
          isEnemy: true,
        };

        gameState.bullets.push(bullet);
      }

      function useBossAbility(boss) {
        const ability =
          boss.abilities[Math.floor(Math.random() * boss.abilities.length)];

        switch (ability) {
          case "shockwave":
            // Create shockwave effect
            createParticleEffect(boss.x, boss.y, "#ff0000", 20);

            // Damage player if close
            const dx = gameState.player.x - boss.x;
            const dy = gameState.player.y - boss.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 150) {
              gameState.health -= 30;
              updateHealthDisplay();
            }

            playSound(100, 0.5, "sawtooth", 0.2);
            break;

          case "teleport":
            // Teleport to random position
            boss.x = 100 + Math.random() * (canvas.width - 200);
            boss.y = 100 + Math.random() * (canvas.height - 200);
            createParticleEffect(boss.x, boss.y, "#aa00aa", 15);
            playSound(500, 0.3, "sine", 0.15);
            break;

          case "multishot":
            // Fire multiple projectiles (simplified as area damage)
            for (let i = 0; i < 8; i++) {
              const angle = ((Math.PI * 2) / 8) * i;
              const x = boss.x + Math.cos(angle) * 50;
              const y = boss.y + Math.sin(angle) * 50;
              createParticleEffect(x, y, "#ff8800", 5);
            }
            playSound(300, 0.2, "square", 0.1);
            break;

          case "summon":
            // Spawn additional enemies
            for (let i = 0; i < 2; i++) {
              spawnEnemy();
            }
            playSound(150, 0.4, "triangle", 0.1);
            break;
        }
      }

      function updateBullets() {
        gameState.bullets.forEach((bullet, bulletIndex) => {
          bullet.x += bullet.vx;
          bullet.y += bullet.vy;
          bullet.life--;

          // Remove bullets that are off-screen or expired
          if (
            bullet.x < -50 ||
            bullet.x > canvas.width + 50 ||
            bullet.y < -50 ||
            bullet.y > canvas.height + 50 ||
            bullet.life <= 0
          ) {
            gameState.bullets.splice(bulletIndex, 1);
            return;
          }

          // Check collision with enemies
          gameState.enemies.forEach((enemy, enemyIndex) => {
            const dx = bullet.x - enemy.x;
            const dy = bullet.y - enemy.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < enemy.size + 5) {
              // Calculate damage
              let damage = bullet.damage;

              // Critical hit chance from accessory
              const accessory = gameState.equippedItems.accessory;
              if (accessory && accessory.effect === "critChance") {
                if (Math.random() * 100 < accessory.value) {
                  damage *= 2; // Critical hit
                  showDamageNumber(enemy.x, enemy.y, damage, true);
                }
              }

              // Apply damage
              enemy.health -= damage;
              enemy.hitFlash = 255;

              // Handle piercing bullets
              if (bullet.special !== "piercing") {
                gameState.bullets.splice(bulletIndex, 1);
              }

              // Create hit effects
              createParticleEffect(enemy.x, enemy.y, "#ffff00", 8);
              if (!showDamageNumber.called) {
                showDamageNumber(enemy.x, enemy.y, damage, false);
              }

              playSound(600, 0.03, "square");

              if (enemy.health <= 0) {
                // Enemy killed - mark for removal, will be handled in updateEnemies
                enemy.health = 0;
              }
            }
          });
        });
      }

      function handleEnemyKill(enemy, enemyIndex) {
        // Create explosion and particles
        createExplosion(enemy.x, enemy.y);
        createParticleEffect(enemy.x, enemy.y, enemy.color, 15);

        // Award points, XP, and currency
        gameState.score += enemy.points;
        gainXP(enemy.xp);
        gameState.currency += Math.floor(enemy.points / 10);

        // Update counters
        gameState.waveEnemiesKilled++;
        gameState.totalKills++;
        gameState.dailyChallenge.progress++;

        // Check achievements
        if (gameState.totalKills === 1) {
          completeAchievement("ach1");
        }
        if (gameState.totalKills >= 100) {
          completeAchievement("ach6");
        }
        if (enemy.isBoss) {
          completeAchievement("ach4");
        }

        // Random equipment drop
        if (Math.random() < 0.1) {
          // 10% drop chance
          dropRandomEquipment(enemy.x, enemy.y);
        }

        // Remove enemy
        gameState.enemies.splice(enemyIndex, 1);

        updateAllDisplays();
        playSound(150, 0.2, "sawtooth");

        // Check if wave complete
        if (gameState.waveEnemiesKilled >= gameState.waveEnemiesRemaining) {
          nextWave();
        }
      }

      function showDamageNumber(x, y, damage, isCrit) {
        const damageNum = document.createElement("div");
        damageNum.className = "damage-number";
        damageNum.textContent = damage;
        damageNum.style.left = x + "px";
        damageNum.style.top = y + "px";

        if (isCrit) {
          damageNum.style.color = "#ff0000";
          damageNum.style.fontSize = "20px";
          damageNum.textContent = "CRIT " + damage;
        }

        document.getElementById("ui").appendChild(damageNum);

        setTimeout(() => {
          if (damageNum.parentNode) {
            damageNum.remove();
          }
        }, 1000);
      }

      function dropRandomEquipment(x, y) {
        // Determine rarity based on wave
        let rarity = "common";
        const rand = Math.random();

        if (gameState.wave >= 10) {
          if (rand < 0.05) rarity = "legendary";
          else if (rand < 0.15) rarity = "epic";
          else if (rand < 0.35) rarity = "rare";
          else if (rand < 0.65) rarity = "uncommon";
        } else if (gameState.wave >= 5) {
          if (rand < 0.1) rarity = "epic";
          else if (rand < 0.3) rarity = "rare";
          else if (rand < 0.6) rarity = "uncommon";
        } else {
          if (rand < 0.2) rarity = "rare";
          else if (rand < 0.5) rarity = "uncommon";
        }

        // Select random equipment of that rarity
        const allEquipment = [
          ...EQUIPMENT_DATA.weapons,
          ...EQUIPMENT_DATA.armor,
          ...EQUIPMENT_DATA.accessories,
        ];
        const rarityItems = allEquipment.filter(
          (item) => item.rarity === rarity && item.level <= gameState.level + 5
        );

        if (rarityItems.length > 0) {
          const item =
            rarityItems[Math.floor(Math.random() * rarityItems.length)];
          addItemToInventory(item);

          // Show pickup notification
          showNotification(`Found ${item.name}!`);

          // Visual effect
          createParticleEffect(x, y, getRarityColor(rarity), 10);
        }
      }

      function createParticleEffect(x, y, color, count) {
        for (let i = 0; i < count; i++) {
          gameState.particles.push({
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * 8,
            vy: (Math.random() - 0.5) * 8,
            life: 30 + Math.random() * 20,
            maxLife: 50,
            color: color,
            size: 2 + Math.random() * 3,
          });
        }
      }

      function updateParticles() {
        gameState.particles.forEach((particle, index) => {
          particle.x += particle.vx;
          particle.y += particle.vy;
          particle.vx *= 0.98;
          particle.vy *= 0.98;
          particle.life--;

          if (particle.life <= 0) {
            gameState.particles.splice(index, 1);
          }
        });
      }

      function createExplosion(x, y) {
        const explosion = document.createElement("div");
        explosion.className = "explosion";
        explosion.style.left = x - 15 + "px";
        explosion.style.top = y - 15 + "px";
        document.getElementById("ui").appendChild(explosion);

        setTimeout(() => {
          if (explosion.parentNode) {
            explosion.remove();
          }
        }, 300);
      }

      function nextWave() {
        gameState.wave++;
        gameState.waveEnemiesRemaining = 8 + gameState.wave * 4;
        gameState.waveEnemiesKilled = 0;
        gameState.enemySpawnRate = Math.max(
          800,
          gameState.enemySpawnRate - 100
        );

        // Spawn boss every 5 waves
        if (gameState.wave % 5 === 0) {
          setTimeout(() => spawnBoss(), 2000);
        }

        // Wave completion rewards
        const waveBonus = gameState.wave * 100;
        gameState.currency += waveBonus;
        gameState.reserveAmmo += 60;
        gameState.health = Math.min(gameState.maxHealth, gameState.health + 30);
        gainXP(gameState.wave * 25);

        // Check wave achievements
        if (gameState.wave >= 5) {
          completeAchievement("ach2");
        }

        updateAllDisplays();
        playSound(800, 0.3, "sine");

        showNotification(`Wave ${gameState.wave} - Bonus: ${waveBonus} 💰`);
      }

      function updatePlayer() {
        if (!gameState.running) return;

        const speed = gameState.player.speed;

        // WASD Movement - FIXED
        if (gameState.keys["KeyW"] === true) {
          gameState.player.y = Math.max(20, gameState.player.y - speed);
        }
        if (gameState.keys["KeyS"] === true) {
          gameState.player.y = Math.min(
            canvas.height - 20,
            gameState.player.y + speed
          );
        }
        if (gameState.keys["KeyA"] === true) {
          gameState.player.x = Math.max(20, gameState.player.x - speed);
        }
        if (gameState.keys["KeyD"] === true) {
          gameState.player.x = Math.min(
            canvas.width - 20,
            gameState.player.x + speed
          );
        }

        // Arrow key movement as backup
        if (gameState.keys["ArrowUp"] === true) {
          gameState.player.y = Math.max(20, gameState.player.y - speed);
        }
        if (gameState.keys["ArrowDown"] === true) {
          gameState.player.y = Math.min(
            canvas.height - 20,
            gameState.player.y + speed
          );
        }
        if (gameState.keys["ArrowLeft"] === true) {
          gameState.player.x = Math.max(20, gameState.player.x - speed);
        }
        if (gameState.keys["ArrowRight"] === true) {
          gameState.player.x = Math.min(
            canvas.width - 20,
            gameState.player.x + speed
          );
        }

        // Mouse shooting - FIXED
        if (gameState.mouse.down === true) {
          shoot();
        }

        // Health regeneration from legendary armor
        const armor = gameState.equippedItems.armor;
        if (armor && armor.special === "regeneration") {
          if (Math.random() < 0.01) {
            // 1% chance per frame
            gameState.health = Math.min(
              gameState.maxHealth,
              gameState.health + 1
            );
            updateHealthDisplay();
          }
        }

        // Ammo regeneration from legendary set bonus
        const weapon = gameState.equippedItems.weapon;
        const accessory = gameState.equippedItems.accessory;
        if (
          weapon &&
          armor &&
          accessory &&
          weapon.rarity === "legendary" &&
          armor.rarity === "legendary" &&
          accessory.rarity === "legendary"
        ) {
          if (Math.random() < 0.005) {
            // 0.5% chance per frame
            gameState.ammo = Math.min(gameState.maxAmmo, gameState.ammo + 1);
            updateAmmoDisplay();
          }
        }
      }

      function updateFPS() {
        gameState.frameCount++;
        const now = Date.now();

        if (now - gameState.lastFpsUpdate >= 1000) {
          gameState.fps = gameState.frameCount;
          gameState.frameCount = 0;
          gameState.lastFpsUpdate = now;

          document.getElementById("fps").textContent = `FPS: ${gameState.fps}`;
        }
      }

      function render() {
        // Clear canvas with fade effect
        ctx.fillStyle = "rgba(10, 10, 10, 0.15)";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Debug: Show that render is being called
        if (Math.random() < 0.01) console.log("Rendering frame");

        // Draw animated grid background
        const time = Date.now() * 0.001;
        ctx.strokeStyle = `rgba(255, 255, 255, ${
          0.05 + Math.sin(time) * 0.02
        })`;
        ctx.lineWidth = 1;

        const gridSize = 40;
        for (let x = 0; x < canvas.width; x += gridSize) {
          ctx.beginPath();
          ctx.moveTo(x, 0);
          ctx.lineTo(x, canvas.height);
          ctx.stroke();
        }
        for (let y = 0; y < canvas.height; y += gridSize) {
          ctx.beginPath();
          ctx.moveTo(0, y);
          ctx.lineTo(canvas.width, y);
          ctx.stroke();
        }

        // Draw particles
        gameState.particles.forEach((particle) => {
          const alpha = particle.life / particle.maxLife;
          ctx.fillStyle =
            particle.color +
            Math.floor(alpha * 255)
              .toString(16)
              .padStart(2, "0");
          ctx.beginPath();
          ctx.arc(
            particle.x,
            particle.y,
            particle.size * alpha,
            0,
            Math.PI * 2
          );
          ctx.fill();
        });

        // Draw player
        ctx.save();
        ctx.translate(
          gameState.player.x,
          gameState.player.y - gameState.player.jumpOffset
        );
        ctx.rotate(gameState.player.angle);

        // Player body (with health-based color)
        const healthRatio = gameState.health / gameState.maxHealth;
        const playerColor = `rgb(${255 - healthRatio * 179}, ${
          76 + healthRatio * 179
        }, 80)`;
        ctx.fillStyle = playerColor;
        ctx.fillRect(-12, -10, 24, 20);

        // Player weapon
        ctx.fillStyle = "#888";
        ctx.fillRect(12, -3, 30, 6);

        // Weapon barrel
        ctx.fillStyle = "#444";
        ctx.fillRect(35, -1, 8, 2);

        ctx.restore();

        // Draw player health indicator
        if (gameState.health < gameState.maxHealth * 0.3) {
          ctx.strokeStyle = "#ff4444";
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.arc(
            gameState.player.x,
            gameState.player.y - gameState.player.jumpOffset,
            20,
            0,
            Math.PI * 2
          );
          ctx.stroke();
        }

        // Draw enemies
        gameState.enemies.forEach((enemy) => {
          ctx.save();
          ctx.translate(enemy.x, enemy.y);
          ctx.rotate(enemy.angle);

          // Enemy body with hit flash effect
          if (enemy.hitFlash > 0) {
            ctx.fillStyle = `rgb(255, ${255 - enemy.hitFlash}, ${
              255 - enemy.hitFlash
            })`;
          } else {
            ctx.fillStyle = enemy.color;
          }

          ctx.beginPath();
          ctx.arc(0, 0, enemy.size, 0, Math.PI * 2);
          ctx.fill();

          // Boss indicator
          if (enemy.type === "boss" || enemy.size > 30) {
            ctx.strokeStyle = "#ff0000";
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(0, 0, enemy.size + 5, 0, Math.PI * 2);
            ctx.stroke();
          }

          // Enemy weapon
          ctx.fillStyle = "#666";
          ctx.fillRect(enemy.size, -2, 15, 4);

          ctx.restore();

          // Enemy health bar
          if (enemy.health < enemy.maxHealth) {
            const barWidth = enemy.size * 2;
            const barHeight = 3;
            const barY = enemy.y - enemy.size - 8;

            ctx.fillStyle = "rgba(0, 0, 0, 0.7)";
            ctx.fillRect(enemy.x - barWidth / 2, barY, barWidth, barHeight);

            ctx.fillStyle = "#ff6b6b";
            const healthWidth = (enemy.health / enemy.maxHealth) * barWidth;
            ctx.fillRect(enemy.x - barWidth / 2, barY, healthWidth, barHeight);
          }
        });

        // Draw bullets
        gameState.bullets.forEach((bullet) => {
          // Special bullet effects
          let bulletColor = "#ffff00";
          let bulletSize = 2;

          if (bullet.special === "explosive") {
            bulletColor = "#ff8800";
            bulletSize = 3;
          } else if (bullet.special === "piercing") {
            bulletColor = "#00ffff";
            bulletSize = 2;
          }

          // Bullet glow effect
          const gradient = ctx.createRadialGradient(
            bullet.x,
            bullet.y,
            0,
            bullet.x,
            bullet.y,
            8
          );
          gradient.addColorStop(0, bulletColor);
          gradient.addColorStop(1, "rgba(255, 255, 0, 0)");

          ctx.fillStyle = gradient;
          ctx.beginPath();
          ctx.arc(bullet.x, bullet.y, 4, 0, Math.PI * 2);
          ctx.fill();

          // Bullet core
          ctx.fillStyle = "#ffffff";
          ctx.beginPath();
          ctx.arc(bullet.x, bullet.y, bulletSize, 0, Math.PI * 2);
          ctx.fill();

          // Bullet trail
          ctx.strokeStyle = "rgba(255, 255, 0, 0.6)";
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.moveTo(bullet.x, bullet.y);
          ctx.lineTo(bullet.x - bullet.vx * 4, bullet.y - bullet.vy * 4);
          ctx.stroke();
        });

        // Draw minimap
        drawMinimap();
      }

      function drawMinimap() {
        const minimap = document.getElementById("minimap");
        const minimapCanvas = document.createElement("canvas");
        minimapCanvas.width = 150;
        minimapCanvas.height = 150;
        const minimapCtx = minimapCanvas.getContext("2d");

        // Clear minimap
        minimapCtx.fillStyle = "rgba(0, 0, 0, 0.8)";
        minimapCtx.fillRect(0, 0, 150, 150);

        // Scale factors
        const scaleX = 150 / canvas.width;
        const scaleY = 150 / canvas.height;

        // Draw player
        minimapCtx.fillStyle = "#4CAF50";
        minimapCtx.beginPath();
        minimapCtx.arc(
          gameState.player.x * scaleX,
          gameState.player.y * scaleY,
          3,
          0,
          Math.PI * 2
        );
        minimapCtx.fill();

        // Draw enemies
        gameState.enemies.forEach((enemy) => {
          // Boss enemies are larger and red
          if (enemy.type === "boss" || enemy.size > 30) {
            minimapCtx.fillStyle = "#ff0000";
            minimapCtx.beginPath();
            minimapCtx.arc(
              enemy.x * scaleX,
              enemy.y * scaleY,
              4,
              0,
              Math.PI * 2
            );
            minimapCtx.fill();
          } else {
            minimapCtx.fillStyle = "#ff4444";
            minimapCtx.beginPath();
            minimapCtx.arc(
              enemy.x * scaleX,
              enemy.y * scaleY,
              2,
              0,
              Math.PI * 2
            );
            minimapCtx.fill();
          }
        });

        // Update minimap display
        minimap.style.backgroundImage = `url(${minimapCanvas.toDataURL()})`;
        minimap.style.backgroundSize = "cover";
      }

      // Display update functions
      function updateAmmoDisplay() {
        const ammoText = gameState.reloading
          ? `${gameState.ammo} / ${gameState.reserveAmmo} (Reloading...)`
          : `${gameState.ammo} / ${gameState.reserveAmmo}`;
        document.getElementById("ammoCounter").textContent = ammoText;

        // Color coding for low ammo
        const ammoCounter = document.getElementById("ammoCounter");
        if (gameState.ammo <= 5) {
          ammoCounter.style.color = "#ff4444";
        } else if (gameState.ammo <= 10) {
          ammoCounter.style.color = "#ffaa44";
        } else {
          ammoCounter.style.color = "white";
        }
      }

      function updateHealthDisplay() {
        const healthPercentage = (gameState.health / gameState.maxHealth) * 100;
        document.getElementById(
          "healthFill"
        ).style.width = `${healthPercentage}%`;

        // Color coding for health
        const healthFill = document.getElementById("healthFill");
        if (healthPercentage < 25) {
          healthFill.style.background =
            "linear-gradient(90deg, #f44336 0%, #ff5722 100%)";
        } else if (healthPercentage < 50) {
          healthFill.style.background =
            "linear-gradient(90deg, #ff9800 0%, #ffc107 100%)";
        } else {
          healthFill.style.background =
            "linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%)";
        }
      }

      function updateScoreDisplay() {
        document.getElementById(
          "score"
        ).textContent = `Score: ${gameState.score.toLocaleString()}`;
      }

      function updateWaveDisplay() {
        document.getElementById("wave").textContent = `Wave: ${gameState.wave}`;
      }

      function updateXPDisplay() {
        document.getElementById("levelValue").textContent = gameState.level;
        document.getElementById("xpValue").textContent = gameState.xp;
        document.getElementById("xpNextLevel").textContent =
          gameState.xpToNextLevel;

        const xpPercentage = (gameState.xp / gameState.xpToNextLevel) * 100;
        document.getElementById("xpFill").style.width = `${xpPercentage}%`;
      }

      function updateCurrencyDisplay() {
        document.getElementById("currencyValue").textContent =
          gameState.currency.toLocaleString();
      }

      function updateAllDisplays() {
        updateAmmoDisplay();
        updateHealthDisplay();
        updateScoreDisplay();
        updateWaveDisplay();
        updateXPDisplay();
        updateCurrencyDisplay();
        updateEquipmentSlots();
        updateDailyChallengeDisplay();
      }

      function gameOver() {
        gameState.running = false;

        // Calculate final stats
        const gameTime = (Date.now() - gameState.gameStartTime) / 1000;

        // Show game over screen (simplified)
        alert(`Game Over!

Final Stats:
Score: ${gameState.score.toLocaleString()}
Level: ${gameState.level}
Waves Survived: ${gameState.wave}
Enemies Eliminated: ${gameState.totalKills}
Currency Earned: ${gameState.currency}
Time Played: ${Math.floor(gameTime)}s

Thanks for playing Galaxy Guns 3D!`);

        // Reset to start screen
        document.getElementById("startScreen").style.display = "flex";

        playSound(100, 1.0, "sawtooth");
      }

      function gameLoop() {
        if (!gameState.running) return;

        // Game loop active

        // Update game systems
        updatePlayer();
        updateEnemies();
        updateBullets();
        updateParticles();
        updateFPS();
        updateBossHUD();

        // Spawn enemies based on wave progression
        const now = Date.now();
        const maxEnemies = Math.min(25, 8 + gameState.wave * 2);

        if (
          now - gameState.lastEnemySpawn > gameState.enemySpawnRate &&
          gameState.enemies.length < maxEnemies
        ) {
          spawnEnemy();
          gameState.lastEnemySpawn = now;
        }

        // Render everything
        render();

        // Continue game loop
        requestAnimationFrame(gameLoop);
      }

      // Control functions
      function showControls() {
        alert(`🎮 GALAXY GUNS 3D - CONTROLS & FEATURES

🖱️ MOUSE CONTROLS:
• Move mouse: Aim and look around
• Left click: Fire weapon
• Right click: (Reserved for future features)

⌨️ KEYBOARD CONTROLS:
• W, A, S, D: Move player
• R: Reload weapon
• Space: Jump (visual effect)
• I: Open/close inventory
• P: Open/close store
• O: Open/close achievements
• M: Open/close galaxy map
• N: Next room (in dungeons)

🎯 PROGRESSION SYSTEM:
• Gain XP by eliminating enemies and completing waves
• Level up to unlock new equipment
• Collect currency to purchase better gear
• Find rare equipment drops from enemies
• Complete achievements for bonus rewards

🛡️ EQUIPMENT SYSTEM:
• Weapons: Affect damage, fire rate, and special abilities
• Armor: Increases health and provides special effects
• Accessories: Enhance various player abilities
• Rarity tiers: Common → Uncommon → Rare → Epic → Legendary
• Set bonuses for wearing matching rarity equipment

⚔️ COMBAT FEATURES:
• Different enemy types with unique behaviors
• Boss enemies every 5 waves with special abilities
• Critical hits and special weapon effects
• Difficulty scaling with each wave
• Equipment synergies and special effects

🏆 ACHIEVEMENTS & CHALLENGES:
• Daily challenges for bonus rewards
• Permanent achievements with currency rewards
• Collection goals and progression milestones

Good luck, soldier! 🎖️`);
      }

      function showAbout() {
        alert(`🚀 GALAXY GUNS 3D - COMPLETE PROGRESSION SYSTEM

This enhanced version showcases a full RPG-style progression system for the mobile FPS game.

🎮 NEW FEATURES:
• Complete equipment system with 15 unique items
• Level-based progression (1-50) with XP requirements
• Rarity-based loot system with visual indicators
• Equipment stats affecting gameplay mechanics
• Set bonuses for wearing matching rarity gear
• Boss enemies with unique abilities every 5 waves
• Achievement system with permanent rewards
• Daily challenges for bonus currency
• Store system for purchasing equipment
• Inventory management with filtering
• Critical hits and special weapon effects

🌌 EXPANDED FEATURES:
• Multi-room procedural dungeons with distinct biomes
• Galactic sector map with progression system
• 30+ enemy types with scaling AI and abilities
• 5 unique bosses with phases and mechanics
• Puzzles, events, and environmental hazards
• Keycards and locked doors for exploration
• Meta-progression with permanent upgrades

📊 PROGRESSION MECHANICS:
• XP gained from kills, wave completion, and achievements
• Currency earned from combat and achievements
• Equipment drops with rarity-based probabilities
• Level gates preventing overpowered early equipment
• Difficulty scaling with wave progression
• Equipment synergies and special effects
• NavKey fragments to unlock new sectors

🛠️ TECHNICAL IMPLEMENTATION:
• Comprehensive data-driven equipment system
• Dynamic UI generation for inventory and store
• Real-time stat calculations and applications
• Achievement tracking and notification system
• Save/load progression (localStorage integration)
• Performance optimized for 60fps gameplay
• Procedural dungeon generation algorithm

This represents the complete progression system that would be found in a premium mobile FPS game, demonstrating advanced game design and implementation techniques.

Developed by the Galaxy Guns 3D Team 🎖️`);
      }

      // NEW: Galactic Map Functions
      function toggleGalacticMap() {
        const mapElement = document.getElementById("galacticMap");

        if (mapElement.style.display === "block") {
          mapElement.style.display = "none";
          gameState.mapOpen = false;
        } else {
          mapElement.style.display = "block";
          gameState.mapOpen = true;
          renderGalacticMap();
          updateMapHUD();
        }
      }

      function renderGalacticMap() {
        const canvas = document.getElementById("mapCanvas");
        const ctx = canvas.getContext("2d");

        // Clear canvas
        ctx.fillStyle = "#0a0a1a";
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw starfield background
        drawStarfield(ctx, canvas.width, canvas.height);

        // Draw connections between sectors
        drawSectorConnections(ctx);

        // Draw sectors
        drawSectors(ctx);
      }

      function drawStarfield(ctx, width, height) {
        // Draw small stars
        for (let i = 0; i < 200; i++) {
          const x = Math.random() * width;
          const y = Math.random() * height;
          const size = Math.random() * 1.5;
          const opacity = Math.random() * 0.8 + 0.2;

          ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`;
          ctx.beginPath();
          ctx.arc(x, y, size, 0, Math.PI * 2);
          ctx.fill();
        }

        // Draw a few larger stars
        for (let i = 0; i < 30; i++) {
          const x = Math.random() * width;
          const y = Math.random() * height;
          const size = Math.random() * 2 + 1;

          ctx.fillStyle = "#FFFFFF";
          ctx.beginPath();
          ctx.arc(x, y, size, 0, Math.PI * 2);
          ctx.fill();

          // Add glow
          const glow = ctx.createRadialGradient(x, y, 0, x, y, size * 3);
          glow.addColorStop(0, "rgba(255, 255, 255, 0.3)");
          glow.addColorStop(1, "rgba(255, 255, 255, 0)");

          ctx.fillStyle = glow;
          ctx.beginPath();
          ctx.arc(x, y, size * 3, 0, Math.PI * 2);
          ctx.fill();
        }
      }

      function drawSectorConnections(ctx) {
        ctx.strokeStyle = "rgba(0, 255, 153, 0.3)";
        ctx.lineWidth = 2;

        GALACTIC_MAP.sectors.forEach((sector) => {
          if (sector.connections) {
            sector.connections.forEach((connectionId) => {
              const connectedSector = GALACTIC_MAP.sectors.find(
                (s) => s.id === connectionId
              );
              if (connectedSector) {
                // Draw line between sectors
                ctx.beginPath();
                ctx.moveTo(sector.x, sector.y);
                ctx.lineTo(connectedSector.x, connectedSector.y);
                ctx.stroke();
              }
            });
          }
        });
      }

      function drawSectors(ctx) {
        GALACTIC_MAP.sectors.forEach((sector) => {
          // Determine sector color based on type and status
          let color;
          let size = 15;

          if (sector.type === "boss") {
            color = "#FF0088";
            size = 20;
          } else if (sector.type === "rare") {
            color = "#FFD700";
          } else {
            color = "#00FF99";
          }

          // Adjust for locked/unlocked status
          const isUnlocked = gameState.unlockedSectors.includes(sector.id);
          const isCleared = gameState.clearedSectors.includes(sector.id);

          if (!isUnlocked) {
            color = "#555555";
          } else if (isCleared) {
            color = "#AAAAAA";
          }

          // Draw sector node
          ctx.fillStyle = color;
          ctx.beginPath();
          ctx.arc(sector.x, sector.y, size, 0, Math.PI * 2);
          ctx.fill();

          // Add glow for unlocked sectors
          if (isUnlocked && !isCleared) {
            const glow = ctx.createRadialGradient(
              sector.x,
              sector.y,
              size,
              sector.x,
              sector.y,
              size * 2
            );
            glow.addColorStop(0, `${color}77`);
            glow.addColorStop(1, `${color}00`);

            ctx.fillStyle = glow;
            ctx.beginPath();
            ctx.arc(sector.x, sector.y, size * 2, 0, Math.PI * 2);
            ctx.fill();
          }

          // Add sector name
          ctx.fillStyle = "#FFFFFF";
          ctx.font = '12px "Courier New"';
          ctx.textAlign = "center";
          ctx.fillText(sector.name, sector.x, sector.y + size + 15);
        });
      }

      function updateMapHUD() {
        document.getElementById("navKeyCount").textContent =
          gameState.navKeyFragments;
        document.getElementById("sectorsCleared").textContent =
          gameState.clearedSectors.length;
      }

      // Handle sector selection
      document
        .getElementById("mapCanvas")
        .addEventListener("mousemove", (e) => {
          if (!gameState.mapOpen) return;

          const canvas = document.getElementById("mapCanvas");
          const rect = canvas.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;

          // Check if mouse is over a sector
          const hoveredSector = findSectorAtPosition(x, y);

          if (hoveredSector) {
            updateSectorInfo(hoveredSector);
          }
        });

      document.getElementById("mapCanvas").addEventListener("click", (e) => {
        if (!gameState.mapOpen) return;

        const canvas = document.getElementById("mapCanvas");
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Check if mouse is over a sector
        const clickedSector = findSectorAtPosition(x, y);

        if (
          clickedSector &&
          gameState.unlockedSectors.includes(clickedSector.id)
        ) {
          gameState.currentSector = clickedSector.id;
          updateSectorInfo(clickedSector);
          document.getElementById("enterSectorBtn").disabled = false;
        }
      });

      function findSectorAtPosition(x, y) {
        return GALACTIC_MAP.sectors.find((sector) => {
          const distance = Math.sqrt(
            Math.pow(sector.x - x, 2) + Math.pow(sector.y - y, 2)
          );
          return distance <= (sector.type === "boss" ? 20 : 15);
        });
      }

      function updateSectorInfo(sector) {
        const isUnlocked = gameState.unlockedSectors.includes(sector.id);
        const isCleared = gameState.clearedSectors.includes(sector.id);

        document.getElementById("sectorName").textContent = sector.name;
        document.getElementById("sectorType").textContent = `Type: ${
          sector.type.charAt(0).toUpperCase() + sector.type.slice(1)
        }`;

        // Set difficulty based on sector position
        let difficulty;
        if (sector.id.includes("1_")) {
          difficulty = "Easy";
        } else if (sector.id.includes("2_")) {
          difficulty = "Medium";
        } else {
          difficulty = "Hard";
        }

        document.getElementById(
          "sectorDifficulty"
        ).textContent = `Difficulty: ${difficulty}`;

        // Set reward based on sector type
        let reward;
        if (sector.type === "boss") {
          reward = "Legendary Item + 3 NavKey Fragments";
        } else if (sector.type === "rare") {
          reward = "Epic Item + 2 NavKey Fragments";
        } else {
          reward = "Random Item + 1 NavKey Fragment";
        }

        document.getElementById(
          "sectorReward"
        ).textContent = `Reward: ${reward}`;

        // Set status
        let status;
        if (isCleared) {
          status = "Cleared";
        } else if (isUnlocked) {
          status = "Available";
        } else {
          status = "Locked";
        }

        document.getElementById(
          "sectorStatus"
        ).textContent = `Status: ${status}`;

        // Set description based on sector type
        let description;
        if (sector.type === "boss") {
          description =
            "A dangerous lair housing a powerful boss entity. Prepare for a challenging battle!";
        } else if (sector.type === "rare") {
          description =
            "A valuable sector with rare resources but environmental hazards. High risk, high reward.";
        } else {
          description =
            "A standard sector with moderate enemy presence. Good for gathering resources.";
        }

        document.getElementById("sectorDescription").textContent = description;
      }

      function enterSector() {
        if (!gameState.currentSector) return;

        const sector = GALACTIC_MAP.sectors.find(
          (s) => s.id === gameState.currentSector
        );
        if (!sector) return;

        // Generate dungeon for this sector
        const seed = Date.now();
        const difficulty = sector.id.includes("1_")
          ? 1
          : sector.id.includes("2_")
          ? 2
          : 3;

        gameState.currentDungeon = DUNGEON_GENERATOR.generateDungeon(
          seed,
          difficulty
        );
        gameState.dungeonSeed = seed;
        gameState.roomIndex = 0;
        gameState.currentRoom = gameState.currentDungeon.rooms[0];
        gameState.visitedRooms = [0];

        // Reset keycards
        gameState.keycards = { red: false, blue: false, yellow: false };

        // Close map and start dungeon
        toggleGalacticMap();
        startDungeon();
      }

      // NEW: Dungeon System Functions
      function startDungeon() {
        // Hide start screen and show dungeon HUD
        document.getElementById("startScreen").style.display = "none";
        document.getElementById("dungeonHUD").style.display = "block";
        document.getElementById("keycardDisplay").style.display = "block";
        document.getElementById("dungeonMinimap").style.display = "block";

        // Initialize game state for dungeon
        gameState.running = true;
        gameState.score = 0;
        gameState.wave = 1;
        gameState.health = gameState.maxHealth;
        gameState.ammo = gameState.maxAmmo;
        gameState.reserveAmmo = 120;
        gameState.enemies = [];
        gameState.bullets = [];
        gameState.particles = [];
        gameState.player.x = canvas.width / 2;
        gameState.player.y = canvas.height / 2;

        // Initialize progression system if first time
        if (gameState.inventory.length === 0) {
          initializeProgressionSystem();
        }

        // Initialize audio context
        if (!audioContext) {
          initAudio();
        }

        updateAllDisplays();
        updateDungeonHUD();
        updateKeycardDisplay();

        // Spawn enemies for current room
        spawnRoomEnemies();

        gameLoop();

        console.log(
          "Dungeon started! Navigate through rooms to reach the boss!"
        );
      }

      function updateDungeonHUD() {
        if (!gameState.currentRoom || !gameState.currentDungeon) return;

        document.getElementById("currentRoomType").textContent =
          gameState.currentRoom.type.replace("_", " ").toUpperCase();
        document.getElementById("currentRoomIndex").textContent =
          gameState.roomIndex + 1;
        document.getElementById("totalRooms").textContent =
          gameState.currentDungeon.rooms.length;
      }

      function updateKeycardDisplay() {
        document.getElementById("redKeycard").className = `keycard red ${
          gameState.keycards.red ? "" : "inactive"
        }`;
        document.getElementById("blueKeycard").className = `keycard blue ${
          gameState.keycards.blue ? "" : "inactive"
        }`;
        document.getElementById("yellowKeycard").className = `keycard yellow ${
          gameState.keycards.yellow ? "" : "inactive"
        }`;
      }

      function spawnRoomEnemies() {
        if (!gameState.currentRoom) return;

        // Clear existing enemies
        gameState.enemies = [];

        // Spawn enemies based on room data
        gameState.currentRoom.enemies.forEach((enemyData) => {
          if (!enemyData.spawned) {
            const enemyType = ENEMY_DATA[enemyData.type];
            if (enemyType) {
              const enemy = {
                x: enemyData.x,
                y: enemyData.y,
                health: enemyType.baseHp * (1 + gameState.wave * 0.15),
                maxHealth: enemyType.baseHp * (1 + gameState.wave * 0.15),
                speed: enemyType.speed,
                color: enemyType.color,
                type: enemyData.type,
                lastShot: 0,
                angle: 0,
                attackPattern: enemyType.attackPattern,
                dropRate: enemyType.dropRate,
                size: 15,
                lastMove: 0,
              };

              gameState.enemies.push(enemy);
              enemyData.spawned = true;
            }
          }
        });

        console.log(
          `Spawned ${gameState.enemies.length} enemies in ${gameState.currentRoom.type}`
        );
      }

      function moveToNextRoom() {
        if (!gameState.currentDungeon) return;

        // Check if all enemies are defeated
        if (gameState.enemies.length > 0) {
          showNotification("Clear all enemies before proceeding!");
          return;
        }

        // Find available connections from current room
        const connections = gameState.currentDungeon.connections.filter(
          (conn) => conn.from === gameState.roomIndex
        );

        if (connections.length === 0) {
          showNotification("No available exits!");
          return;
        }

        // For now, take the first available connection
        const nextConnection = connections[0];

        // Check if door is locked
        if (nextConnection.locked && nextConnection.keycard) {
          if (!gameState.keycards[nextConnection.keycard]) {
            showNotification(
              `Need ${nextConnection.keycard} keycard to proceed!`
            );
            return;
          }
        }

        // Move to next room
        gameState.roomIndex = nextConnection.to;
        gameState.currentRoom =
          gameState.currentDungeon.rooms[gameState.roomIndex];
        gameState.visitedRooms.push(gameState.roomIndex);

        // Reset player position
        gameState.player.x = canvas.width / 2;
        gameState.player.y = canvas.height / 2;

        updateDungeonHUD();

        // Check if this is the boss room
        if (gameState.currentRoom.special === "boss") {
          startBossFight();
        } else {
          spawnRoomEnemies();

          // Handle special room types
          handleSpecialRoom();
        }

        showNotification(
          `Entered: ${gameState.currentRoom.type.replace("_", " ")}`
        );
      }

      function handleSpecialRoom() {
        if (!gameState.currentRoom) return;

        switch (gameState.currentRoom.special) {
          case "treasure":
            // Spawn treasure chest
            showNotification("💰 Treasure room discovered!");
            // Award bonus currency and rare item
            gameState.currency += 500;
            const rareItem = generateRandomItem("rare");
            if (rareItem) {
              addItemToInventory(rareItem);
              showNotification(`Found: ${rareItem.name}!`);
            }
            break;

          case "puzzle":
            // Spawn puzzle element
            showNotification("🧩 Puzzle room - solve to unlock rewards!");
            // For now, just award keycard
            const colors = ["red", "blue", "yellow"];
            const randomColor =
              colors[Math.floor(Math.random() * colors.length)];
            if (!gameState.keycards[randomColor]) {
              gameState.keycards[randomColor] = true;
              updateKeycardDisplay();
              showNotification(`🗝️ Found ${randomColor} keycard!`);
            }
            break;

          case "elite":
            // Spawn elite enemy with affixes
            showNotification("⚡ Elite enemy detected!");
            if (gameState.enemies.length > 0) {
              const elite = gameState.enemies[0];
              elite.health *= 2;
              elite.maxHealth *= 2;
              elite.size = 20;
              elite.color = "#FFD700";
              elite.dropRate = 50; // Higher drop rate
            }
            break;
        }
      }

      function startBossFight() {
        // Determine which boss based on sector
        let bossKey = "warp_hydra"; // Default

        if (gameState.currentSector) {
          if (gameState.currentSector === "boss_1") {
            bossKey = "warp_hydra";
          } else if (gameState.currentSector === "boss_2") {
            bossKey = "gravemind_core";
          }
        }

        const bossData = BOSS_DATA[bossKey];
        if (!bossData) return;

        // Show boss HUD
        document.getElementById("bossHUD").style.display = "block";
        document.getElementById("bossName").textContent = bossData.name;
        document.getElementById(
          "bossPhase"
        ).textContent = `Phase 1: ${bossData.phases[0].name}`;

        // Create boss enemy
        const boss = {
          x: canvas.width / 2,
          y: canvas.height / 4,
          health: bossData.baseHp,
          maxHealth: bossData.baseHp,
          speed: 1.5,
          color: bossData.color,
          type: "boss",
          bossKey: bossKey,
          phase: 0,
          lastShot: 0,
          angle: 0,
          size: 40,
          lastMove: 0,
          enrageTime: Date.now() + bossData.enrageTimer * 1000,
          mechanics: [...bossData.phases[0].mechanics],
        };

        gameState.enemies = [boss];

        // Show boss taunt
        showNotification(`💀 ${bossData.taunt}`);

        console.log(`Boss fight started: ${bossData.name}`);
      }

      function updateBossHUD() {
        if (
          gameState.enemies.length === 0 ||
          gameState.enemies[0].type !== "boss"
        ) {
          document.getElementById("bossHUD").style.display = "none";
          return;
        }

        const boss = gameState.enemies[0];
        const healthPercentage = (boss.health / boss.maxHealth) * 100;

        document.getElementById(
          "bossHealthFill"
        ).style.width = `${healthPercentage}%`;

        // Check for phase transitions
        const bossData = BOSS_DATA[boss.bossKey];
        if (bossData) {
          let newPhase = boss.phase;

          for (let i = 0; i < bossData.phases.length; i++) {
            if (
              healthPercentage <= bossData.phases[i].hpThreshold &&
              i > boss.phase
            ) {
              newPhase = i;
              break;
            }
          }

          if (newPhase !== boss.phase) {
            boss.phase = newPhase;
            boss.mechanics = [...bossData.phases[newPhase].mechanics];
            document.getElementById("bossPhase").textContent = `Phase ${
              newPhase + 1
            }: ${bossData.phases[newPhase].name}`;
            showNotification(
              `🔥 Boss entered ${bossData.phases[newPhase].name}!`
            );
          }
        }
      }

      function completeDungeon() {
        // Award sector completion rewards
        const sector = GALACTIC_MAP.sectors.find(
          (s) => s.id === gameState.currentSector
        );
        if (sector) {
          // Mark sector as cleared
          if (!gameState.clearedSectors.includes(sector.id)) {
            gameState.clearedSectors.push(sector.id);
          }

          // Award NavKey fragments
          let fragments = 1;
          if (sector.type === "rare") fragments = 2;
          if (sector.type === "boss") fragments = 3;

          gameState.navKeyFragments += fragments;

          // Unlock connected sectors
          sector.connections.forEach((connId) => {
            if (!gameState.unlockedSectors.includes(connId)) {
              gameState.unlockedSectors.push(connId);
            }
          });

          showNotification(`🎉 Sector cleared! +${fragments} NavKey Fragments`);
        }

        // Hide dungeon UI
        document.getElementById("dungeonHUD").style.display = "none";
        document.getElementById("keycardDisplay").style.display = "none";
        document.getElementById("dungeonMinimap").style.display = "none";
        document.getElementById("bossHUD").style.display = "none";

        // Return to start screen
        gameState.running = false;
        document.getElementById("startScreen").style.display = "flex";

        console.log("Dungeon completed! Returning to start screen.");
      }

      // Initialize displays on page load
      updateAllDisplays();

      // Welcome message
      console.log(`
🎮 GALAXY GUNS 3D - COMPLETE PROGRESSION SYSTEM LOADED!

🌌 MASSIVE EXPANSION FEATURES:
• Galactic Map: Navigate between sectors with unique challenges
• Procedural Dungeons: Multi-room layouts with distinct biomes
• 30+ Enemy Types: From trash mobs to legendary bosses
• 5 Unique Bosses: Multi-phase fights with special mechanics
• Equipment System: Weapons, Armor, Accessories with rarities
• Level Progression: XP system with 50 levels
• Puzzle & Event System: Interactive challenges and rewards
• Keycard System: Unlock doors and secret areas
• Meta Progression: Permanent upgrades and currencies

🎯 CONTROLS:
• WASD: Move
• Mouse: Aim
• Click: Shoot
• R: Reload
• Space: Jump
• I: Inventory
• P: Store
• O: Achievements
• M: Galaxy Map
• N: Next Room (in dungeons)

🏆 PROGRESSION FEATURES:
• Gain XP from kills and wave completion
• Level up to unlock better equipment
• Find rare equipment drops from enemies
• Complete achievements for bonus currency
• Daily challenges for extra rewards
• Equipment synergies and set bonuses
• NavKey fragments to unlock new sectors
• Boss rewards and legendary items

🌟 HOW TO PLAY:
1. Click START GAME for arena mode OR
2. Press M to open Galaxy Map
3. Select a sector and click "Enter Sector"
4. Navigate through procedural dungeons
5. Defeat bosses to unlock new areas
6. Collect equipment and level up!

Click START GAME for arena mode or press M for the Galaxy Map!
Good luck, soldier! 🎖️
        `);

      // Auto-focus canvas for immediate keyboard input
      canvas.focus();
      canvas.tabIndex = 0; // Make canvas focusable

      // Also focus on any click
      canvas.addEventListener("click", () => {
        canvas.focus();
      });

      // Focus on page load
      window.addEventListener("load", () => {
        canvas.focus();
      });

      // Test if event listeners are working
      console.log("Canvas event listeners initialized");
      console.log("Canvas size:", canvas.width, "x", canvas.height);
      console.log("Game state initialized:", gameState);

      // Test core systems
      console.log(
        "ENEMY_DATA loaded:",
        Object.keys(ENEMY_DATA).length,
        "enemy types"
      );
      console.log(
        "BOSS_DATA loaded:",
        Object.keys(BOSS_DATA).length,
        "boss types"
      );
      console.log(
        "GALACTIC_MAP loaded:",
        GALACTIC_MAP.sectors.length,
        "sectors"
      );
      console.log(
        "ROOM_TYPES loaded:",
        Object.keys(ROOM_TYPES).length,
        "room types"
      );
      console.log(
        "EQUIPMENT_DATA loaded:",
        EQUIPMENT_DATA.weapons.length +
          EQUIPMENT_DATA.armor.length +
          EQUIPMENT_DATA.accessories.length,
        "total items"
      );

      // Test functions exist
      console.log("Core functions available:");
      console.log("- startGame:", typeof startGame);
      console.log("- toggleGalacticMap:", typeof toggleGalacticMap);
      console.log("- spawnEnemy:", typeof spawnEnemy);
      console.log("- levelUp:", typeof levelUp);
      console.log("- DUNGEON_GENERATOR:", typeof DUNGEON_GENERATOR);

      console.log("🎮 Galaxy Guns 3D - All systems loaded successfully!");
      console.log(
        "🚀 Ready to play! Click START GAME or press M for Galaxy Map"
      );
    </script>
  </body>
</html>
