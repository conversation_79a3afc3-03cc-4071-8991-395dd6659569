#!/usr/bin/env python3
"""
Galaxy Guns 3D - Audio Processing Pipeline
Automated audio processing and FMOD integration for adaptive soundtracks
"""

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path
from typing import Dict, List, Optional
import shutil

class AudioProcessor:
    """Handles automated audio processing and optimization"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.source_dir = self.project_root / "assets" / "audio" / "source"
        self.output_dir = self.project_root / "client" / "assets" / "audio"
        self.config_file = self.project_root / "assets" / "audio" / "audio_config.json"
        
        # Audio processing tools
        self.ffmpeg_path = self._find_ffmpeg()
        
        # Ensure directories exist
        self.source_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        for subdir in ["sfx", "music", "voice", "ambient"]:
            (self.output_dir / subdir).mkdir(exist_ok=True)
    
    def _find_ffmpeg(self) -> str:
        """Find FFmpeg executable on the system"""
        if shutil.which("ffmpeg"):
            return "ffmpeg"
        
        possible_paths = [
            "C:/ffmpeg/bin/ffmpeg.exe",  # Windows
            "/usr/local/bin/ffmpeg",     # macOS Homebrew
            "/usr/bin/ffmpeg"            # Linux
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return path
                
        raise FileNotFoundError(
            "FFmpeg not found. Please install FFmpeg and ensure it's in your PATH."
        )
    
    def load_config(self) -> Dict:
        """Load audio processing configuration"""
        default_config = {
            "formats": {
                "sfx": {
                    "format": "ogg",
                    "codec": "libvorbis",
                    "bitrate": "192k",
                    "sample_rate": 44100,
                    "channels": 1  # Mono for SFX
                },
                "music": {
                    "format": "ogg",
                    "codec": "libvorbis",
                    "bitrate": "256k",
                    "sample_rate": 44100,
                    "channels": 2  # Stereo for music
                },
                "voice": {
                    "format": "ogg",
                    "codec": "libvorbis",
                    "bitrate": "128k",
                    "sample_rate": 22050,
                    "channels": 1  # Mono for voice
                },
                "ambient": {
                    "format": "ogg",
                    "codec": "libvorbis",
                    "bitrate": "160k",
                    "sample_rate": 44100,
                    "channels": 2  # Stereo for ambient
                }
            },
            "processing": {
                "normalize": True,
                "noise_reduction": True,
                "fade_in": 0.1,  # seconds
                "fade_out": 0.1,  # seconds
                "trim_silence": True
            },
            "adaptive_music": {
                "bpm_tiers": [120, 140, 160],  # Low, medium, high intensity
                "crossfade_duration": 2.0,
                "loop_points": True
            },
            "spatial_audio": {
                "enable_3d": True,
                "max_distance": 1000.0,
                "rolloff_factor": 1.0
            }
        }
        
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                config = json.load(f)
                # Merge with defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        else:
            # Create default config
            with open(self.config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            return default_config
    
    def detect_audio_type(self, audio_file: Path) -> str:
        """Detect audio type based on filename and directory structure"""
        path_parts = audio_file.parts
        
        # Check directory structure
        if "sfx" in path_parts or "sound_effects" in path_parts:
            return "sfx"
        elif "music" in path_parts or "soundtrack" in path_parts:
            return "music"
        elif "voice" in path_parts or "dialogue" in path_parts:
            return "voice"
        elif "ambient" in path_parts or "atmosphere" in path_parts:
            return "ambient"
        
        # Check filename patterns
        filename_lower = audio_file.name.lower()
        if any(keyword in filename_lower for keyword in ["gun", "shot", "explosion", "impact", "reload"]):
            return "sfx"
        elif any(keyword in filename_lower for keyword in ["music", "theme", "track", "bgm"]):
            return "music"
        elif any(keyword in filename_lower for keyword in ["voice", "dialogue", "speech", "callout"]):
            return "voice"
        elif any(keyword in filename_lower for keyword in ["ambient", "atmosphere", "wind", "rain"]):
            return "ambient"
        
        # Default to SFX
        return "sfx"
    
    def analyze_audio(self, audio_file: Path) -> Dict:
        """Analyze audio file properties"""
        try:
            result = subprocess.run([
                self.ffmpeg_path,
                "-i", str(audio_file),
                "-f", "null", "-"
            ], capture_output=True, text=True, stderr=subprocess.STDOUT)
            
            # Parse FFmpeg output for audio properties
            output = result.stdout
            
            # Extract duration
            duration = 0.0
            if "Duration:" in output:
                duration_str = output.split("Duration:")[1].split(",")[0].strip()
                time_parts = duration_str.split(":")
                duration = float(time_parts[0]) * 3600 + float(time_parts[1]) * 60 + float(time_parts[2])
            
            # Extract sample rate and channels
            sample_rate = 44100
            channels = 2
            if "Hz" in output:
                hz_part = output.split("Hz")[0].split()[-1]
                try:
                    sample_rate = int(hz_part)
                except ValueError:
                    pass
            
            if "mono" in output.lower():
                channels = 1
            elif "stereo" in output.lower():
                channels = 2
            
            return {
                "duration": duration,
                "sample_rate": sample_rate,
                "channels": channels,
                "size": audio_file.stat().st_size
            }
        except Exception as e:
            print(f"⚠️ Could not analyze {audio_file.name}: {e}")
            return {"duration": 0.0, "sample_rate": 44100, "channels": 2, "size": 0}
    
    def process_audio_file(self, audio_file: Path, config: Dict) -> Optional[Path]:
        """Process a single audio file"""
        print(f"🎵 Processing: {audio_file.name}")
        
        # Detect audio type
        audio_type = self.detect_audio_type(audio_file)
        format_config = config["formats"][audio_type]
        processing_config = config["processing"]
        
        # Create output path
        output_dir = self.output_dir / audio_type
        output_file = output_dir / f"{audio_file.stem}.{format_config['format']}"
        
        # Build FFmpeg command
        cmd = [
            self.ffmpeg_path,
            "-i", str(audio_file),
            "-y"  # Overwrite output files
        ]
        
        # Audio codec and quality
        cmd.extend(["-c:a", format_config["codec"]])
        cmd.extend(["-b:a", format_config["bitrate"]])
        cmd.extend(["-ar", str(format_config["sample_rate"])])
        cmd.extend(["-ac", str(format_config["channels"])])
        
        # Audio processing filters
        filters = []
        
        # Normalize audio
        if processing_config["normalize"]:
            filters.append("loudnorm")
        
        # Trim silence
        if processing_config["trim_silence"]:
            filters.append("silenceremove=start_periods=1:start_silence=0.1:start_threshold=-50dB")
        
        # Add fade in/out
        fade_in = processing_config["fade_in"]
        fade_out = processing_config["fade_out"]
        if fade_in > 0 or fade_out > 0:
            # Get duration for fade out calculation
            analysis = self.analyze_audio(audio_file)
            duration = analysis["duration"]
            if fade_in > 0:
                filters.append(f"afade=t=in:ss=0:d={fade_in}")
            if fade_out > 0 and duration > fade_out:
                filters.append(f"afade=t=out:st={duration-fade_out}:d={fade_out}")
        
        # Apply filters if any
        if filters:
            cmd.extend(["-af", ",".join(filters)])
        
        # Output file
        cmd.append(str(output_file))
        
        try:
            subprocess.run(cmd, check=True, capture_output=True)
            
            # Generate Godot import file
            self._generate_godot_audio_import(output_file, audio_type, config)
            
            # Generate metadata
            self._generate_audio_metadata(audio_file, output_file, audio_type, config)
            
            print(f"✅ Processed: {audio_file.name} -> {output_file.name}")
            return output_file
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Error processing {audio_file.name}: {e}")
            return None
    
    def _generate_godot_audio_import(self, audio_file: Path, audio_type: str, config: Dict):
        """Generate Godot import file for audio"""
        import_file = audio_file.with_suffix(f'.{audio_file.suffix}.import')
        
        # Configure import settings based on audio type
        if audio_type == "music":
            import_content = f"""[remap]

importer="oggvorbisstr"
type="AudioStreamOggVorbis"
uid="uid://generated_{audio_file.stem}"
path="res://.godot/imported/{audio_file.name}-{hash(str(audio_file))}.oggvorbisstr"

[deps]

source_file="res://assets/audio/{audio_type}/{audio_file.name}"
dest_files=["res://.godot/imported/{audio_file.name}-{hash(str(audio_file))}.oggvorbisstr"]

[params]

loop=true
loop_offset=0.0
bpm=0.0
beat_count=0
bar_beats=4
"""
        else:
            import_content = f"""[remap]

importer="oggvorbisstr"
type="AudioStreamOggVorbis"
uid="uid://generated_{audio_file.stem}"
path="res://.godot/imported/{audio_file.name}-{hash(str(audio_file))}.oggvorbisstr"

[deps]

source_file="res://assets/audio/{audio_type}/{audio_file.name}"
dest_files=["res://.godot/imported/{audio_file.name}-{hash(str(audio_file))}.oggvorbisstr"]

[params]

loop=false
loop_offset=0
bpm=0
beat_count=0
bar_beats=4
"""
        
        with open(import_file, 'w') as f:
            f.write(import_content)
    
    def _generate_audio_metadata(self, source_file: Path, output_file: Path, audio_type: str, config: Dict):
        """Generate metadata file for the audio"""
        analysis = self.analyze_audio(source_file)
        
        metadata = {
            "source_file": str(source_file.relative_to(self.project_root)),
            "output_file": str(output_file.relative_to(self.project_root)),
            "audio_type": audio_type,
            "processing_date": str(subprocess.check_output(["date"], text=True).strip()),
            "properties": analysis,
            "format_config": config["formats"][audio_type]
        }
        
        # Add specific metadata based on audio type
        if audio_type == "music":
            metadata["adaptive_music"] = {
                "bpm": self._detect_bpm(source_file),
                "key": "C",  # Would need music analysis library
                "intensity_level": "medium"  # Would be manually tagged
            }
        elif audio_type == "sfx":
            metadata["spatial_audio"] = {
                "is_3d": True,
                "max_distance": config["spatial_audio"]["max_distance"],
                "rolloff": config["spatial_audio"]["rolloff_factor"]
            }
        
        metadata_file = output_file.parent / f"{output_file.stem}_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    def _detect_bpm(self, audio_file: Path) -> float:
        """Detect BPM of music file (simplified implementation)"""
        # This would use a proper BPM detection library in production
        # For now, return a default based on filename patterns
        filename_lower = audio_file.name.lower()
        if "slow" in filename_lower or "ambient" in filename_lower:
            return 120.0
        elif "fast" in filename_lower or "action" in filename_lower:
            return 160.0
        else:
            return 140.0
    
    def create_adaptive_music_layers(self, music_files: List[Path], config: Dict):
        """Create adaptive music layers for dynamic soundtrack"""
        print("🎼 Creating adaptive music layers...")
        
        adaptive_dir = self.output_dir / "music" / "adaptive"
        adaptive_dir.mkdir(exist_ok=True)
        
        # Group music files by BPM tiers
        bpm_tiers = config["adaptive_music"]["bpm_tiers"]
        
        for music_file in music_files:
            if music_file.parent.name != "music":
                continue
                
            bpm = self._detect_bpm(music_file)
            
            # Determine tier
            tier = "medium"
            if bpm <= bpm_tiers[0]:
                tier = "low"
            elif bpm >= bpm_tiers[2]:
                tier = "high"
            
            # Create tier directory
            tier_dir = adaptive_dir / tier
            tier_dir.mkdir(exist_ok=True)
            
            # Copy to tier directory
            tier_file = tier_dir / music_file.name
            shutil.copy2(music_file, tier_file)
            
            print(f"📁 Categorized {music_file.name} as {tier} intensity (BPM: {bpm})")
    
    def process_all_audio(self):
        """Process all audio files in the source directory"""
        config = self.load_config()
        
        # Find all audio files
        audio_extensions = ['.wav', '.mp3', '.flac', '.aiff', '.ogg', '.m4a']
        audio_files = []
        
        for ext in audio_extensions:
            audio_files.extend(self.source_dir.glob(f"**/*{ext}"))
        
        if not audio_files:
            print("⚠️ No audio files found in source directory")
            return
        
        print(f"🎵 Found {len(audio_files)} audio files to process")
        
        processed_files = []
        for audio_file in audio_files:
            try:
                output_file = self.process_audio_file(audio_file, config)
                if output_file:
                    processed_files.append(output_file)
            except Exception as e:
                print(f"❌ Error processing {audio_file.name}: {e}")
        
        print(f"🎉 Audio processing complete! Generated {len(processed_files)} output files")
        
        # Create adaptive music layers
        music_files = [f for f in processed_files if f.parent.name == "music"]
        if music_files:
            self.create_adaptive_music_layers(music_files, config)

def main():
    parser = argparse.ArgumentParser(description="Galaxy Guns 3D Audio Processor")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--file", help="Process specific audio file")
    parser.add_argument("--type", choices=["sfx", "music", "voice", "ambient"], help="Audio type")
    
    args = parser.parse_args()
    
    processor = AudioProcessor(args.project_root)
    
    if args.file:
        config = processor.load_config()
        processor.process_audio_file(Path(args.file), config)
    else:
        processor.process_all_audio()

if __name__ == "__main__":
    main()
