# Galaxy Guns 3D - Map Loader System
# Tile map streaming with quad-tree culling for optimal performance
# Supports large maps with dynamic loading/unloading of chunks

class_name MapLoader
extends Node3D

## Signals for map events
signal chunk_loaded(chunk_id: String, chunk_position: Vector2i)
signal chunk_unloaded(chunk_id: String, chunk_position: Vector2i)
signal map_loading_complete()
signal map_loading_progress(progress: float)

## Map configuration
@export_group("Map Settings")
@export var map_name: String = "default_map"
@export var chunk_size: int = 64  # Size of each chunk in world units
@export var load_radius: int = 3   # Chunks to load around player
@export var unload_radius: int = 5 # Chunks to unload when far away
@export var max_chunks_per_frame: int = 2  # Performance limit

@export_group("Culling")
@export var enable_frustum_culling: bool = true
@export var enable_occlusion_culling: bool = true
@export var culling_update_frequency: float = 0.1

## Quad-tree for spatial partitioning
class QuadTreeNode:
	var bounds: Rect2
	var level: int
	var chunks: Array[MapChunk] = []
	var children: Array[QuadTreeNode] = []
	var is_leaf: bool = true
	
	func _init(rect: Rect2, tree_level: int = 0):
		bounds = rect
		level = tree_level
	
	func subdivide():
		if not is_leaf:
			return
		
		var half_width = bounds.size.x / 2.0
		var half_height = bounds.size.y / 2.0
		var x = bounds.position.x
		var y = bounds.position.y
		
		children.append(QuadTreeNode.new(Rect2(x, y, half_width, half_height), level + 1))
		children.append(QuadTreeNode.new(Rect2(x + half_width, y, half_width, half_height), level + 1))
		children.append(QuadTreeNode.new(Rect2(x, y + half_height, half_width, half_height), level + 1))
		children.append(QuadTreeNode.new(Rect2(x + half_width, y + half_height, half_width, half_height), level + 1))
		
		is_leaf = false
	
	func insert_chunk(chunk: MapChunk) -> bool:
		if not bounds.has_point(Vector2(chunk.world_position.x, chunk.world_position.z)):
			return false
		
		if is_leaf and chunks.size() < 4:
			chunks.append(chunk)
			return true
		
		if is_leaf:
			subdivide()
		
		for child in children:
			if child.insert_chunk(chunk):
				return true
		
		return false
	
	func query_chunks(query_bounds: Rect2, result: Array[MapChunk]):
		if not bounds.intersects(query_bounds):
			return
		
		if is_leaf:
			for chunk in chunks:
				var chunk_pos = Vector2(chunk.world_position.x, chunk.world_position.z)
				if query_bounds.has_point(chunk_pos):
					result.append(chunk)
		else:
			for child in children:
				child.query_chunks(query_bounds, result)

## Map chunk representation
class MapChunk:
	var chunk_id: String
	var chunk_position: Vector2i
	var world_position: Vector3
	var scene_instance: Node3D
	var is_loaded: bool = false
	var is_visible: bool = true
	var last_access_time: float
	var static_bodies: Array[StaticBody3D] = []
	var enemies: Array[EnemyAI] = []
	var pickups: Array[Node3D] = []
	
	func _init(id: String, pos: Vector2i, world_pos: Vector3):
		chunk_id = id
		chunk_position = pos
		world_position = world_pos
		last_access_time = Time.get_time_dict_from_system()["unix"]

## Internal state
var quad_tree: QuadTreeNode
var loaded_chunks: Dictionary = {}  # chunk_id -> MapChunk
var chunk_load_queue: Array[Vector2i] = []
var chunk_unload_queue: Array[String] = []
var player_reference: Node3D
var camera_reference: Camera3D

## Map data
var map_config: Dictionary = {}
var chunk_scenes: Dictionary = {}  # chunk_type -> PackedScene

## Performance tracking
var culling_timer: float = 0.0
var chunks_loaded_this_frame: int = 0
var loading_progress: float = 0.0

func _ready():
	# Initialize quad-tree with map bounds
	var map_bounds = Rect2(-1000, -1000, 2000, 2000)  # 2km x 2km map
	quad_tree = QuadTreeNode.new(map_bounds)
	
	# Load map configuration
	_load_map_config()
	_load_chunk_scenes()
	
	# Find player and camera references
	_find_references()
	
	print("🗺️ Map Loader initialized for map: %s" % map_name)

func _load_map_config():
	"""Load map configuration from JSON"""
	var config_path = "res://shared/data/maps/" + map_name + ".json"
	var file = FileAccess.open(config_path, FileAccess.READ)
	
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			map_config = json.data
			print("📄 Loaded map config: %s" % map_name)
		else:
			push_error("Failed to parse map config: " + config_path)
	else:
		push_error("Failed to load map config: " + config_path)

func _load_chunk_scenes():
	"""Load chunk scene references"""
	chunk_scenes = {
		"urban": preload("res://client/scenes/chunks/urban_chunk.tscn"),
		"industrial": preload("res://client/scenes/chunks/industrial_chunk.tscn"),
		"residential": preload("res://client/scenes/chunks/residential_chunk.tscn"),
		"park": preload("res://client/scenes/chunks/park_chunk.tscn"),
		"warehouse": preload("res://client/scenes/chunks/warehouse_chunk.tscn")
	}

func _find_references():
	"""Find player and camera references"""
	player_reference = get_tree().get_first_node_in_group("player")
	if player_reference:
		camera_reference = player_reference.get_node("CameraPivot/Camera3D")

func _process(delta):
	# Update culling system
	culling_timer += delta
	if culling_timer >= culling_update_frequency:
		culling_timer = 0.0
		_update_culling()
	
	# Process chunk loading/unloading
	_process_chunk_queue()
	
	# Update chunk streaming based on player position
	if player_reference:
		_update_chunk_streaming()

func _update_chunk_streaming():
	"""Update chunk loading based on player position"""
	if not player_reference:
		return
	
	var player_pos = player_reference.global_position
	var player_chunk = Vector2i(
		int(player_pos.x / chunk_size),
		int(player_pos.z / chunk_size)
	)
	
	# Queue chunks for loading
	for x in range(player_chunk.x - load_radius, player_chunk.x + load_radius + 1):
		for z in range(player_chunk.y - load_radius, player_chunk.y + load_radius + 1):
			var chunk_pos = Vector2i(x, z)
			var chunk_id = _get_chunk_id(chunk_pos)
			
			if not loaded_chunks.has(chunk_id) and not chunk_pos in chunk_load_queue:
				chunk_load_queue.append(chunk_pos)
	
	# Queue chunks for unloading
	var chunks_to_unload: Array[String] = []
	for chunk_id in loaded_chunks.keys():
		var chunk = loaded_chunks[chunk_id]
		var distance = chunk.chunk_position.distance_to(player_chunk)
		
		if distance > unload_radius:
			chunks_to_unload.append(chunk_id)
	
	for chunk_id in chunks_to_unload:
		if not chunk_id in chunk_unload_queue:
			chunk_unload_queue.append(chunk_id)

func _process_chunk_queue():
	"""Process chunk loading and unloading queues"""
	chunks_loaded_this_frame = 0
	
	# Process unloading first (frees memory)
	while chunk_unload_queue.size() > 0 and chunks_loaded_this_frame < max_chunks_per_frame:
		var chunk_id = chunk_unload_queue.pop_front()
		_unload_chunk(chunk_id)
		chunks_loaded_this_frame += 1
	
	# Process loading
	while chunk_load_queue.size() > 0 and chunks_loaded_this_frame < max_chunks_per_frame:
		var chunk_pos = chunk_load_queue.pop_front()
		_load_chunk(chunk_pos)
		chunks_loaded_this_frame += 1

func _load_chunk(chunk_pos: Vector2i):
	"""Load a map chunk"""
	var chunk_id = _get_chunk_id(chunk_pos)
	
	if loaded_chunks.has(chunk_id):
		return  # Already loaded
	
	# Get chunk configuration
	var chunk_config = _get_chunk_config(chunk_pos)
	if not chunk_config:
		return
	
	# Create chunk instance
	var world_pos = Vector3(chunk_pos.x * chunk_size, 0, chunk_pos.y * chunk_size)
	var chunk = MapChunk.new(chunk_id, chunk_pos, world_pos)
	
	# Load chunk scene
	var chunk_type = chunk_config.get("type", "urban")
	var chunk_scene = chunk_scenes.get(chunk_type)
	
	if chunk_scene:
		chunk.scene_instance = chunk_scene.instantiate()
		chunk.scene_instance.position = world_pos
		add_child(chunk.scene_instance)
		
		# Initialize chunk content
		_initialize_chunk_content(chunk, chunk_config)
		
		chunk.is_loaded = true
		loaded_chunks[chunk_id] = chunk
		
		# Add to quad-tree
		quad_tree.insert_chunk(chunk)
		
		chunk_loaded.emit(chunk_id, chunk_pos)
		print("📦 Loaded chunk: %s at %s" % [chunk_id, chunk_pos])

func _unload_chunk(chunk_id: String):
	"""Unload a map chunk"""
	if not loaded_chunks.has(chunk_id):
		return
	
	var chunk = loaded_chunks[chunk_id]
	
	# Remove scene instance
	if chunk.scene_instance:
		chunk.scene_instance.queue_free()
	
	# Clean up chunk content
	_cleanup_chunk_content(chunk)
	
	loaded_chunks.erase(chunk_id)
	chunk_unloaded.emit(chunk_id, chunk.chunk_position)
	print("📤 Unloaded chunk: %s" % chunk_id)

func _initialize_chunk_content(chunk: MapChunk, config: Dictionary):
	"""Initialize enemies, pickups, and other content in chunk"""
	if not chunk.scene_instance:
		return
	
	# Spawn enemies
	var enemies_config = config.get("enemies", [])
	for enemy_config in enemies_config:
		var enemy_scene = preload("res://client/scenes/enemies/soldier.tscn")
		var enemy = enemy_scene.instantiate()
		
		var spawn_pos = Vector3(
			enemy_config.get("x", 0),
			enemy_config.get("y", 0),
			enemy_config.get("z", 0)
		)
		
		enemy.position = chunk.world_position + spawn_pos
		enemy.ai_type = enemy_config.get("type", "soldier")
		
		chunk.scene_instance.add_child(enemy)
		chunk.enemies.append(enemy)
	
	# Spawn pickups
	var pickups_config = config.get("pickups", [])
	for pickup_config in pickups_config:
		# This would spawn weapon pickups, health packs, etc.
		pass

func _cleanup_chunk_content(chunk: MapChunk):
	"""Clean up chunk content before unloading"""
	# Remove enemies from AI system
	for enemy in chunk.enemies:
		if is_instance_valid(enemy):
			enemy.queue_free()
	
	chunk.enemies.clear()
	chunk.pickups.clear()
	chunk.static_bodies.clear()

func _update_culling():
	"""Update frustum and occlusion culling"""
	if not camera_reference or not enable_frustum_culling:
		return
	
	var camera_transform = camera_reference.global_transform
	var camera_pos = camera_transform.origin
	
	# Get camera frustum (simplified)
	var camera_forward = -camera_transform.basis.z
	var fov = camera_reference.fov
	var near_plane = camera_reference.near
	var far_plane = camera_reference.far
	
	# Query chunks in camera view
	var view_bounds = Rect2(
		camera_pos.x - far_plane,
		camera_pos.z - far_plane,
		far_plane * 2,
		far_plane * 2
	)
	
	var visible_chunks: Array[MapChunk] = []
	quad_tree.query_chunks(view_bounds, visible_chunks)
	
	# Update chunk visibility
	for chunk_id in loaded_chunks.keys():
		var chunk = loaded_chunks[chunk_id]
		var was_visible = chunk.is_visible
		
		# Check if chunk is in visible set
		chunk.is_visible = chunk in visible_chunks
		
		# Update scene visibility
		if chunk.scene_instance and chunk.is_visible != was_visible:
			chunk.scene_instance.visible = chunk.is_visible

func _get_chunk_id(chunk_pos: Vector2i) -> String:
	"""Generate chunk ID from position"""
	return "%s_%d_%d" % [map_name, chunk_pos.x, chunk_pos.y]

func _get_chunk_config(chunk_pos: Vector2i) -> Dictionary:
	"""Get configuration for chunk at position"""
	var chunks_config = map_config.get("chunks", {})
	var chunk_key = "%d_%d" % [chunk_pos.x, chunk_pos.y]
	
	if chunks_config.has(chunk_key):
		return chunks_config[chunk_key]
	
	# Return default chunk config
	return {
		"type": "urban",
		"enemies": [],
		"pickups": []
	}

## Public API

func get_chunk_at_position(world_pos: Vector3) -> MapChunk:
	"""Get chunk at world position"""
	var chunk_pos = Vector2i(
		int(world_pos.x / chunk_size),
		int(world_pos.z / chunk_size)
	)
	var chunk_id = _get_chunk_id(chunk_pos)
	return loaded_chunks.get(chunk_id)

func force_load_chunk(chunk_pos: Vector2i):
	"""Force load a specific chunk"""
	if not chunk_pos in chunk_load_queue:
		chunk_load_queue.push_front(chunk_pos)

func get_loaded_chunks() -> Array[MapChunk]:
	"""Get all currently loaded chunks"""
	var chunks: Array[MapChunk] = []
	for chunk in loaded_chunks.values():
		chunks.append(chunk)
	return chunks

func get_map_stats() -> Dictionary:
	"""Get map loading statistics"""
	return {
		"loaded_chunks": loaded_chunks.size(),
		"load_queue_size": chunk_load_queue.size(),
		"unload_queue_size": chunk_unload_queue.size(),
		"visible_chunks": _count_visible_chunks(),
		"map_name": map_name
	}

func _count_visible_chunks() -> int:
	"""Count currently visible chunks"""
	var count = 0
	for chunk in loaded_chunks.values():
		if chunk.is_visible:
			count += 1
	return count
