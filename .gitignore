# Godot 4.x
.godot/
.import/
export.cfg
export_presets.cfg.bak
*.tmp

# Godot-specific
.mono/
data_*/
mono_crash.*.json

# Build outputs
builds/
*.ipa
*.apk
*.exe
*.dmg
*.zip

# iOS specific
*.dSYM/
*.mobileprovision
*.p12
*.cer
DerivedData/
xcuserdata/
*.xcworkspace/xcuserdata/
*.xcodeproj/xcuserdata/

# Xcode
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.xcuserstate
project.xcworkspace/
xcuserdata/
*.moved-aside
*.xccheckout
*.xcscmblueprint

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependencies
node_modules/
target/
Cargo.lock
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
*.cache
.parcel-cache/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Asset processing
.aseprite/
*.ase.bak
*.aseprite.bak

# Audio processing
*.wav.asd
*.aup3
*.aup3-shm
*.aup3-wal

# Server specific
server/target/
server/.env
server/logs/
server/data/

# Docker
.dockerignore
docker-compose.override.yml

# CI/CD
.github/workflows/*.yml.bak
ci/secrets/
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Marketing assets (working files)
marketing/working/
*.psd
*.ai
*.sketch
*.fig

# Documentation builds
docs/_build/
docs/.doctrees/

# Test coverage
coverage/
*.coverage
.nyc_output/

# Profiling
*.prof
*.trace

# Game data (generated)
shared/data/generated/
client/assets/generated/

# Analytics and crash reports
crashlytics-build.properties
fabric.properties

# Fastlane
fastlane/README.md
fastlane/Fastfile.bak

# App Store Connect API
AuthKey_*.p8
api_key.json

# Certificates and provisioning profiles
*.mobileprovision
*.p12
*.cer
*.certSigningRequest

# Local configuration
config.local.json
settings.local.json
