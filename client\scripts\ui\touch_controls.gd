# Galaxy Guns 3D - Touch Controls UI
# Optimized virtual controls for mobile FPS gameplay
# Dual-stick layout with customizable button positions

class_name TouchControls
extends Control

## Signals for input events
signal movement_input(input: Vector2)
signal look_input(input: Vector2)
signal fire_pressed()
signal fire_released()
signal reload_pressed()
signal jump_pressed()
signal crouch_pressed()
signal weapon_switch_pressed()

## Touch control nodes
@onready var movement_joystick: VirtualJoystick = $MovementJoystick
@onready var look_area: Control = $LookArea
@onready var fire_button: TouchButton = $FireButton
@onready var reload_button: TouchButton = $ReloadButton
@onready var jump_button: TouchButton = $JumpButton
@onready var crouch_button: TouchButton = $CrouchButton
@onready var weapon_button: TouchButton = $WeaponButton

## Touch tracking
var active_touches: Dictionary = {}
var look_touch_id: int = -1
var movement_touch_id: int = -1

## Settings
@export var look_sensitivity: float = 1.0
@export var movement_deadzone: float = 0.2
@export var look_deadzone: float = 0.1
@export var button_opacity: float = 0.7

## Virtual Joystick Component
class VirtualJoystick extends Control:
	signal input_changed(input: Vector2)
	
	var knob: Control
	var background: Control
	var is_active: bool = false
	var touch_id: int = -1
	var center_position: Vector2
	var max_distance: float = 50.0
	var current_input: Vector2 = Vector2.ZERO
	
	func _ready():
		# Create joystick visuals
		background = ColorRect.new()
		background.color = Color(1, 1, 1, 0.3)
		background.size = Vector2(100, 100)
		background.position = -background.size / 2
		add_child(background)
		
		knob = ColorRect.new()
		knob.color = Color(1, 1, 1, 0.8)
		knob.size = Vector2(40, 40)
		knob.position = -knob.size / 2
		add_child(knob)
		
		center_position = Vector2.ZERO
		max_distance = background.size.x / 2 - knob.size.x / 2
	
	func _gui_input(event):
		if event is InputEventScreenTouch:
			if event.pressed and not is_active:
				# Start joystick interaction
				is_active = true
				touch_id = event.index
				_update_knob_position(event.position - global_position)
			elif not event.pressed and event.index == touch_id:
				# End joystick interaction
				_reset_joystick()
		
		elif event is InputEventScreenDrag and event.index == touch_id:
			# Update joystick position
			_update_knob_position(event.position - global_position)
	
	func _update_knob_position(local_pos: Vector2):
		var offset = local_pos - center_position
		var distance = offset.length()
		
		if distance > max_distance:
			offset = offset.normalized() * max_distance
		
		knob.position = center_position + offset - knob.size / 2
		
		# Calculate input value
		current_input = offset / max_distance
		input_changed.emit(current_input)
	
	func _reset_joystick():
		is_active = false
		touch_id = -1
		knob.position = center_position - knob.size / 2
		current_input = Vector2.ZERO
		input_changed.emit(current_input)

## Touch Button Component
class TouchButton extends Control:
	signal pressed()
	signal released()
	
	var is_pressed: bool = false
	var touch_id: int = -1
	var button_texture: TextureRect
	
	func _ready():
		# Create button visual
		button_texture = TextureRect.new()
		button_texture.expand_mode = TextureRect.EXPAND_FIT_WIDTH_PROPORTIONAL
		button_texture.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
		add_child(button_texture)
		
		# Set default size
		custom_minimum_size = Vector2(60, 60)
	
	func _gui_input(event):
		if event is InputEventScreenTouch:
			if event.pressed and not is_pressed:
				is_pressed = true
				touch_id = event.index
				modulate = Color(0.8, 0.8, 0.8)  # Visual feedback
				pressed.emit()
			elif not event.pressed and event.index == touch_id:
				is_pressed = false
				touch_id = -1
				modulate = Color.WHITE
				released.emit()
	
	func set_button_texture(texture: Texture2D):
		if button_texture:
			button_texture.texture = texture

func _ready():
	# Initialize touch controls
	_setup_joystick()
	_setup_buttons()
	_setup_look_area()
	_load_control_settings()
	
	# Only show on mobile devices
	visible = OS.has_feature("mobile")
	
	print("📱 Touch Controls initialized")

func _setup_joystick():
	"""Setup movement joystick"""
	if movement_joystick:
		movement_joystick.input_changed.connect(_on_movement_input)
		movement_joystick.position = Vector2(80, get_viewport().size.y - 120)

func _setup_buttons():
	"""Setup touch buttons"""
	var viewport_size = get_viewport().size
	
	# Fire button (right side, thumb position)
	if fire_button:
		fire_button.position = Vector2(viewport_size.x - 100, viewport_size.y - 120)
		fire_button.pressed.connect(fire_pressed.emit)
		fire_button.released.connect(fire_released.emit)
		fire_button.modulate.a = button_opacity
	
	# Reload button (above fire button)
	if reload_button:
		reload_button.position = Vector2(viewport_size.x - 100, viewport_size.y - 200)
		reload_button.pressed.connect(reload_pressed.emit)
		reload_button.modulate.a = button_opacity
	
	# Jump button (right side, higher)
	if jump_button:
		jump_button.position = Vector2(viewport_size.x - 180, viewport_size.y - 180)
		jump_button.pressed.connect(jump_pressed.emit)
		jump_button.modulate.a = button_opacity
	
	# Crouch button (left side, above joystick)
	if crouch_button:
		crouch_button.position = Vector2(160, viewport_size.y - 120)
		crouch_button.pressed.connect(crouch_pressed.emit)
		crouch_button.modulate.a = button_opacity
	
	# Weapon switch button (top right)
	if weapon_button:
		weapon_button.position = Vector2(viewport_size.x - 80, 80)
		weapon_button.pressed.connect(weapon_switch_pressed.emit)
		weapon_button.modulate.a = button_opacity

func _setup_look_area():
	"""Setup look/aim area"""
	if look_area:
		# Cover right half of screen for look input
		var viewport_size = get_viewport().size
		look_area.position = Vector2(viewport_size.x / 2, 0)
		look_area.size = Vector2(viewport_size.x / 2, viewport_size.y)
		look_area.gui_input.connect(_on_look_area_input)

func _load_control_settings():
	"""Load control customization settings"""
	# This would load saved control positions and sensitivity
	var settings_file = "user://touch_controls.cfg"
	var config = ConfigFile.new()
	
	if config.load(settings_file) == OK:
		look_sensitivity = config.get_value("controls", "look_sensitivity", 1.0)
		movement_deadzone = config.get_value("controls", "movement_deadzone", 0.2)
		look_deadzone = config.get_value("controls", "look_deadzone", 0.1)
		button_opacity = config.get_value("controls", "button_opacity", 0.7)

func _on_movement_input(input: Vector2):
	"""Handle movement joystick input"""
	if input.length() > movement_deadzone:
		movement_input.emit(input)
	else:
		movement_input.emit(Vector2.ZERO)

func _on_look_area_input(event):
	"""Handle look area touch input"""
	if event is InputEventScreenTouch:
		if event.pressed:
			look_touch_id = event.index
		elif event.index == look_touch_id:
			look_touch_id = -1
			look_input.emit(Vector2.ZERO)
	
	elif event is InputEventScreenDrag and event.index == look_touch_id:
		var look_delta = event.relative * look_sensitivity
		if look_delta.length() > look_deadzone:
			look_input.emit(look_delta)

## Public API

func set_look_sensitivity(sensitivity: float):
	"""Set look sensitivity"""
	look_sensitivity = clamp(sensitivity, 0.1, 3.0)
	_save_settings()

func set_button_opacity(opacity: float):
	"""Set button opacity"""
	button_opacity = clamp(opacity, 0.3, 1.0)
	
	# Update all buttons
	for button in [fire_button, reload_button, jump_button, crouch_button, weapon_button]:
		if button:
			button.modulate.a = button_opacity
	
	_save_settings()

func set_movement_deadzone(deadzone: float):
	"""Set movement joystick deadzone"""
	movement_deadzone = clamp(deadzone, 0.0, 0.5)
	_save_settings()

func set_look_deadzone(deadzone: float):
	"""Set look input deadzone"""
	look_deadzone = clamp(deadzone, 0.0, 0.3)
	_save_settings()

func enable_customization_mode():
	"""Enable button position customization"""
	# This would allow dragging buttons to new positions
	print("📱 Customization mode enabled")

func disable_customization_mode():
	"""Disable button position customization"""
	print("📱 Customization mode disabled")

func reset_to_defaults():
	"""Reset controls to default settings"""
	look_sensitivity = 1.0
	movement_deadzone = 0.2
	look_deadzone = 0.1
	button_opacity = 0.7
	
	_setup_buttons()
	_save_settings()

func _save_settings():
	"""Save control settings to file"""
	var config = ConfigFile.new()
	config.set_value("controls", "look_sensitivity", look_sensitivity)
	config.set_value("controls", "movement_deadzone", movement_deadzone)
	config.set_value("controls", "look_deadzone", look_deadzone)
	config.set_value("controls", "button_opacity", button_opacity)
	
	config.save("user://touch_controls.cfg")

## Haptic feedback

func trigger_haptic_feedback(intensity: float = 0.5):
	"""Trigger haptic feedback on supported devices"""
	if OS.has_feature("mobile"):
		# This would trigger device haptic feedback
		Input.vibrate_handheld(int(intensity * 100))

## Adaptive controls

func adapt_to_screen_size():
	"""Adapt control layout to screen size"""
	var viewport_size = get_viewport().size
	var is_tablet = viewport_size.x > 1000 or viewport_size.y > 1000
	
	if is_tablet:
		# Larger buttons and different positioning for tablets
		_setup_tablet_layout()
	else:
		# Standard phone layout
		_setup_phone_layout()

func _setup_tablet_layout():
	"""Setup controls for tablet screens"""
	# Larger buttons, more spread out
	if fire_button:
		fire_button.custom_minimum_size = Vector2(80, 80)
	
	# Adjust positions for larger screens
	_setup_buttons()

func _setup_phone_layout():
	"""Setup controls for phone screens"""
	# Standard button sizes
	if fire_button:
		fire_button.custom_minimum_size = Vector2(60, 60)
	
	# Standard positions
	_setup_buttons()

## Debug and utility

func get_control_stats() -> Dictionary:
	"""Get control usage statistics"""
	return {
		"look_sensitivity": look_sensitivity,
		"movement_deadzone": movement_deadzone,
		"look_deadzone": look_deadzone,
		"button_opacity": button_opacity,
		"active_touches": active_touches.size(),
		"movement_active": movement_joystick.is_active if movement_joystick else false,
		"look_active": look_touch_id != -1
	}

func _notification(what):
	"""Handle system notifications"""
	if what == NOTIFICATION_RESIZED:
		# Adapt to screen rotation/resize
		adapt_to_screen_size()
