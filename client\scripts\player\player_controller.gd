# Galaxy Guns 3D - Player Controller
# Touch-optimized FPS controller with gyroscope fine-aim and auto-sprint
# Designed for 60fps performance on iPhone 12 mini and above

class_name PlayerController
extends CharacterBody3D

## Signals for player events
signal health_changed(new_health: int, max_health: int)
signal ammo_changed(current_ammo: int, reserve_ammo: int)
signal weapon_changed(weapon_name: String)
signal player_died()
signal movement_state_changed(state: MovementState)

## Movement states for animation and audio
enum MovementState {
	IDLE,
	WALKING,
	RUNNING,
	CROUCHING,
	JUMPING,
	FALLING
}

## Player configuration
@export_group("Movement")
@export var walk_speed: float = 3.0
@export var run_speed: float = 6.0
@export var crouch_speed: float = 1.5
@export var jump_velocity: float = 8.0
@export var acceleration: float = 10.0
@export var friction: float = 15.0
@export var air_acceleration: float = 2.0

@export_group("Camera")
@export var mouse_sensitivity: float = 0.002
@export var touch_sensitivity: float = 0.003
@export var gyro_sensitivity: float = 0.5
@export var camera_smoothing: float = 10.0
@export var max_look_angle: float = 90.0

@export_group("Touch Controls")
@export var virtual_joystick_deadzone: float = 0.2
@export var touch_look_deadzone: float = 0.1
@export var auto_sprint_enabled: bool = true
@export var gyro_fine_aim_enabled: bool = true

@export_group("Player Stats")
@export var max_health: int = 100
@export var current_health: int = 100

## Node references
@onready var camera_pivot: Node3D = $CameraPivot
@onready var camera: Camera3D = $CameraPivot/Camera3D
@onready var collision_shape: CollisionShape3D = $CollisionShape3D
@onready var weapon_holder: Node3D = $CameraPivot/Camera3D/WeaponHolder
@onready var ui_manager: Control = get_node("/root/UIManager")

## Movement state
var current_movement_state: MovementState = MovementState.IDLE
var is_crouching: bool = false
var is_sprinting: bool = false
var can_jump: bool = true

## Camera control
var camera_rotation: Vector2 = Vector2.ZERO
var gyro_input: Vector2 = Vector2.ZERO
var touch_look_input: Vector2 = Vector2.ZERO

## Touch input handling
var movement_input: Vector2 = Vector2.ZERO
var is_touching_movement: bool = false
var is_touching_look: bool = false
var touch_positions: Dictionary = {}

## Weapon system
var current_weapon: WeaponBase = null
var weapon_manager: WeaponManager

## Physics
var gravity: float = ProjectSettings.get_setting("physics/3d/default_gravity")
var original_collision_height: float

func _ready():
	# Initialize player systems
	_setup_camera()
	_setup_collision()
	_setup_input()
	_setup_weapon_system()
	
	# Connect to game systems
	_connect_signals()
	
	# Enable gyroscope if available and enabled
	if gyro_fine_aim_enabled and Input.get_connected_joypads().size() == 0:
		Input.set_use_accumulated_input(false)
	
	print("🎮 Player Controller initialized")

func _setup_camera():
	"""Initialize camera settings for optimal mobile performance"""
	camera.fov = 75.0  # Slightly wider FOV for mobile
	camera.near = 0.1
	camera.far = 1000.0
	
	# Set camera position for FPS view
	camera_pivot.position = Vector3(0, 1.6, 0)  # Eye level
	camera.position = Vector3.ZERO

func _setup_collision():
	"""Setup collision shape for player"""
	if collision_shape.shape is CapsuleShape3D:
		var capsule = collision_shape.shape as CapsuleShape3D
		original_collision_height = capsule.height
	
	# Set collision layers
	collision_layer = 2  # Player layer
	collision_mask = 1 | 4 | 8  # World, enemies, projectiles

func _setup_input():
	"""Configure input handling for mobile"""
	# Capture mouse for desktop testing
	if not OS.has_feature("mobile"):
		Input.mouse_mode = Input.MOUSE_MODE_CAPTURED

func _setup_weapon_system():
	"""Initialize weapon management system"""
	weapon_manager = WeaponManager.new()
	add_child(weapon_manager)
	weapon_manager.weapon_changed.connect(_on_weapon_changed)
	weapon_manager.ammo_changed.connect(_on_ammo_changed)

func _connect_signals():
	"""Connect to game system signals"""
	# Connect to UI for touch input
	if ui_manager:
		ui_manager.movement_input.connect(_on_movement_input)
		ui_manager.look_input.connect(_on_look_input)
		ui_manager.fire_pressed.connect(_on_fire_pressed)
		ui_manager.fire_released.connect(_on_fire_released)
		ui_manager.reload_pressed.connect(_on_reload_pressed)
		ui_manager.jump_pressed.connect(_on_jump_pressed)
		ui_manager.crouch_pressed.connect(_on_crouch_pressed)

func _physics_process(delta):
	# Handle movement
	_handle_movement(delta)
	
	# Handle camera rotation
	_handle_camera_rotation(delta)
	
	# Update movement state
	_update_movement_state()
	
	# Apply physics
	move_and_slide()

func _handle_movement(delta):
	"""Handle player movement with touch controls"""
	# Add gravity
	if not is_on_floor():
		velocity.y -= gravity * delta
	
	# Handle jumping
	if Input.is_action_just_pressed("jump") and is_on_floor() and can_jump:
		velocity.y = jump_velocity
	
	# Get movement input (from touch or keyboard)
	var input_dir = Vector2.ZERO
	
	# Touch input (primary for mobile)
	if movement_input.length() > virtual_joystick_deadzone:
		input_dir = movement_input
	
	# Keyboard input (fallback for desktop testing)
	if not OS.has_feature("mobile"):
		input_dir.x = Input.get_action_strength("move_right") - Input.get_action_strength("move_left")
		input_dir.y = Input.get_action_strength("move_backward") - Input.get_action_strength("move_forward")
	
	# Calculate movement direction relative to camera
	var direction = Vector3.ZERO
	if input_dir.length() > 0:
		var camera_basis = camera_pivot.global_transform.basis
		direction = (camera_basis * Vector3(input_dir.x, 0, input_dir.y)).normalized()
	
	# Determine movement speed
	var target_speed = _get_movement_speed(input_dir.length())
	
	# Apply movement with acceleration/friction
	if direction.length() > 0:
		var accel = acceleration if is_on_floor() else air_acceleration
		velocity.x = move_toward(velocity.x, direction.x * target_speed, accel * delta)
		velocity.z = move_toward(velocity.z, direction.z * target_speed, accel * delta)
	else:
		# Apply friction when not moving
		if is_on_floor():
			velocity.x = move_toward(velocity.x, 0, friction * delta)
			velocity.z = move_toward(velocity.z, 0, friction * delta)

func _get_movement_speed(input_magnitude: float) -> float:
	"""Calculate movement speed based on input and state"""
	var base_speed = walk_speed
	
	if is_crouching:
		base_speed = crouch_speed
	elif auto_sprint_enabled and input_magnitude > 0.8:
		# Auto-sprint when moving at full stick deflection
		base_speed = run_speed
		is_sprinting = true
	elif Input.is_action_pressed("sprint"):
		base_speed = run_speed
		is_sprinting = true
	else:
		is_sprinting = false
	
	return base_speed

func _handle_camera_rotation(delta):
	"""Handle camera rotation with touch and gyroscope input"""
	var rotation_input = Vector2.ZERO
	
	# Touch look input (primary for mobile)
	if touch_look_input.length() > touch_look_deadzone:
		rotation_input += touch_look_input * touch_sensitivity
	
	# Gyroscope fine-aim (mobile only)
	if gyro_fine_aim_enabled and OS.has_feature("mobile"):
		var gyro = Input.get_gyroscope()
		if gyro.length() > 0.01:  # Deadzone for gyro noise
			gyro_input = Vector2(-gyro.x, gyro.y) * gyro_sensitivity
			rotation_input += gyro_input * 0.3  # Reduced sensitivity for fine aim
	
	# Mouse input (desktop testing)
	if not OS.has_feature("mobile") and Input.mouse_mode == Input.MOUSE_MODE_CAPTURED:
		rotation_input += Input.get_last_mouse_velocity() * mouse_sensitivity
	
	# Apply rotation with smoothing
	if rotation_input.length() > 0:
		camera_rotation.x -= rotation_input.y
		camera_rotation.y -= rotation_input.x
		
		# Clamp vertical rotation
		camera_rotation.x = clamp(camera_rotation.x, -deg_to_rad(max_look_angle), deg_to_rad(max_look_angle))
		
		# Apply rotation with smoothing
		var target_rotation = Vector3(camera_rotation.x, 0, 0)
		camera_pivot.rotation.x = lerp_angle(camera_pivot.rotation.x, target_rotation.x, camera_smoothing * delta)
		
		# Horizontal rotation applied to player body
		rotation.y = lerp_angle(rotation.y, camera_rotation.y, camera_smoothing * delta)

func _update_movement_state():
	"""Update movement state for animation and audio"""
	var new_state = MovementState.IDLE
	
	if not is_on_floor():
		if velocity.y > 0:
			new_state = MovementState.JUMPING
		else:
			new_state = MovementState.FALLING
	elif is_crouching:
		new_state = MovementState.CROUCHING
	elif velocity.length() > 0.1:
		if is_sprinting:
			new_state = MovementState.RUNNING
		else:
			new_state = MovementState.WALKING
	
	if new_state != current_movement_state:
		current_movement_state = new_state
		movement_state_changed.emit(new_state)

## Input event handlers

func _on_movement_input(input: Vector2):
	"""Handle virtual joystick movement input"""
	movement_input = input

func _on_look_input(input: Vector2):
	"""Handle touch look input"""
	touch_look_input = input

func _on_fire_pressed():
	"""Handle fire button press"""
	if current_weapon:
		current_weapon.start_firing()

func _on_fire_released():
	"""Handle fire button release"""
	if current_weapon:
		current_weapon.stop_firing()

func _on_reload_pressed():
	"""Handle reload button press"""
	if current_weapon:
		current_weapon.reload()

func _on_jump_pressed():
	"""Handle jump button press"""
	if is_on_floor() and can_jump:
		velocity.y = jump_velocity

func _on_crouch_pressed():
	"""Toggle crouch state"""
	set_crouching(not is_crouching)

## Public API

func set_crouching(crouching: bool):
	"""Set crouch state with collision adjustment"""
	if crouching == is_crouching:
		return
	
	is_crouching = crouching
	
	# Adjust collision shape
	if collision_shape.shape is CapsuleShape3D:
		var capsule = collision_shape.shape as CapsuleShape3D
		if is_crouching:
			capsule.height = original_collision_height * 0.6
			camera_pivot.position.y = 1.0  # Lower camera
		else:
			capsule.height = original_collision_height
			camera_pivot.position.y = 1.6  # Normal camera height

func take_damage(damage: int, source: Node = null):
	"""Apply damage to player"""
	current_health = max(0, current_health - damage)
	health_changed.emit(current_health, max_health)
	
	if current_health <= 0:
		_handle_death()

func heal(amount: int):
	"""Heal player"""
	current_health = min(max_health, current_health + amount)
	health_changed.emit(current_health, max_health)

func equip_weapon(weapon_resource: WeaponResource):
	"""Equip a new weapon"""
	if weapon_manager:
		weapon_manager.equip_weapon(weapon_resource)

func get_aim_direction() -> Vector3:
	"""Get current aim direction for weapons"""
	return -camera.global_transform.basis.z

func is_aiming() -> bool:
	"""Check if player is currently aiming"""
	return current_weapon != null and current_weapon.is_aiming()

## Private methods

func _handle_death():
	"""Handle player death"""
	print("💀 Player died")
	player_died.emit()
	
	# Disable input
	set_physics_process(false)
	
	# Play death animation/effects
	# This would trigger death screen UI

func _on_weapon_changed(weapon: WeaponBase):
	"""Handle weapon change event"""
	current_weapon = weapon
	if weapon:
		weapon_changed.emit(weapon.weapon_name)

func _on_ammo_changed(current: int, reserve: int):
	"""Handle ammo change event"""
	ammo_changed.emit(current, reserve)

## Debug and utility methods

func get_movement_info() -> Dictionary:
	"""Get current movement information for debugging"""
	return {
		"velocity": velocity,
		"speed": velocity.length(),
		"state": MovementState.keys()[current_movement_state],
		"on_floor": is_on_floor(),
		"is_crouching": is_crouching,
		"is_sprinting": is_sprinting,
		"health": current_health,
		"camera_rotation": camera_rotation
	}

func reset_player():
	"""Reset player to initial state"""
	current_health = max_health
	velocity = Vector3.ZERO
	camera_rotation = Vector2.ZERO
	is_crouching = false
	is_sprinting = false
	set_physics_process(true)
	
	health_changed.emit(current_health, max_health)
