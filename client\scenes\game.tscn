[gd_scene load_steps=7 format=3 uid="uid://c8xr8n4qxqyh"]

[ext_resource type="PackedScene" uid="uid://player_scene" path="res://scenes/player.tscn" id="1_player"]
[ext_resource type="Script" path="res://scripts/game_manager.gd" id="1_game_manager"]
[ext_resource type="PackedScene" uid="uid://ui_scene" path="res://scenes/ui/game_ui.tscn" id="2_ui"]

[sub_resource type="NavigationMesh" id="NavigationMesh_1"]

[sub_resource type="BoxMesh" id="BoxMesh_ground"]
size = Vector3(1, 0.1, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_ground"]
size = Vector3(1, 0.1, 1)

[node name="Game" type="Node3D"]
script = ExtResource("1_game_manager")

[node name="Environment" type="Node3D" parent="."]

[node name="NavigationRegion3D" type="NavigationRegion3D" parent="Environment"]
navigation_mesh = SubResource("NavigationMesh_1")

[node name="Ground" type="StaticBody3D" parent="Environment/NavigationRegion3D"]

[node name="GroundMesh" type="MeshInstance3D" parent="Environment/NavigationRegion3D/Ground"]
transform = Transform3D(100, 0, 0, 0, 1, 0, 0, 0, 100, 0, 0, 0)
mesh = SubResource("BoxMesh_ground")

[node name="GroundCollision" type="CollisionShape3D" parent="Environment/NavigationRegion3D/Ground"]
transform = Transform3D(100, 0, 0, 0, 1, 0, 0, 0, 100, 0, 0, 0)
shape = SubResource("BoxShape3D_ground")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
light_energy = 1.0
shadow_enabled = true

[node name="Player" parent="." instance=ExtResource("1_player")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, 0)

[node name="EnemySpawner" type="Node3D" parent="."]

[node name="UI" parent="." instance=ExtResource("2_ui")]
