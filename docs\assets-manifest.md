# Galaxy Guns 3D - Assets Manifest
**Version:** 1.0  
**Date:** July 15, 2025  
**Total Estimated Assets:** 2,847 files  
**Estimated Storage:** 1.8GB (compressed)  

---

## 📊 Asset Overview

| Category | Count | Size (MB) | Format | Priority |
|----------|-------|-----------|---------|----------|
| Sprites | 1,200 | 450 | PNG/ASTC | High |
| Audio | 380 | 280 | OGG Vorbis | High |
| UI Elements | 450 | 120 | PNG/SVG | High |
| Shaders | 25 | 2 | GLSL | High |
| 3D Models | 180 | 200 | GLTF2 | Medium |
| Animations | 320 | 150 | Godot .tres | High |
| Fonts | 8 | 12 | TTF/OTF | Medium |
| Textures | 284 | 586 | ASTC/PNG | High |

---

## 🎨 Sprite Sheets & Characters

### Player Characters
```
/assets/sprites/characters/
├── player_base/
│   ├── idle_32x32_12f.png (4x variants)
│   ├── walk_32x32_8f.png
│   ├── run_32x32_6f.png
│   ├── jump_32x32_4f.png
│   ├── crouch_32x32_6f.png
│   └── death_32x32_8f.png
├── customization/
│   ├── helmets/ (24 variants)
│   ├── armor/ (18 variants)
│   ├── accessories/ (32 variants)
│   └── color_masks/ (RGB variants)
└── enemies/
    ├── bot_soldier/ (full animation set)
    ├── bot_heavy/ (full animation set)
    └── bot_sniper/ (full animation set)
```

### Weapons & Equipment
```
/assets/sprites/weapons/
├── assault_rifles/
│   ├── ak47_32x16.png (idle, fire, reload)
│   ├── m4a1_32x16.png
│   ├── scar_32x16.png
│   └── famas_32x16.png
├── sniper_rifles/
│   ├── awp_48x16.png
│   ├── barrett_52x16.png
│   └── dragunov_44x16.png
├── pistols/
│   ├── glock_16x12.png
│   ├── deagle_20x14.png
│   └── revolver_18x14.png
├── explosives/
│   ├── grenade_8x8_6f.png
│   ├── rocket_16x4.png
│   └── c4_12x8.png
└── attachments/
    ├── scopes/ (8 variants)
    ├── silencers/ (4 variants)
    └── grips/ (6 variants)
```

---

## 🌍 Environment Assets

### Map Tiles & Structures
```
/assets/sprites/environment/
├── terrain/
│   ├── grass_tileset_16x16.png (64 tiles)
│   ├── concrete_tileset_16x16.png (48 tiles)
│   ├── metal_tileset_16x16.png (32 tiles)
│   └── sand_tileset_16x16.png (40 tiles)
├── buildings/
│   ├── warehouse/ (walls, doors, windows)
│   ├── office/ (cubicles, desks, computers)
│   ├── factory/ (machinery, pipes, tanks)
│   └── bunker/ (concrete, steel, barriers)
├── props/
│   ├── crates/ (wooden, metal, explosive)
│   ├── barrels/ (oil, toxic, explosive)
│   ├── vehicles/ (cars, trucks, tanks)
│   └── decorative/ (plants, signs, debris)
└── skyboxes/
    ├── desert_sunset_2048x1024.png
    ├── urban_night_2048x1024.png
    ├── space_station_2048x1024.png
    └── arctic_dawn_2048x1024.png
```

### Interactive Elements
```
/assets/sprites/interactive/
├── doors/ (sliding, hinged, blast)
├── switches/ (buttons, levers, panels)
├── elevators/ (platforms, cables)
├── spawn_points/ (team markers)
└── objectives/ (flags, control points)
```

---

## ✨ Visual Effects (VFX)

### Particle Systems
```
/assets/vfx/
├── weapon_effects/
│   ├── muzzle_flash/ (per weapon type)
│   ├── bullet_trails/ (tracer rounds)
│   ├── shell_casings/ (brass physics)
│   └── impact_sparks/ (metal, concrete, flesh)
├── explosions/
│   ├── grenade_explosion_64x64_16f.png
│   ├── rocket_explosion_96x96_20f.png
│   ├── barrel_explosion_80x80_18f.png
│   └── smoke_clouds/ (various sizes)
├── environmental/
│   ├── dust_particles/ (ambient, footsteps)
│   ├── water_splashes/ (puddles, rain)
│   ├── fire_effects/ (flames, embers)
│   └── electrical/ (sparks, lightning)
└── ui_effects/
    ├── damage_numbers/ (floating text)
    ├── hit_markers/ (crosshair feedback)
    ├── screen_effects/ (blood, flash)
    └── transitions/ (fade, wipe, zoom)
```

---

## 🎵 Audio Assets

### Sound Effects (SFX)
```
/assets/audio/sfx/
├── weapons/
│   ├── gunshots/ (44.1kHz, 16-bit, mono)
│   │   ├── ak47_fire.ogg
│   │   ├── m4a1_fire.ogg
│   │   ├── awp_fire.ogg
│   │   └── [15 more weapon sounds]
│   ├── reloads/ (magazine, shell, clip)
│   ├── foley/ (draw, holster, cock)
│   └── impacts/ (flesh, metal, concrete)
├── explosions/
│   ├── grenade_explode.ogg
│   ├── rocket_explode.ogg
│   ├── barrel_explode.ogg
│   └── distant_explosions/ (3 variants)
├── movement/
│   ├── footsteps/ (concrete, grass, metal, sand)
│   ├── jumping/ (land, grunt, effort)
│   ├── climbing/ (ladder, rope, wall)
│   └── swimming/ (splash, stroke, bubble)
├── environment/
│   ├── ambient/ (wind, machinery, birds)
│   ├── interactive/ (doors, switches, alarms)
│   └── destruction/ (glass, wood, metal)
└── ui/
    ├── menu_sounds/ (click, hover, confirm)
    ├── notifications/ (achievement, message)
    └── feedback/ (error, success, warning)
```

### Music & Soundtracks
```
/assets/audio/music/
├── menu_themes/
│   ├── main_menu_loop.ogg (2:30, seamless)
│   ├── loading_ambient.ogg (1:45)
│   └── victory_fanfare.ogg (0:15)
├── gameplay_tracks/
│   ├── action_low_bpm.ogg (120 BPM, 3:00)
│   ├── action_med_bpm.ogg (140 BPM, 3:20)
│   ├── action_high_bpm.ogg (160 BPM, 2:45)
│   └── tension_buildup.ogg (Variable BPM)
└── adaptive_layers/
    ├── drums_layer.ogg
    ├── bass_layer.ogg
    ├── melody_layer.ogg
    └── harmony_layer.ogg
```

### Voice & VOIP
```
/assets/audio/voice/
├── announcer/
│   ├── match_start.ogg
│   ├── team_victory.ogg
│   ├── team_defeat.ogg
│   └── objective_callouts/ (20 variants)
├── character_voices/
│   ├── pain_sounds/ (male/female variants)
│   ├── effort_sounds/ (jumping, climbing)
│   └── death_sounds/ (4 variants each)
└── radio_chatter/
    ├── enemy_spotted.ogg
    ├── reloading.ogg
    ├── need_backup.ogg
    └── [15 more tactical callouts]
```

---

## 🖼 User Interface Assets

### HUD Elements
```
/assets/ui/hud/
├── crosshairs/ (8 customizable variants)
├── health_bars/ (animated, gradient)
├── ammo_counters/ (digital, analog styles)
├── minimap/ (circular, square variants)
├── damage_indicators/ (directional arrows)
├── objective_markers/ (waypoints, flags)
└── kill_feed/ (background, icons)
```

### Menu Systems
```
/assets/ui/menus/
├── backgrounds/ (animated, parallax)
├── buttons/ (normal, hover, pressed states)
├── panels/ (transparent, solid, gradient)
├── icons/ (weapons, achievements, settings)
├── progress_bars/ (XP, loading, health)
└── modal_dialogs/ (confirmation, error, info)
```

### Customization UI
```
/assets/ui/customization/
├── character_preview/ (3D viewport frame)
├── weapon_showcase/ (rotation platform)
├── color_pickers/ (RGB, HSV wheels)
├── category_tabs/ (helmets, armor, weapons)
└── preview_buttons/ (zoom, rotate, reset)
```

---

## 🎯 Shader Assets

### Visual Enhancement Shaders
```
/assets/shaders/
├── pixel_lighting.gdshader (normal mapping for sprites)
├── water_surface.gdshader (animated water effects)
├── screen_distortion.gdshader (explosion effects)
├── outline_highlight.gdshader (enemy highlighting)
├── damage_flash.gdshader (screen damage effect)
└── ui_blur.gdshader (background blur for menus)
```

---

## 📝 Font Assets

### Typography
```
/assets/fonts/
├── primary_ui.ttf (Orbitron - futuristic, clean)
├── secondary_ui.ttf (Roboto - readable, modern)
├── pixel_display.ttf (Perfect DOS VGA - retro feel)
├── damage_numbers.ttf (Impact - bold, visible)
└── localization/
    ├── chinese_simplified.ttf
    ├── japanese.ttf
    ├── korean.ttf
    └── arabic.ttf
```

---

## 🔧 Technical Assets

### Configuration Files
```
/assets/data/
├── weapon_stats.json (damage, range, accuracy)
├── map_layouts.json (spawn points, objectives)
├── progression_tables.json (XP requirements, rewards)
├── localization_strings.json (all UI text)
└── balance_config.json (gameplay tuning values)
```

### Asset Processing Pipeline
- **Source Format:** PNG (uncompressed, 32-bit RGBA)
- **Target Format:** ASTC 4x4 (iOS optimized compression)
- **Batch Processing:** Aseprite CLI + custom Python scripts
- **Quality Control:** Automated pixel-perfect validation
- **Version Control:** Git LFS for binary assets

---

## 📈 Asset Optimization Strategy

### Performance Targets
- **Texture Memory:** <512MB peak usage
- **Audio Memory:** <128MB simultaneous playback
- **Loading Times:** <3 seconds per map
- **Streaming:** Progressive asset loading for large maps

### Compression Settings
- **Sprites:** ASTC 4x4 (6:1 ratio, high quality)
- **Audio:** OGG Vorbis Q6 (192kbps equivalent)
- **UI Elements:** PNG with palette optimization
- **Fonts:** WOFF2 compression for web compatibility
