# Galaxy Guns 3D - Fastlane Configuration
# Automated iOS build and deployment pipeline

default_platform(:ios)

# Global variables
BUNDLE_ID = "com.orvyn.galaxyguns"
APP_NAME = "Galaxy Guns 3D"
SCHEME = "Galaxy Guns 3D"
WORKSPACE = "client/GalaxyGuns.xcworkspace"
PROJECT = "client/GalaxyGuns.xcodeproj"

platform :ios do
  
  # ==========================================
  # SETUP AND CONFIGURATION
  # ==========================================
  
  desc "Setup development environment"
  lane :setup do
    # Install or update CocoaPods
    cocoapods(
      podfile: "client/Podfile",
      try_repo_update_on_error: true
    )
    
    # Setup match for code signing
    match(
      type: "development",
      app_identifier: BUNDLE_ID,
      readonly: true
    )
    
    UI.success("✅ Development environment setup complete!")
  end
  
  # ==========================================
  # TESTING
  # ==========================================
  
  desc "Run all tests"
  lane :test do
    # Run unit tests
    run_tests(
      workspace: WORKSPACE,
      scheme: SCHEME,
      device: "iPhone 15 Pro",
      clean: true,
      code_coverage: true,
      output_directory: "./test_output",
      output_types: "html,junit"
    )
    
    # Upload test results to CI
    trainer(path: "./test_output")
    
    UI.success("✅ All tests passed!")
  end
  
  desc "Run performance tests"
  lane :performance_test do
    # Build for testing
    build_for_testing(
      workspace: WORKSPACE,
      scheme: SCHEME,
      configuration: "Release",
      destination: "platform=iOS Simulator,name=iPhone 15 Pro"
    )
    
    # Run performance tests
    run_tests(
      workspace: WORKSPACE,
      scheme: SCHEME,
      device: "iPhone 15 Pro",
      test_without_building: true,
      only_testing: ["GalaxyGunsPerformanceTests"]
    )
    
    UI.success("✅ Performance tests completed!")
  end
  
  # ==========================================
  # BUILDING
  # ==========================================
  
  desc "Build for development"
  lane :build_dev do
    # Update build number
    increment_build_number(
      xcodeproj: PROJECT,
      build_number: latest_testflight_build_number + 1
    )
    
    # Setup code signing
    match(
      type: "development",
      app_identifier: BUNDLE_ID
    )
    
    # Build the app
    build_app(
      workspace: WORKSPACE,
      scheme: SCHEME,
      configuration: "Debug",
      export_method: "development",
      output_directory: "./builds/ios",
      output_name: "GalaxyGuns-Dev.ipa",
      clean: true,
      include_bitcode: false,
      include_symbols: true
    )
    
    UI.success("✅ Development build complete!")
  end
  
  desc "Build for TestFlight"
  lane :build_beta do
    # Ensure clean working directory
    ensure_git_status_clean
    
    # Update version and build number
    increment_build_number(
      xcodeproj: PROJECT,
      build_number: latest_testflight_build_number + 1
    )
    
    # Setup code signing for App Store
    match(
      type: "appstore",
      app_identifier: BUNDLE_ID
    )
    
    # Build the app
    build_app(
      workspace: WORKSPACE,
      scheme: SCHEME,
      configuration: "Release",
      export_method: "app-store",
      output_directory: "./builds/ios",
      output_name: "GalaxyGuns-Beta.ipa",
      clean: true,
      include_bitcode: false,
      include_symbols: true
    )
    
    UI.success("✅ Beta build complete!")
  end
  
  desc "Build for App Store release"
  lane :build_release do
    # Ensure clean working directory
    ensure_git_status_clean
    
    # Verify we're on main branch
    ensure_git_branch(branch: "main")
    
    # Update version number
    version = prompt(text: "Enter version number (e.g., 1.0.0): ")
    increment_version_number(
      xcodeproj: PROJECT,
      version_number: version
    )
    
    # Reset build number to 1 for new version
    increment_build_number(
      xcodeproj: PROJECT,
      build_number: 1
    )
    
    # Setup code signing for App Store
    match(
      type: "appstore",
      app_identifier: BUNDLE_ID
    )
    
    # Build the app
    build_app(
      workspace: WORKSPACE,
      scheme: SCHEME,
      configuration: "Release",
      export_method: "app-store",
      output_directory: "./builds/ios",
      output_name: "GalaxyGuns-Release.ipa",
      clean: true,
      include_bitcode: false,
      include_symbols: true
    )
    
    # Commit version bump
    commit_version_bump(
      xcodeproj: PROJECT,
      message: "chore: bump version to #{version}"
    )
    
    # Create git tag
    add_git_tag(tag: "v#{version}")
    
    UI.success("✅ Release build complete!")
  end
  
  # ==========================================
  # DEPLOYMENT
  # ==========================================
  
  desc "Deploy to TestFlight"
  lane :beta do
    # Build for beta
    build_beta
    
    # Upload to TestFlight
    upload_to_testflight(
      ipa: "./builds/ios/GalaxyGuns-Beta.ipa",
      skip_waiting_for_build_processing: false,
      changelog: changelog_from_git_commits(
        commits_count: 10,
        pretty: "- %s"
      ),
      groups: ["Internal Testers", "External Testers"],
      notify_external_testers: true
    )
    
    # Send notification to Slack/Discord
    slack(
      message: "🚀 Galaxy Guns 3D Beta #{get_build_number} uploaded to TestFlight!",
      channel: "#galaxy-guns-dev",
      success: true
    )
    
    UI.success("✅ Beta deployed to TestFlight!")
  end
  
  desc "Deploy to App Store"
  lane :release do
    # Build for release
    build_release
    
    # Upload to App Store
    upload_to_app_store(
      ipa: "./builds/ios/GalaxyGuns-Release.ipa",
      force: true,
      reject_if_possible: true,
      skip_metadata: false,
      skip_screenshots: false,
      submit_for_review: true,
      automatic_release: false,
      submission_information: {
        add_id_info_limits_tracking: true,
        add_id_info_serves_ads: false,
        add_id_info_tracks_action: true,
        add_id_info_tracks_install: true,
        add_id_info_uses_idfa: true,
        content_rights_has_rights: true,
        content_rights_contains_third_party_content: false,
        export_compliance_platform: "ios",
        export_compliance_compliance_required: false,
        export_compliance_encryption_updated: false,
        export_compliance_app_type: nil,
        export_compliance_uses_encryption: false
      }
    )
    
    # Push git changes
    push_to_git_remote
    
    # Send notification
    slack(
      message: "🎉 Galaxy Guns 3D v#{get_version_number} submitted to App Store!",
      channel: "#galaxy-guns-releases",
      success: true
    )
    
    UI.success("✅ Release submitted to App Store!")
  end
  
  # ==========================================
  # UTILITY LANES
  # ==========================================
  
  desc "Update certificates and provisioning profiles"
  lane :certificates do
    match(
      type: "development",
      app_identifier: BUNDLE_ID,
      force_for_new_devices: true
    )
    
    match(
      type: "appstore",
      app_identifier: BUNDLE_ID
    )
    
    UI.success("✅ Certificates updated!")
  end
  
  desc "Generate screenshots for App Store"
  lane :screenshots do
    # Capture screenshots using snapshot
    snapshot(
      workspace: WORKSPACE,
      scheme: SCHEME,
      devices: [
        "iPhone 15 Pro Max",
        "iPhone 15 Pro",
        "iPhone SE (3rd generation)",
        "iPad Pro (12.9-inch) (6th generation)"
      ],
      languages: ["en-US"],
      output_directory: "./marketing/screenshots",
      clear_previous_screenshots: true,
      override_status_bar: true,
      localize_simulator: true
    )
    
    UI.success("✅ Screenshots generated!")
  end
  
  desc "Update App Store metadata"
  lane :metadata do
    deliver(
      skip_binary_upload: true,
      skip_screenshots: true,
      force: true,
      metadata_path: "./marketing/metadata"
    )
    
    UI.success("✅ Metadata updated!")
  end
  
  desc "Clean build artifacts"
  lane :clean do
    # Clean Xcode build folder
    clear_derived_data
    
    # Clean Fastlane build artifacts
    sh("rm -rf ../builds/ios/*.ipa")
    sh("rm -rf ../test_output")
    
    UI.success("✅ Build artifacts cleaned!")
  end
  
  # ==========================================
  # ERROR HANDLING
  # ==========================================
  
  error do |lane, exception|
    slack(
      message: "❌ Galaxy Guns 3D build failed in lane '#{lane}': #{exception.message}",
      channel: "#galaxy-guns-dev",
      success: false
    )
  end
  
end

# ==========================================
# HELPER FUNCTIONS
# ==========================================

def changelog_from_git_commits(commits_count: 10, pretty: "- %s")
  changelog = sh("git log --oneline -#{commits_count} --pretty='#{pretty}'", log: false)
  changelog.strip
rescue
  "No recent commits found"
end
