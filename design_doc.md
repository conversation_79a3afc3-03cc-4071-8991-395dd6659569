# Galaxy Guns 3D - Complete Progression System Design Document

## Overview
Galaxy Guns 3D has been expanded from a simple arena shooter into a comprehensive RPG-style progression system featuring procedural dungeons, galactic exploration, and deep character advancement mechanics.

## Core Systems

### 1. Galactic Map & Sector System
**Node-Based Galaxy Navigation**
- **Sector Types**: Common (standard difficulty), Rare (environmental hazards), Boss Lairs (major encounters)
- **Progression Gates**: NavKey Fragments unlock new galactic rings
- **Meta-Loop**: Clear sectors → earn fragments → unlock new areas → face greater challenges

**Implementation**:
- Interactive star map with clickable sectors
- Visual progression indicators (locked/unlocked/cleared states)
- Lore snippets and ASCII starfield backgrounds
- Connection-based unlocking system

### 2. Procedural Dungeon Generation
**Room-Based Architecture**
- **Size Range**: 20×20 to 40×40 cells per dungeon
- **Room Types**: Start Bay, Combat Hall, Elite Gauntlet, Treasure Vault, Puzzle Hub, Boss Chamber
- **Connectivity**: Guaranteed primary path with 35% branch probability
- **Security**: Colored keycards (red/blue/yellow) for locked doors

**Generation Algorithm**:
```
1. Create room graph with guaranteed start→boss path
2. Add branch rooms based on probability
3. Place locked doors requiring keycards
4. Populate rooms with enemies based on danger rating
5. Apply biome theming and decorative layers
```

### 3. Enemy System & AI (30+ Types)
**Classification Hierarchy**:
- **Trash Tier** (Waves 1-5): Drone Swarm, Scout Bot, Repair Drone
- **Shooter Tier** (Waves 3-10): Shock Trooper, Plasma Gunner, Sniper Unit
- **Assassin Tier** (Waves 5-15): Phase Specter, Shadow Stalker, Void Hunter
- **Heavy Tier** (Waves 8-20): Obliterator Bot, Siege Mech, Titan Crusher
- **Elite Variants** (Waves 10+): Enhanced versions with special affixes
- **Legendary Types** (Waves 25+): Apex Predator, Omega Construct

**Elite Affix System**:
- Reflective Bullets, Rapid Fire, Explosive Rounds, Piercing
- Invisibility, Poison Blade, Critical Shots, Stealth
- Explosive Death, Shielded, Armor Plated, Damage Reduction

### 4. Boss Framework (5 Unique Encounters)
**Multi-Phase Design**:
Each boss features 3 distinct phases triggered at 75%, 50%, and 25% health thresholds.

**Boss Roster**:
1. **Warp Hydra** (Time Distortion) - Hexagonal arena, temporal mechanics
2. **Gravemind Core** (Bio-Horror) - Circular arena, organic minions
3. **Void Empress** (Dimensional Chaos) - Octagonal arena, reality manipulation
4. **Quantum Overlord** (Probability Control) - Square arena, quantum effects
5. **Stellar Devourer** (Cosmic Horror) - Circular arena, stellar phenomena

**Enrage Mechanics**:
- 4-8 minute soft enrage timers
- Damage ×1.5, attack rate ×2 when enraged
- Unique rewards: legendary items and permanent upgrades

### 5. Loot & Progression Systems
**Equipment Categories**:
- **Weapons**: 5 types with mod slots (Barrel, Core, Sight, Utility)
- **Armor**: Health bonuses and special defensive effects
- **Accessories**: Utility enhancements and combat modifiers

**Rarity Tiers**: Common → Uncommon → Rare → Epic → Legendary
- Visual indicators: color-coded borders and glow effects
- Stat scaling and special ability unlocks per tier

**Currencies**:
- **Credits**: In-run currency for immediate purchases
- **Aurite Cores**: Meta-progression permanent upgrades
- **Blueprint Shards**: Unlock new weapon types and modifications

### 6. Puzzle & Event System
**Interactive Challenges**:
- **Pressure Plates**: Binary sequence puzzles with timed inputs
- **Power Couplers**: Pipe-connection style rotation puzzles
- **Data Terminals**: Pattern matching under time pressure

**Risk/Reward Events**:
- **Shrines**: Sacrifice health/ammo for temporary power boosts
- **Secret Vendors**: 1% spawn chance, rare goods at premium prices
- **Lore Terminals**: 5% spawn chance, galactic history fragments

## Technical Implementation

### Architecture Overview
```
/Scripts
    /Dungeon   - DungeonGenerator.cs, Room.cs, BiomeManager.cs
    /AI        - EnemyBase.cs, BossController.cs, EliteAffix.cs
    /Loot      - LootTable.cs, Item.cs, Inventory.cs, RaritySystem.cs
    /Systems   - SaveManager.cs, MapManager.cs, EventBus.cs
    /UI        - MinimapUI.cs, DialogueBox.cs, ProgressionUI.cs
```

### Key Components
**DungeonGenerator.cs**:
- Seeded random generation for consistent layouts
- Room graph creation with connectivity validation
- NavMesh runtime baking for AI pathfinding
- Biome application and decoration placement

**BossController.cs**:
- Phase transition management
- Mechanic execution system
- Enrage timer implementation
- Reward distribution on defeat

**SaveManager.cs**:
- JSON serialization of progression state
- Sector unlock tracking
- Equipment and upgrade persistence
- Achievement progress storage

### Performance Considerations
- **Target**: 60fps on mobile devices
- **Optimization**: Object pooling for bullets/particles
- **Memory**: Texture atlasing for equipment sprites
- **Loading**: Asynchronous dungeon generation
- **Rendering**: Frustum culling for large dungeons

## Art & Audio Direction

### Visual Style
- **Palette**: #00FF99 neon accents on #0B0F1A dark backgrounds
- **Effects**: Thin bloom, chromatic aberration on dash, screen shake on heavy damage
- **UI**: Minimalist geometric design with clean typography
- **Boss Arenas**: Unique ambient gradients per encounter

### Audio Design
- **UI**: Retro-synth feedback sounds
- **Combat**: Bassy woosh for dashes, layered explosions
- **Music**: Adaptive soundtrack adding percussion layers per danger rank
- **Ambience**: Biome-specific environmental audio loops

## Progression Flow

### New Player Experience
1. **Tutorial**: Basic arena mode introduces core mechanics
2. **Galaxy Map**: Press M to access sector selection
3. **First Dungeon**: Guided experience through room types
4. **Equipment Discovery**: Loot drops and rarity explanation
5. **Boss Encounter**: First major challenge with clear telegraphs

### Long-Term Engagement
- **Sector Progression**: Unlock new galactic rings with NavKey fragments
- **Meta Upgrades**: Permanent character improvements via Aurite Cores
- **Collection Goals**: Complete equipment sets and legendary items
- **Achievement System**: Milestone rewards and completion tracking
- **Daily Challenges**: Rotating objectives for bonus rewards

## Balancing Philosophy

### Difficulty Scaling
- **Enemy Health**: +15% per wave/sector difficulty
- **Player Power**: Equipment and level progression counteracts scaling
- **Boss Encounters**: Skill-based with clear telegraphs and patterns
- **Loot Distribution**: Rare items feel impactful but not mandatory

### Progression Pacing
- **Early Game**: Frequent upgrades and clear progression markers
- **Mid Game**: Strategic choices between equipment and playstyles
- **End Game**: Perfecting builds and tackling ultimate challenges
- **Retention**: Daily challenges and rotating content

## Future Expansion Opportunities
- **New Biomes**: Ice worlds, volcanic planets, space stations
- **Weapon Crafting**: Combine materials for custom equipment
- **Multiplayer**: Co-op dungeon runs and competitive modes
- **Seasonal Events**: Limited-time sectors and exclusive rewards
- **Story Campaign**: Narrative-driven progression through galactic conflict

---

*This design document represents the complete vision for Galaxy Guns 3D's progression system expansion, providing a roadmap for implementation and future development.*
