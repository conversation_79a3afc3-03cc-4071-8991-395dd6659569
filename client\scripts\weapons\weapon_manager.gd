# Galaxy Guns 3D - Weapon Manager
# Manages weapon inventory, switching, and JSON-driven weapon configuration
# Supports hot-swapping weapons and real-time balance adjustments

class_name WeaponManager
extends Node

## Signals for weapon management
signal weapon_changed(weapon: WeaponBase)
signal weapon_equipped(weapon_name: String)
signal weapon_dropped(weapon_name: String)
signal ammo_changed(current_ammo: int, reserve_ammo: int)
signal inventory_changed()

## Weapon inventory
var weapon_inventory: Array[WeaponResource] = []
var current_weapon_index: int = 0
var current_weapon: WeaponBase = null
var weapon_holder: Node3D

## Weapon configurations loaded from JSON
var weapon_configs: Dictionary = {}
var weapon_scenes: Dictionary = {}

## Default weapon loadout
var default_weapons: Array[String] = ["pistol", "assault_rifle"]

func _ready():
	# Load weapon configurations
	_load_weapon_configs()
	_load_weapon_scenes()
	
	# Get weapon holder reference
	weapon_holder = get_parent().get_node("CameraPivot/Camera3D/WeaponHolder")
	
	# Load default weapons
	_load_default_weapons()
	
	print("🔫 Weapon Manager initialized with %d weapons" % weapon_inventory.size())

func _load_weapon_configs():
	"""Load weapon configurations from JSON files"""
	var config_dir = "res://shared/data/weapons/"
	var dir = DirAccess.open(config_dir)
	
	if dir == null:
		push_error("Failed to open weapon config directory: " + config_dir)
		return
	
	dir.list_dir_begin()
	var file_name = dir.get_next()
	
	while file_name != "":
		if file_name.ends_with(".json"):
			var weapon_id = file_name.get_basename()
			var config_path = config_dir + file_name
			
			var file = FileAccess.open(config_path, FileAccess.READ)
			if file:
				var json_string = file.get_as_text()
				file.close()
				
				var json = JSON.new()
				var parse_result = json.parse(json_string)
				
				if parse_result == OK:
					weapon_configs[weapon_id] = json.data
					print("📄 Loaded weapon config: %s" % weapon_id)
				else:
					push_error("Failed to parse weapon config: " + config_path)
		
		file_name = dir.get_next()

func _load_weapon_scenes():
	"""Load weapon scene references"""
	weapon_scenes = {
		"pistol": preload("res://client/scenes/weapons/pistol.tscn"),
		"assault_rifle": preload("res://client/scenes/weapons/assault_rifle.tscn"),
		"sniper_rifle": preload("res://client/scenes/weapons/sniper_rifle.tscn"),
		"shotgun": preload("res://client/scenes/weapons/shotgun.tscn"),
		"smg": preload("res://client/scenes/weapons/smg.tscn")
	}

func _load_default_weapons():
	"""Load default weapon loadout"""
	for weapon_name in default_weapons:
		var weapon_resource = create_weapon_resource(weapon_name)
		if weapon_resource:
			weapon_inventory.append(weapon_resource)
	
	# Equip first weapon
	if weapon_inventory.size() > 0:
		equip_weapon_by_index(0)

func create_weapon_resource(weapon_name: String) -> WeaponResource:
	"""Create a weapon resource from configuration"""
	if not weapon_configs.has(weapon_name):
		push_error("Weapon config not found: " + weapon_name)
		return null
	
	var config = weapon_configs[weapon_name]
	var resource = WeaponResource.new()
	
	resource.weapon_name = weapon_name
	resource.display_name = config.get("display_name", weapon_name.capitalize())
	resource.weapon_scene = weapon_scenes.get(weapon_name)
	resource.weapon_config = config
	
	return resource

## Public API

func equip_weapon(weapon_resource: WeaponResource):
	"""Equip a weapon from resource"""
	if not weapon_resource or not weapon_resource.weapon_scene:
		push_error("Invalid weapon resource")
		return
	
	# Remove current weapon
	if current_weapon:
		current_weapon.queue_free()
		current_weapon = null
	
	# Instantiate new weapon
	var weapon_scene = weapon_resource.weapon_scene.instantiate()
	weapon_holder.add_child(weapon_scene)
	
	current_weapon = weapon_scene as WeaponBase
	if current_weapon:
		# Load weapon configuration
		current_weapon.load_weapon_config(weapon_resource.weapon_config)
		
		# Connect signals
		current_weapon.weapon_fired.connect(_on_weapon_fired)
		current_weapon.weapon_reloaded.connect(_on_weapon_reloaded)
		current_weapon.ammo_changed.connect(_on_ammo_changed)
		current_weapon.weapon_empty.connect(_on_weapon_empty)
		
		# Emit signals
		weapon_changed.emit(current_weapon)
		weapon_equipped.emit(weapon_resource.weapon_name)
		
		print("🔫 Equipped weapon: %s" % weapon_resource.display_name)

func equip_weapon_by_index(index: int):
	"""Equip weapon by inventory index"""
	if index < 0 or index >= weapon_inventory.size():
		return
	
	current_weapon_index = index
	equip_weapon(weapon_inventory[index])

func equip_weapon_by_name(weapon_name: String):
	"""Equip weapon by name"""
	for i in range(weapon_inventory.size()):
		if weapon_inventory[i].weapon_name == weapon_name:
			equip_weapon_by_index(i)
			return
	
	push_warning("Weapon not found in inventory: " + weapon_name)

func switch_to_next_weapon():
	"""Switch to next weapon in inventory"""
	if weapon_inventory.size() <= 1:
		return
	
	var next_index = (current_weapon_index + 1) % weapon_inventory.size()
	equip_weapon_by_index(next_index)

func switch_to_previous_weapon():
	"""Switch to previous weapon in inventory"""
	if weapon_inventory.size() <= 1:
		return
	
	var prev_index = (current_weapon_index - 1 + weapon_inventory.size()) % weapon_inventory.size()
	equip_weapon_by_index(prev_index)

func add_weapon_to_inventory(weapon_name: String) -> bool:
	"""Add weapon to inventory"""
	# Check if weapon already exists
	for weapon in weapon_inventory:
		if weapon.weapon_name == weapon_name:
			return false
	
	var weapon_resource = create_weapon_resource(weapon_name)
	if weapon_resource:
		weapon_inventory.append(weapon_resource)
		inventory_changed.emit()
		print("➕ Added weapon to inventory: %s" % weapon_name)
		return true
	
	return false

func remove_weapon_from_inventory(weapon_name: String) -> bool:
	"""Remove weapon from inventory"""
	for i in range(weapon_inventory.size()):
		if weapon_inventory[i].weapon_name == weapon_name:
			weapon_inventory.remove_at(i)
			
			# Adjust current index if necessary
			if current_weapon_index >= i:
				current_weapon_index = max(0, current_weapon_index - 1)
			
			# Switch weapon if current was removed
			if weapon_inventory.size() > 0 and current_weapon and current_weapon.weapon_name == weapon_name:
				equip_weapon_by_index(current_weapon_index)
			
			inventory_changed.emit()
			weapon_dropped.emit(weapon_name)
			print("➖ Removed weapon from inventory: %s" % weapon_name)
			return true
	
	return false

func get_current_weapon() -> WeaponBase:
	"""Get currently equipped weapon"""
	return current_weapon

func get_weapon_inventory() -> Array[WeaponResource]:
	"""Get weapon inventory"""
	return weapon_inventory.duplicate()

func has_weapon(weapon_name: String) -> bool:
	"""Check if weapon is in inventory"""
	for weapon in weapon_inventory:
		if weapon.weapon_name == weapon_name:
			return true
	return false

func reload_current_weapon():
	"""Reload currently equipped weapon"""
	if current_weapon:
		current_weapon.reload()

func start_firing():
	"""Start firing current weapon"""
	if current_weapon:
		current_weapon.start_firing()

func stop_firing():
	"""Stop firing current weapon"""
	if current_weapon:
		current_weapon.stop_firing()

## Ammo management

func add_ammo(weapon_name: String, amount: int):
	"""Add ammo for specific weapon"""
	if current_weapon and current_weapon.weapon_name == weapon_name:
		current_weapon.reserve_ammo = min(
			current_weapon.max_reserve_ammo,
			current_weapon.reserve_ammo + amount
		)
		ammo_changed.emit(current_weapon.current_ammo, current_weapon.reserve_ammo)

func get_ammo_count(weapon_name: String) -> Dictionary:
	"""Get ammo count for weapon"""
	if current_weapon and current_weapon.weapon_name == weapon_name:
		return {
			"current": current_weapon.current_ammo,
			"reserve": current_weapon.reserve_ammo,
			"max_reserve": current_weapon.max_reserve_ammo
		}
	return {"current": 0, "reserve": 0, "max_reserve": 0}

## Configuration hot-reloading

func reload_weapon_config(weapon_name: String):
	"""Reload weapon configuration from file"""
	var config_path = "res://shared/data/weapons/" + weapon_name + ".json"
	var file = FileAccess.open(config_path, FileAccess.READ)
	
	if file:
		var json_string = file.get_as_text()
		file.close()
		
		var json = JSON.new()
		var parse_result = json.parse(json_string)
		
		if parse_result == OK:
			weapon_configs[weapon_name] = json.data
			
			# Update current weapon if it matches
			if current_weapon and current_weapon.weapon_name == weapon_name:
				current_weapon.load_weapon_config(json.data)
			
			# Update inventory resource
			for weapon in weapon_inventory:
				if weapon.weapon_name == weapon_name:
					weapon.weapon_config = json.data
					break
			
			print("🔄 Reloaded weapon config: %s" % weapon_name)
		else:
			push_error("Failed to parse weapon config: " + config_path)
	else:
		push_error("Failed to open weapon config: " + config_path)

func reload_all_weapon_configs():
	"""Reload all weapon configurations"""
	for weapon_name in weapon_configs.keys():
		reload_weapon_config(weapon_name)

## Input handling

func _input(event):
	"""Handle weapon switching input"""
	if event.is_action_pressed("weapon_next"):
		switch_to_next_weapon()
	elif event.is_action_pressed("weapon_previous"):
		switch_to_previous_weapon()
	elif event.is_action_pressed("reload"):
		reload_current_weapon()
	
	# Number key weapon switching
	for i in range(min(weapon_inventory.size(), 9)):
		if event.is_action_pressed("weapon_" + str(i + 1)):
			equip_weapon_by_index(i)

## Signal handlers

func _on_weapon_fired(weapon: WeaponBase):
	"""Handle weapon fired event"""
	# Could trigger UI updates, audio, etc.
	pass

func _on_weapon_reloaded(weapon: WeaponBase):
	"""Handle weapon reloaded event"""
	# Could trigger UI updates, audio, etc.
	pass

func _on_ammo_changed(current: int, reserve: int):
	"""Handle ammo changed event"""
	ammo_changed.emit(current, reserve)

func _on_weapon_empty():
	"""Handle weapon empty event"""
	# Could trigger auto-reload or UI indication
	if current_weapon and current_weapon.reserve_ammo > 0:
		current_weapon.reload()

## Debug and utility

func get_weapon_stats() -> Dictionary:
	"""Get current weapon statistics"""
	if current_weapon:
		return current_weapon.get_weapon_stats()
	return {}

func print_inventory():
	"""Print current weapon inventory"""
	print("🎒 Weapon Inventory:")
	for i in range(weapon_inventory.size()):
		var weapon = weapon_inventory[i]
		var current_marker = " [CURRENT]" if i == current_weapon_index else ""
		print("  %d. %s%s" % [i + 1, weapon.display_name, current_marker])

## Weapon Resource Class

class WeaponResource:
	var weapon_name: String
	var display_name: String
	var weapon_scene: PackedScene
	var weapon_config: Dictionary
