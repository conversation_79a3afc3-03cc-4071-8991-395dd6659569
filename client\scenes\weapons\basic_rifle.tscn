[gd_scene load_steps=3 format=3 uid="uid://basic_rifle_scene"]

[ext_resource type="Script" path="res://scripts/weapons/weapon_base.gd" id="1_weapon_base"]

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(0.1, 0.1, 0.8)

[node name="BasicRifle" type="Node3D"]
script = ExtResource("1_weapon_base")
weapon_name = "Basic Rifle"
damage = 25.0
fire_rate = 600.0
magazine_size = 30
current_ammo = 30
reserve_ammo = 120

[node name="WeaponMesh" type="MeshInstance3D" parent="."]
mesh = SubResource("BoxMesh_1")

[node name="MuzzlePoint" type="Marker3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, -0.4)

[node name="ShellEjectPoint" type="Marker3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.1, 0, -0.2)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]

[node name="AudioPlayer" type="AudioStreamPlayer3D" parent="."]
