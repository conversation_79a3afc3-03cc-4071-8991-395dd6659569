#!/usr/bin/env python3
"""
Galaxy Guns 3D - App Store Submission Preparation
Automated preparation for iOS App Store submission
Handles metadata, screenshots, compliance, and validation
"""

import os
import sys
import json
import shutil
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime
import requests
from PIL import Image, ImageDraw, ImageFont

class AppStorePreparation:
    """Handles App Store submission preparation"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.app_store_dir = self.project_root / "app_store"
        self.metadata_dir = self.app_store_dir / "metadata"
        self.screenshots_dir = self.app_store_dir / "screenshots"
        self.assets_dir = self.app_store_dir / "assets"
        
        # App Store configuration
        self.app_config = self._load_app_config()
        
        # Create directories
        self._create_directories()
        
        print("📱 App Store Preparation initialized")
    
    def _load_app_config(self) -> Dict:
        """Load app configuration for App Store"""
        config_file = self.project_root / "app_store_config.json"
        
        default_config = {
            "app_name": "Galaxy Guns 3D",
            "bundle_id": "com.galaxyguns3d.mobile",
            "version": "1.0.0",
            "build_number": "1",
            "category": "Games",
            "subcategory": "Action",
            "age_rating": "12+",
            "price_tier": "Free",
            "in_app_purchases": True,
            "game_center": True,
            "privacy_policy_url": "https://galaxyguns3d.com/privacy",
            "support_url": "https://galaxyguns3d.com/support",
            "marketing_url": "https://galaxyguns3d.com",
            "keywords": [
                "fps", "shooter", "multiplayer", "action", "3d",
                "competitive", "mobile", "gaming", "battle", "guns"
            ],
            "description": {
                "short": "Fast-paced 3D multiplayer FPS optimized for mobile",
                "full": """Galaxy Guns 3D delivers console-quality FPS action to your mobile device.

🎮 FEATURES:
• Intense 8-player multiplayer battles
• Touch-optimized controls with gyroscope aiming
• Multiple game modes: Team Deathmatch, Domination, Capture the Flag
• Skill-based matchmaking for balanced competition
• Stunning 3D graphics optimized for iOS
• 60fps gameplay on supported devices
• Voice chat integration
• Regular content updates

⚡ OPTIMIZED FOR MOBILE:
• Battery-efficient rendering
• Adaptive quality for smooth performance
• Cellular network optimization
• Quick match times under 60 seconds

🏆 COMPETITIVE GAMEPLAY:
• Ranked matches with skill ratings
• Leaderboards and achievements
• Anti-cheat protection
• Fair play enforcement

Download now and dominate the battlefield!"""
            },
            "what_is_new": "Initial release with multiplayer FPS action!",
            "supported_devices": [
                "iPhone 12 mini and newer",
                "iPad Air (4th generation) and newer",
                "iPad Pro (3rd generation) and newer"
            ],
            "required_ios_version": "15.0",
            "languages": ["English"],
            "content_warnings": [
                "Cartoon Violence",
                "Mild Language",
                "Online Interactions"
            ]
        }
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                config = json.load(f)
                # Merge with defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        else:
            # Create default config
            with open(config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            return default_config
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self.app_store_dir,
            self.metadata_dir,
            self.screenshots_dir,
            self.assets_dir,
            self.screenshots_dir / "iphone_6_7",
            self.screenshots_dir / "iphone_5_5",
            self.screenshots_dir / "ipad_pro_3rd_gen",
            self.screenshots_dir / "ipad_pro_2nd_gen"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def prepare_app_store_submission(self):
        """Complete App Store submission preparation"""
        print("🚀 Starting App Store submission preparation...")
        
        # Generate app icons
        self._generate_app_icons()
        
        # Generate screenshots
        self._generate_screenshots()
        
        # Prepare metadata
        self._prepare_metadata()
        
        # Generate privacy manifest
        self._generate_privacy_manifest()
        
        # Validate submission
        self._validate_submission()
        
        # Generate submission checklist
        self._generate_submission_checklist()
        
        print("✅ App Store submission preparation complete!")
    
    def _generate_app_icons(self):
        """Generate all required app icon sizes"""
        print("🎨 Generating app icons...")
        
        # App icon sizes for iOS
        icon_sizes = {
            "app_store": 1024,
            "iphone_60pt_3x": 180,
            "iphone_60pt_2x": 120,
            "ipad_76pt_2x": 152,
            "ipad_76pt_1x": 76,
            "ipad_pro_83_5pt_2x": 167,
            "spotlight_40pt_3x": 120,
            "spotlight_40pt_2x": 80,
            "settings_29pt_3x": 87,
            "settings_29pt_2x": 58,
            "notification_20pt_3x": 60,
            "notification_20pt_2x": 40
        }
        
        # Source icon (should be 1024x1024)
        source_icon = self.project_root / "assets" / "icons" / "app_icon_1024.png"
        
        if not source_icon.exists():
            self._create_placeholder_icon(source_icon)
        
        # Generate all sizes
        for name, size in icon_sizes.items():
            output_path = self.assets_dir / f"app_icon_{name}_{size}x{size}.png"
            self._resize_icon(source_icon, output_path, size)
        
        print(f"📱 Generated {len(icon_sizes)} app icon variants")
    
    def _create_placeholder_icon(self, output_path: Path):
        """Create placeholder app icon"""
        size = 1024
        img = Image.new('RGB', (size, size), color='#1a1a2e')
        draw = ImageDraw.Draw(img)
        
        # Draw simple game-themed icon
        # Background gradient effect
        for i in range(size):
            color_value = int(26 + (i / size) * 50)  # Gradient from dark to lighter
            draw.line([(0, i), (size, i)], fill=(color_value, color_value, 46))
        
        # Draw crosshair
        center = size // 2
        crosshair_size = size // 4
        line_width = size // 64
        
        # Horizontal line
        draw.rectangle([
            center - crosshair_size, center - line_width,
            center + crosshair_size, center + line_width
        ], fill='#ff6b6b')
        
        # Vertical line
        draw.rectangle([
            center - line_width, center - crosshair_size,
            center + line_width, center + crosshair_size
        ], fill='#ff6b6b')
        
        # Add text
        try:
            font = ImageFont.truetype("Arial", size // 16)
        except:
            font = ImageFont.load_default()
        
        text = "GALAXY\nGUNS 3D"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        draw.text(
            (center - text_width // 2, center + crosshair_size + 20),
            text,
            fill='white',
            font=font,
            align='center'
        )
        
        output_path.parent.mkdir(parents=True, exist_ok=True)
        img.save(output_path, 'PNG')
        print(f"📱 Created placeholder app icon: {output_path}")
    
    def _resize_icon(self, source_path: Path, output_path: Path, size: int):
        """Resize icon to specific size"""
        with Image.open(source_path) as img:
            # Resize with high-quality resampling
            resized = img.resize((size, size), Image.Resampling.LANCZOS)
            resized.save(output_path, 'PNG', optimize=True)
    
    def _generate_screenshots(self):
        """Generate App Store screenshots"""
        print("📸 Generating App Store screenshots...")
        
        # Screenshot specifications
        screenshot_specs = {
            "iphone_6_7": {
                "size": (1290, 2796),
                "count": 10,
                "description": "iPhone 6.7-inch display"
            },
            "iphone_5_5": {
                "size": (1242, 2208),
                "count": 10,
                "description": "iPhone 5.5-inch display"
            },
            "ipad_pro_3rd_gen": {
                "size": (2048, 2732),
                "count": 10,
                "description": "iPad Pro (3rd generation)"
            },
            "ipad_pro_2nd_gen": {
                "size": (2048, 2732),
                "count": 10,
                "description": "iPad Pro (2nd generation)"
            }
        }
        
        # Screenshot content descriptions
        screenshot_content = [
            "Main menu with game modes",
            "Multiplayer lobby and matchmaking",
            "Intense FPS gameplay action",
            "Weapon selection and customization",
            "Team Deathmatch battle",
            "Domination mode capture points",
            "Capture the Flag gameplay",
            "Player statistics and progression",
            "Settings and controls customization",
            "Leaderboards and achievements"
        ]
        
        for device, spec in screenshot_specs.items():
            device_dir = self.screenshots_dir / device
            
            for i in range(spec["count"]):
                screenshot_path = device_dir / f"screenshot_{i+1:02d}.png"
                content_desc = screenshot_content[i] if i < len(screenshot_content) else f"Gameplay screenshot {i+1}"
                
                self._create_screenshot_mockup(
                    screenshot_path,
                    spec["size"],
                    content_desc,
                    device
                )
        
        print(f"📱 Generated screenshots for {len(screenshot_specs)} device types")
    
    def _create_screenshot_mockup(self, output_path: Path, size: tuple, content_desc: str, device_type: str):
        """Create screenshot mockup"""
        width, height = size
        img = Image.new('RGB', (width, height), color='#0a0a0a')
        draw = ImageDraw.Draw(img)
        
        # Create game-like UI mockup
        # Top UI bar
        ui_height = height // 20
        draw.rectangle([0, 0, width, ui_height], fill='#1a1a2e')
        
        # Bottom UI bar
        draw.rectangle([0, height - ui_height, width, height], fill='#1a1a2e')
        
        # Crosshair in center
        center_x, center_y = width // 2, height // 2
        crosshair_size = min(width, height) // 20
        
        draw.line([
            center_x - crosshair_size, center_y,
            center_x + crosshair_size, center_y
        ], fill='#ff6b6b', width=4)
        
        draw.line([
            center_x, center_y - crosshair_size,
            center_x, center_y + crosshair_size
        ], fill='#ff6b6b', width=4)
        
        # Add content description
        try:
            font_size = max(24, min(width, height) // 40)
            font = ImageFont.truetype("Arial", font_size)
        except:
            font = ImageFont.load_default()
        
        # Title
        title = "GALAXY GUNS 3D"
        bbox = draw.textbbox((0, 0), title, font=font)
        title_width = bbox[2] - bbox[0]
        
        draw.text(
            (width // 2 - title_width // 2, ui_height // 4),
            title,
            fill='white',
            font=font
        )
        
        # Content description
        desc_font_size = max(18, font_size // 2)
        try:
            desc_font = ImageFont.truetype("Arial", desc_font_size)
        except:
            desc_font = ImageFont.load_default()
        
        bbox = draw.textbbox((0, 0), content_desc, font=desc_font)
        desc_width = bbox[2] - bbox[0]
        
        draw.text(
            (width // 2 - desc_width // 2, height - ui_height + ui_height // 4),
            content_desc,
            fill='#cccccc',
            font=desc_font
        )
        
        # Add some visual elements to make it look more game-like
        self._add_game_ui_elements(draw, width, height)
        
        img.save(output_path, 'PNG', optimize=True)
    
    def _add_game_ui_elements(self, draw: ImageDraw.Draw, width: int, height: int):
        """Add game UI elements to screenshot"""
        # Health bar (top left)
        health_width = width // 6
        health_height = height // 40
        health_x = width // 20
        health_y = height // 15
        
        # Health background
        draw.rectangle([
            health_x, health_y,
            health_x + health_width, health_y + health_height
        ], fill='#333333')
        
        # Health fill (80% health)
        draw.rectangle([
            health_x, health_y,
            health_x + int(health_width * 0.8), health_y + health_height
        ], fill='#4CAF50')
        
        # Ammo counter (bottom right)
        ammo_text = "30 / 120"
        try:
            ammo_font = ImageFont.truetype("Arial", height // 30)
        except:
            ammo_font = ImageFont.load_default()
        
        bbox = draw.textbbox((0, 0), ammo_text, font=ammo_font)
        ammo_width = bbox[2] - bbox[0]
        
        draw.text(
            (width - ammo_width - width // 20, height - height // 10),
            ammo_text,
            fill='white',
            font=ammo_font
        )
        
        # Mini-map (top right)
        minimap_size = min(width, height) // 8
        minimap_x = width - minimap_size - width // 20
        minimap_y = height // 15
        
        draw.rectangle([
            minimap_x, minimap_y,
            minimap_x + minimap_size, minimap_y + minimap_size
        ], fill='#1a1a2e', outline='#666666', width=2)
        
        # Player dot on minimap
        dot_size = minimap_size // 20
        center_x = minimap_x + minimap_size // 2
        center_y = minimap_y + minimap_size // 2
        
        draw.ellipse([
            center_x - dot_size, center_y - dot_size,
            center_x + dot_size, center_y + dot_size
        ], fill='#00ff00')
    
    def _prepare_metadata(self):
        """Prepare App Store metadata"""
        print("📝 Preparing App Store metadata...")
        
        metadata = {
            "app_name": self.app_config["app_name"],
            "subtitle": "Mobile FPS Action",
            "description": self.app_config["description"]["full"],
            "keywords": ", ".join(self.app_config["keywords"]),
            "support_url": self.app_config["support_url"],
            "marketing_url": self.app_config["marketing_url"],
            "privacy_policy_url": self.app_config["privacy_policy_url"],
            "category": self.app_config["category"],
            "age_rating": self.app_config["age_rating"],
            "version": self.app_config["version"],
            "what_is_new": self.app_config["what_is_new"],
            "review_notes": """
Thank you for reviewing Galaxy Guns 3D!

TEST ACCOUNT:
Username: <EMAIL>
Password: ReviewTest2024!

TESTING NOTES:
- The game requires internet connection for multiplayer
- Best tested on iPhone 12 mini or newer for optimal performance
- All multiplayer features are fully functional
- In-app purchases are implemented but use sandbox environment

FEATURES TO TEST:
1. Touch controls and gyroscope aiming
2. Multiplayer matchmaking (usually <60 seconds)
3. Voice chat functionality
4. Battery optimization features
5. Performance on different device orientations

<NAME_EMAIL> for any questions.
            """.strip()
        }
        
        # Save metadata
        metadata_file = self.metadata_dir / "app_store_metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        print(f"📱 Metadata saved to: {metadata_file}")
    
    def _generate_privacy_manifest(self):
        """Generate privacy manifest for iOS"""
        print("🔒 Generating privacy manifest...")
        
        privacy_manifest = {
            "NSPrivacyCollectedDataTypes": [
                {
                    "NSPrivacyCollectedDataType": "NSPrivacyCollectedDataTypeUserID",
                    "NSPrivacyCollectedDataTypeLinked": True,
                    "NSPrivacyCollectedDataTypeTracking": False,
                    "NSPrivacyCollectedDataTypePurposes": [
                        "NSPrivacyCollectedDataTypePurposeGameplay"
                    ]
                },
                {
                    "NSPrivacyCollectedDataType": "NSPrivacyCollectedDataTypeGameplayContent",
                    "NSPrivacyCollectedDataTypeLinked": True,
                    "NSPrivacyCollectedDataTypeTracking": False,
                    "NSPrivacyCollectedDataTypePurposes": [
                        "NSPrivacyCollectedDataTypePurposeGameplay",
                        "NSPrivacyCollectedDataTypePurposeAnalytics"
                    ]
                },
                {
                    "NSPrivacyCollectedDataType": "NSPrivacyCollectedDataTypePerformanceData",
                    "NSPrivacyCollectedDataTypeLinked": False,
                    "NSPrivacyCollectedDataTypeTracking": False,
                    "NSPrivacyCollectedDataTypePurposes": [
                        "NSPrivacyCollectedDataTypePurposeAnalytics",
                        "NSPrivacyCollectedDataTypePurposeAppFunctionality"
                    ]
                }
            ],
            "NSPrivacyAccessedAPITypes": [
                {
                    "NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategoryUserDefaults",
                    "NSPrivacyAccessedAPITypeReasons": ["CA92.1"]
                },
                {
                    "NSPrivacyAccessedAPIType": "NSPrivacyAccessedAPICategorySystemBootTime",
                    "NSPrivacyAccessedAPITypeReasons": ["35F9.1"]
                }
            ]
        }
        
        manifest_file = self.assets_dir / "PrivacyInfo.xcprivacy"
        
        # Convert to plist format (simplified)
        plist_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>NSPrivacyCollectedDataTypes</key>
    <array>
        <!-- User ID for gameplay -->
        <dict>
            <key>NSPrivacyCollectedDataType</key>
            <string>NSPrivacyCollectedDataTypeUserID</string>
            <key>NSPrivacyCollectedDataTypeLinked</key>
            <true/>
            <key>NSPrivacyCollectedDataTypeTracking</key>
            <false/>
            <key>NSPrivacyCollectedDataTypePurposes</key>
            <array>
                <string>NSPrivacyCollectedDataTypePurposeGameplay</string>
            </array>
        </dict>
        <!-- Gameplay content -->
        <dict>
            <key>NSPrivacyCollectedDataType</key>
            <string>NSPrivacyCollectedDataTypeGameplayContent</string>
            <key>NSPrivacyCollectedDataTypeLinked</key>
            <true/>
            <key>NSPrivacyCollectedDataTypeTracking</key>
            <false/>
            <key>NSPrivacyCollectedDataTypePurposes</key>
            <array>
                <string>NSPrivacyCollectedDataTypePurposeGameplay</string>
                <string>NSPrivacyCollectedDataTypePurposeAnalytics</string>
            </array>
        </dict>
    </array>
    <key>NSPrivacyAccessedAPITypes</key>
    <array>
        <dict>
            <key>NSPrivacyAccessedAPIType</key>
            <string>NSPrivacyAccessedAPICategoryUserDefaults</string>
            <key>NSPrivacyAccessedAPITypeReasons</key>
            <array>
                <string>CA92.1</string>
            </array>
        </dict>
    </array>
</dict>
</plist>"""
        
        with open(manifest_file, 'w') as f:
            f.write(plist_content)
        
        print(f"🔒 Privacy manifest saved to: {manifest_file}")
    
    def _validate_submission(self):
        """Validate App Store submission requirements"""
        print("✅ Validating submission requirements...")
        
        validation_results = {
            "app_icons": self._validate_app_icons(),
            "screenshots": self._validate_screenshots(),
            "metadata": self._validate_metadata(),
            "privacy_manifest": self._validate_privacy_manifest(),
            "content_rating": self._validate_content_rating()
        }
        
        # Save validation results
        validation_file = self.app_store_dir / "validation_results.json"
        with open(validation_file, 'w') as f:
            json.dump(validation_results, f, indent=2)
        
        # Print summary
        all_valid = all(validation_results.values())
        status = "✅ PASSED" if all_valid else "❌ FAILED"
        print(f"📱 Validation {status}")
        
        for category, result in validation_results.items():
            status_icon = "✅" if result else "❌"
            print(f"  {status_icon} {category.replace('_', ' ').title()}")
    
    def _validate_app_icons(self) -> bool:
        """Validate app icons"""
        required_icons = [
            "app_icon_app_store_1024x1024.png",
            "app_icon_iphone_60pt_3x_180x180.png",
            "app_icon_iphone_60pt_2x_120x120.png"
        ]
        
        for icon in required_icons:
            icon_path = self.assets_dir / icon
            if not icon_path.exists():
                return False
        
        return True
    
    def _validate_screenshots(self) -> bool:
        """Validate screenshots"""
        required_devices = ["iphone_6_7", "iphone_5_5"]
        
        for device in required_devices:
            device_dir = self.screenshots_dir / device
            if not device_dir.exists():
                return False
            
            screenshots = list(device_dir.glob("*.png"))
            if len(screenshots) < 3:  # Minimum 3 screenshots required
                return False
        
        return True
    
    def _validate_metadata(self) -> bool:
        """Validate metadata"""
        metadata_file = self.metadata_dir / "app_store_metadata.json"
        return metadata_file.exists()
    
    def _validate_privacy_manifest(self) -> bool:
        """Validate privacy manifest"""
        manifest_file = self.assets_dir / "PrivacyInfo.xcprivacy"
        return manifest_file.exists()
    
    def _validate_content_rating(self) -> bool:
        """Validate content rating"""
        # Check if content warnings are appropriate
        warnings = self.app_config.get("content_warnings", [])
        required_warnings = ["Cartoon Violence", "Online Interactions"]
        
        return all(warning in warnings for warning in required_warnings)
    
    def _generate_submission_checklist(self):
        """Generate submission checklist"""
        print("📋 Generating submission checklist...")
        
        checklist = """# Galaxy Guns 3D - App Store Submission Checklist

## Pre-Submission Requirements
- [ ] App icons generated (all sizes)
- [ ] Screenshots created (iPhone 6.7", 5.5", iPad Pro)
- [ ] App Store metadata prepared
- [ ] Privacy manifest included
- [ ] Content rating validated
- [ ] Test account credentials prepared

## Technical Requirements
- [ ] App built with release configuration
- [ ] Code signing certificates valid
- [ ] Provisioning profiles updated
- [ ] App size under 4GB limit
- [ ] 64-bit architecture support
- [ ] iOS 15.0+ compatibility verified

## Content Requirements
- [ ] App description under 4000 characters
- [ ] Keywords under 100 characters
- [ ] Screenshots show actual gameplay
- [ ] Age rating matches content
- [ ] Privacy policy accessible
- [ ] Support URL functional

## Testing Requirements
- [ ] Tested on iPhone 12 mini (minimum device)
- [ ] Tested on iPad Pro (tablet support)
- [ ] Multiplayer functionality verified
- [ ] In-app purchases tested (sandbox)
- [ ] Voice chat permissions working
- [ ] Battery optimization validated
- [ ] Network connectivity edge cases tested

## Compliance Requirements
- [ ] Privacy manifest includes all data collection
- [ ] COPPA compliance (if applicable)
- [ ] GDPR compliance for EU users
- [ ] Accessibility features implemented
- [ ] Content warnings accurate
- [ ] Anti-cheat system documented

## Submission Process
- [ ] Upload build to App Store Connect
- [ ] Fill in app information
- [ ] Upload screenshots and metadata
- [ ] Set pricing and availability
- [ ] Submit for review
- [ ] Monitor review status

## Post-Submission
- [ ] Prepare for potential rejection feedback
- [ ] Plan marketing campaign
- [ ] Monitor crash reports
- [ ] Prepare first update

## Contact Information
- Developer: Galaxy Guns 3D Team
- Support: <EMAIL>
- Privacy: <EMAIL>
- Marketing: <EMAIL>

## Review Notes for Apple
Thank you for reviewing Galaxy Guns 3D! This is a premium mobile FPS game optimized for iOS devices. Please use the provided test account for full multiplayer testing. The game requires internet connection and performs best on iPhone 12 mini or newer devices.
"""
        
        checklist_file = self.app_store_dir / "submission_checklist.md"
        with open(checklist_file, 'w') as f:
            f.write(checklist)
        
        print(f"📋 Submission checklist saved to: {checklist_file}")

def main():
    """Main entry point"""
    if len(sys.argv) != 2:
        print("Usage: python app_store_prep.py <project_root>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    
    if not os.path.exists(project_root):
        print(f"Error: Project root '{project_root}' does not exist")
        sys.exit(1)
    
    # Initialize App Store preparation
    app_store_prep = AppStorePreparation(project_root)
    
    # Run complete preparation
    app_store_prep.prepare_app_store_submission()
    
    print("\n🎉 App Store submission preparation complete!")
    print("📁 Check the 'app_store' directory for all generated assets")
    print("📋 Review the submission checklist before uploading to App Store Connect")

if __name__ == "__main__":
    main()
