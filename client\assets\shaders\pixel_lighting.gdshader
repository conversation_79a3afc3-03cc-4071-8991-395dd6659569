/*
Galaxy Guns 3D - Dynamic Pixel Lighting Shader
Advanced per-pixel lighting for pixel art sprites with normal mapping
Optimized for 60fps performance on iPhone 12 mini and above
*/

shader_type canvas_item;

// Texture inputs
uniform sampler2D albedo_texture : source_color, filter_nearest, repeat_disable;
uniform sampler2D normal_texture : hint_normal, filter_nearest, repeat_disable;
uniform sampler2D emission_texture : source_color, filter_nearest, repeat_disable;

// Lighting parameters
uniform vec3 ambient_color : source_color = vec3(0.2, 0.2, 0.3);
uniform float ambient_strength : hint_range(0.0, 1.0) = 0.3;

// Dynamic light sources (up to 8 lights for performance)
uniform int light_count : hint_range(0, 8) = 4;
uniform vec2 light_positions[8];
uniform vec3 light_colors[8] : source_color;
uniform float light_intensities[8] : hint_range(0.0, 10.0);
uniform float light_ranges[8] : hint_range(0.0, 1000.0);

// Material properties
uniform float metallic : hint_range(0.0, 1.0) = 0.0;
uniform float roughness : hint_range(0.0, 1.0) = 0.8;
uniform float specular_strength : hint_range(0.0, 2.0) = 0.5;
uniform float emission_strength : hint_range(0.0, 5.0) = 1.0;

// Pixel art specific settings
uniform bool pixel_snap : hint_default_white = true;
uniform float pixel_size : hint_range(1.0, 8.0) = 1.0;
uniform bool dithering_enabled = false;
uniform float dither_strength : hint_range(0.0, 1.0) = 0.1;

// Performance optimization
uniform bool high_quality_normals = true;
uniform bool enable_shadows = true;
uniform float shadow_softness : hint_range(0.0, 1.0) = 0.3;

// Animation support
uniform float time_offset : hint_range(0.0, 1.0) = 0.0;
uniform vec2 uv_offset = vec2(0.0, 0.0);
uniform vec2 uv_scale = vec2(1.0, 1.0);

varying vec2 world_position;
varying vec3 world_normal;

// Utility functions
vec2 snap_to_pixel(vec2 uv, float pixel_size) {
    if (pixel_snap) {
        return floor(uv * pixel_size) / pixel_size;
    }
    return uv;
}

vec3 unpack_normal(vec4 normal_sample) {
    if (high_quality_normals) {
        // High quality normal unpacking
        vec3 normal = normal_sample.rgb * 2.0 - 1.0;
        normal.z = sqrt(1.0 - dot(normal.xy, normal.xy));
        return normalize(normal);
    } else {
        // Fast normal unpacking for performance
        return normalize(normal_sample.rgb * 2.0 - 1.0);
    }
}

float calculate_attenuation(float distance, float range) {
    // Smooth attenuation function optimized for pixel art
    float normalized_distance = distance / range;
    return 1.0 / (1.0 + normalized_distance * normalized_distance);
}

vec3 calculate_diffuse_lighting(vec3 normal, vec3 light_dir, vec3 light_color, float attenuation) {
    float ndotl = max(dot(normal, light_dir), 0.0);
    return light_color * ndotl * attenuation;
}

vec3 calculate_specular_lighting(vec3 normal, vec3 light_dir, vec3 view_dir, vec3 light_color, float attenuation) {
    vec3 reflect_dir = reflect(-light_dir, normal);
    float spec = pow(max(dot(view_dir, reflect_dir), 0.0), (1.0 - roughness) * 128.0);
    return light_color * spec * specular_strength * attenuation;
}

float calculate_shadow(vec2 pixel_pos, vec2 light_pos, float light_range) {
    if (!enable_shadows) return 1.0;
    
    // Simple shadow calculation for pixel art
    vec2 to_light = light_pos - pixel_pos;
    float distance_to_light = length(to_light);
    
    if (distance_to_light > light_range) return 0.0;
    
    // Soft shadow approximation
    float shadow = 1.0 - (distance_to_light / light_range);
    return mix(shadow_softness, 1.0, shadow);
}

vec3 apply_dithering(vec3 color, vec2 screen_pos) {
    if (!dithering_enabled) return color;
    
    // Ordered dithering pattern for pixel art
    mat4 dither_matrix = mat4(
        vec4(0.0, 8.0, 2.0, 10.0),
        vec4(12.0, 4.0, 14.0, 6.0),
        vec4(3.0, 11.0, 1.0, 9.0),
        vec4(15.0, 7.0, 13.0, 5.0)
    );
    
    int x = int(mod(screen_pos.x, 4.0));
    int y = int(mod(screen_pos.y, 4.0));
    float dither_value = dither_matrix[x][y] / 16.0;
    
    return color + (dither_value - 0.5) * dither_strength;
}

void vertex() {
    // Apply UV transformations for animation support
    UV = (UV + uv_offset) * uv_scale;
    
    // Snap to pixel grid if enabled
    UV = snap_to_pixel(UV, pixel_size);
    
    // Calculate world position for lighting
    world_position = (MODEL_MATRIX * vec4(VERTEX, 0.0, 1.0)).xy;
}

void fragment() {
    // Sample textures with pixel-perfect filtering
    vec2 snapped_uv = snap_to_pixel(UV, pixel_size);
    
    vec4 albedo_sample = texture(albedo_texture, snapped_uv);
    vec4 normal_sample = texture(normal_texture, snapped_uv);
    vec4 emission_sample = texture(emission_texture, snapped_uv);
    
    // Early exit for transparent pixels
    if (albedo_sample.a < 0.01) {
        COLOR = vec4(0.0);
        return;
    }
    
    // Unpack normal map
    vec3 normal = unpack_normal(normal_sample);
    
    // Transform normal to world space (simplified for 2D)
    world_normal = normal;
    
    // Start with ambient lighting
    vec3 final_color = ambient_color * ambient_strength * albedo_sample.rgb;
    
    // Calculate view direction (camera looking down negative Z)
    vec3 view_dir = vec3(0.0, 0.0, 1.0);
    
    // Process each dynamic light
    for (int i = 0; i < light_count && i < 8; i++) {
        vec2 light_pos = light_positions[i];
        vec3 light_color = light_colors[i];
        float light_intensity = light_intensities[i];
        float light_range = light_ranges[i];
        
        // Calculate light direction and distance
        vec2 to_light_2d = light_pos - world_position;
        float distance = length(to_light_2d);
        
        // Skip if light is out of range
        if (distance > light_range) continue;
        
        vec3 light_dir = normalize(vec3(to_light_2d, 50.0)); // Assume light height of 50 units
        
        // Calculate attenuation
        float attenuation = calculate_attenuation(distance, light_range) * light_intensity;
        
        // Calculate shadow
        float shadow = calculate_shadow(world_position, light_pos, light_range);
        attenuation *= shadow;
        
        // Diffuse lighting
        vec3 diffuse = calculate_diffuse_lighting(normal, light_dir, light_color, attenuation);
        final_color += diffuse * albedo_sample.rgb;
        
        // Specular lighting (only if not fully rough)
        if (roughness < 0.99) {
            vec3 specular = calculate_specular_lighting(normal, light_dir, view_dir, light_color, attenuation);
            final_color += specular * (1.0 - metallic);
        }
        
        // Metallic reflection
        if (metallic > 0.01) {
            vec3 reflect_dir = reflect(-view_dir, normal);
            float metallic_factor = metallic * attenuation;
            final_color = mix(final_color, light_color * metallic_factor, metallic);
        }
    }
    
    // Add emission
    final_color += emission_sample.rgb * emission_strength;
    
    // Apply dithering for pixel art aesthetic
    final_color = apply_dithering(final_color, SCREEN_UV * SCREEN_PIXEL_SIZE);
    
    // Clamp to prevent over-bright pixels
    final_color = clamp(final_color, 0.0, 1.0);
    
    // Output final color
    COLOR = vec4(final_color, albedo_sample.a);
}
