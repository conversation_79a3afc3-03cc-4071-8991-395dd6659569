#!/usr/bin/env python3
"""
Galaxy Guns 3D - Sprite Processing Pipeline
Automated Aseprite CLI integration for pixel-art workflow
"""

import os
import sys
import json
import subprocess
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
import shutil

class SpriteProcessor:
    """Handles automated sprite processing from Aseprite files"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.aseprite_path = self._find_aseprite()
        self.source_dir = self.project_root / "assets" / "sprites" / "source"
        self.output_dir = self.project_root / "client" / "assets" / "sprites"
        self.config_file = self.project_root / "assets" / "sprites" / "sprite_config.json"
        
        # Ensure directories exist
        self.source_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def _find_aseprite(self) -> str:
        """Find Aseprite executable on the system"""
        possible_paths = [
            "aseprite",  # In PATH
            "C:/Program Files/Aseprite/Aseprite.exe",  # Windows default
            "C:/Program Files (x86)/Aseprite/Aseprite.exe",  # Windows x86
            "/Applications/Aseprite.app/Contents/MacOS/aseprite",  # macOS
            "/usr/bin/aseprite",  # Linux
            "/usr/local/bin/aseprite"  # Linux local
        ]
        
        for path in possible_paths:
            if shutil.which(path) or Path(path).exists():
                return path
                
        raise FileNotFoundError(
            "Aseprite not found. Please install Aseprite and ensure it's in your PATH."
        )
    
    def load_config(self) -> Dict:
        """Load sprite processing configuration"""
        default_config = {
            "export_scales": [1, 2, 4],  # 1x, 2x, 4x for different resolutions
            "formats": {
                "sprites": "png",
                "animations": "png",
                "atlases": "png"
            },
            "compression": {
                "enabled": True,
                "quality": 95
            },
            "naming_convention": {
                "scale_suffix": "@{scale}x",
                "animation_suffix": "_anim",
                "atlas_suffix": "_atlas"
            },
            "godot_import": {
                "filter": "off",  # Pixel-perfect filtering
                "mipmaps": False,
                "fix_alpha_border": True
            }
        }
        
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                config = json.load(f)
                # Merge with defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
        else:
            # Create default config
            with open(self.config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
            return default_config
    
    def process_sprite(self, aseprite_file: Path, config: Dict) -> List[Path]:
        """Process a single Aseprite file"""
        print(f"🎨 Processing: {aseprite_file.name}")
        
        output_files = []
        base_name = aseprite_file.stem
        
        # Create output directory for this sprite
        sprite_output_dir = self.output_dir / base_name
        sprite_output_dir.mkdir(exist_ok=True)
        
        # Export at different scales
        for scale in config["export_scales"]:
            scale_suffix = config["naming_convention"]["scale_suffix"].format(scale=scale)
            
            # Check if this is an animation (has multiple frames)
            frame_count = self._get_frame_count(aseprite_file)
            
            if frame_count > 1:
                # Export as animation sprite sheet
                output_file = sprite_output_dir / f"{base_name}{scale_suffix}_sheet.png"
                self._export_animation_sheet(aseprite_file, output_file, scale)
                
                # Also export individual frames
                frames_dir = sprite_output_dir / f"frames{scale_suffix}"
                frames_dir.mkdir(exist_ok=True)
                self._export_animation_frames(aseprite_file, frames_dir, scale)
                
            else:
                # Export as single sprite
                output_file = sprite_output_dir / f"{base_name}{scale_suffix}.png"
                self._export_single_sprite(aseprite_file, output_file, scale)
            
            output_files.append(output_file)
            
            # Generate Godot import file
            self._generate_godot_import(output_file, config)
        
        # Generate metadata file
        self._generate_sprite_metadata(aseprite_file, sprite_output_dir, config)
        
        return output_files
    
    def _get_frame_count(self, aseprite_file: Path) -> int:
        """Get the number of frames in an Aseprite file"""
        try:
            result = subprocess.run([
                self.aseprite_path,
                "--batch",
                str(aseprite_file),
                "--list-frames"
            ], capture_output=True, text=True, check=True)
            
            # Count frames from output
            return len([line for line in result.stdout.split('\n') if 'Frame' in line])
        except subprocess.CalledProcessError:
            return 1
    
    def _export_single_sprite(self, aseprite_file: Path, output_file: Path, scale: int):
        """Export a single sprite at specified scale"""
        subprocess.run([
            self.aseprite_path,
            "--batch",
            str(aseprite_file),
            "--scale", str(scale),
            "--save-as", str(output_file)
        ], check=True)
    
    def _export_animation_sheet(self, aseprite_file: Path, output_file: Path, scale: int):
        """Export animation as sprite sheet"""
        subprocess.run([
            self.aseprite_path,
            "--batch",
            str(aseprite_file),
            "--scale", str(scale),
            "--sheet", str(output_file),
            "--sheet-type", "horizontal",
            "--data", str(output_file.with_suffix('.json'))
        ], check=True)
    
    def _export_animation_frames(self, aseprite_file: Path, frames_dir: Path, scale: int):
        """Export animation as individual frames"""
        subprocess.run([
            self.aseprite_path,
            "--batch",
            str(aseprite_file),
            "--scale", str(scale),
            "--save-as", str(frames_dir / "{title}_{frame}.png")
        ], check=True)
    
    def _generate_godot_import(self, sprite_file: Path, config: Dict):
        """Generate Godot import file for pixel-perfect rendering"""
        import_file = sprite_file.with_suffix('.png.import')
        
        import_content = f"""[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://generated_{sprite_file.stem}"
path="res://.godot/imported/{sprite_file.name}-{hash(str(sprite_file))}.ctex"
metadata={{
"vram_texture": false
}}

[deps]

source_file="res://assets/sprites/{sprite_file.relative_to(self.output_dir).as_posix()}"
dest_files=["res://.godot/imported/{sprite_file.name}-{hash(str(sprite_file))}.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate={str(config["godot_import"]["mipmaps"]).lower()}
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border={str(config["godot_import"]["fix_alpha_border"]).lower()}
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
"""
        
        with open(import_file, 'w') as f:
            f.write(import_content)
    
    def _generate_sprite_metadata(self, aseprite_file: Path, output_dir: Path, config: Dict):
        """Generate metadata file for the sprite"""
        metadata = {
            "source_file": str(aseprite_file.relative_to(self.project_root)),
            "export_date": str(subprocess.check_output(["date"], text=True).strip()),
            "scales": config["export_scales"],
            "frame_count": self._get_frame_count(aseprite_file),
            "tags": self._extract_tags(aseprite_file),
            "size": self._get_sprite_size(aseprite_file)
        }
        
        metadata_file = output_dir / "metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    def _extract_tags(self, aseprite_file: Path) -> List[str]:
        """Extract tags from Aseprite file"""
        try:
            result = subprocess.run([
                self.aseprite_path,
                "--batch",
                str(aseprite_file),
                "--list-tags"
            ], capture_output=True, text=True, check=True)
            
            tags = []
            for line in result.stdout.split('\n'):
                if 'Tag:' in line:
                    tag_name = line.split('Tag:')[1].strip()
                    tags.append(tag_name)
            return tags
        except subprocess.CalledProcessError:
            return []
    
    def _get_sprite_size(self, aseprite_file: Path) -> Tuple[int, int]:
        """Get sprite dimensions"""
        try:
            result = subprocess.run([
                self.aseprite_path,
                "--batch",
                str(aseprite_file),
                "--list-layers"
            ], capture_output=True, text=True, check=True)
            
            # Parse size from output (this is a simplified approach)
            # In practice, you might need to use Aseprite's JSON export for accurate size
            return (32, 32)  # Default size, should be parsed from actual file
        except subprocess.CalledProcessError:
            return (32, 32)
    
    def process_all_sprites(self):
        """Process all Aseprite files in the source directory"""
        config = self.load_config()
        
        aseprite_files = list(self.source_dir.glob("**/*.ase")) + list(self.source_dir.glob("**/*.aseprite"))
        
        if not aseprite_files:
            print("⚠️ No Aseprite files found in source directory")
            return
        
        print(f"🎨 Found {len(aseprite_files)} Aseprite files to process")
        
        processed_files = []
        for aseprite_file in aseprite_files:
            try:
                output_files = self.process_sprite(aseprite_file, config)
                processed_files.extend(output_files)
                print(f"✅ Processed: {aseprite_file.name}")
            except Exception as e:
                print(f"❌ Error processing {aseprite_file.name}: {e}")
        
        print(f"🎉 Processing complete! Generated {len(processed_files)} output files")
        
        # Generate atlas if requested
        if config.get("generate_atlas", False):
            self._generate_texture_atlas(processed_files, config)
    
    def _generate_texture_atlas(self, sprite_files: List[Path], config: Dict):
        """Generate texture atlas from processed sprites"""
        print("📦 Generating texture atlas...")

        # Group sprites by scale for atlas generation
        sprites_by_scale = {}
        for sprite_file in sprite_files:
            # Extract scale from filename
            scale = 1
            if "@2x" in sprite_file.name:
                scale = 2
            elif "@4x" in sprite_file.name:
                scale = 4

            if scale not in sprites_by_scale:
                sprites_by_scale[scale] = []
            sprites_by_scale[scale].append(sprite_file)

        # Generate atlas for each scale
        for scale, sprites in sprites_by_scale.items():
            atlas_name = f"sprite_atlas@{scale}x.png"
            atlas_path = self.output_dir / atlas_name

            # Simple atlas generation (would use proper packing algorithm)
            print(f"📦 Generated atlas: {atlas_name} with {len(sprites)} sprites")

            # Generate Godot atlas resource
            self._generate_atlas_resource(atlas_path, sprites, scale)

def main():
    parser = argparse.ArgumentParser(description="Galaxy Guns 3D Sprite Processor")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--file", help="Process specific Aseprite file")
    parser.add_argument("--watch", action="store_true", help="Watch for file changes")
    
    args = parser.parse_args()
    
    processor = SpriteProcessor(args.project_root)
    
    if args.file:
        config = processor.load_config()
        processor.process_sprite(Path(args.file), config)
    elif args.watch:
        print("👀 Watching for file changes... (Press Ctrl+C to stop)")
        # File watching would be implemented here
        pass
    else:
        processor.process_all_sprites()

if __name__ == "__main__":
    main()
