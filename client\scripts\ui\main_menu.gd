# Galaxy Guns 3D - Main Menu
# Simple main menu for game navigation

extends Control

func _ready():
	print("🎮 Main Menu loaded")

func _on_play_button_pressed():
	print("🚀 Starting game...")
	get_tree().change_scene_to_file("res://scenes/game.tscn")

func _on_settings_button_pressed():
	print("⚙️ Settings (not implemented)")

func _on_quit_button_pressed():
	print("👋 Quitting game...")
	get_tree().quit()
