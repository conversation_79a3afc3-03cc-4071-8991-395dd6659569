# Galaxy Guns 3D - Anti-Cheat System
# Client-side validation and server-side verification
# Detects speed hacking, aimbots, wallhacks, and other cheats

class_name AntiCheat
extends Node

## Signals for anti-cheat events
signal cheat_detected(cheat_type: String, severity: int, evidence: Dictionary)
signal player_flagged(player_id: int, reason: String)
signal validation_failed(validation_type: String, player_id: int)

## Anti-cheat configuration
@export_group("Detection Settings")
@export var movement_validation_enabled: bool = true
@export var aim_validation_enabled: bool = true
@export var shot_validation_enabled: bool = true
@export var timing_validation_enabled: bool = true

@export_group("Thresholds")
@export var max_speed_multiplier: float = 1.2  # 20% over normal speed
@export var max_acceleration: float = 50.0     # m/s²
@export var max_aim_snap_angle: float = 180.0  # degrees per frame
@export var min_human_reaction_time: float = 0.100  # 100ms

## Validation systems
var movement_validator: MovementValidator
var aim_validator: AimValidator
var shot_validator: ShotValidator
var timing_validator: TimingValidator

## Player tracking
var player_data: Dictionary = {}  # player_id -> PlayerAntiCheatData
var violation_history: Dictionary = {}  # player_id -> Array[Violation]

## Performance tracking
var validation_time: float = 0.0
var validations_per_second: int = 0
var false_positive_rate: float = 0.0

## Player anti-cheat data
class PlayerAntiCheatData:
	var player_id: int
	var last_position: Vector3
	var last_velocity: Vector3
	var last_rotation: Vector3
	var last_update_time: float
	var movement_history: Array[Vector3] = []
	var aim_history: Array[Vector3] = []
	var shot_history: Array[ShotData] = []
	var input_timing: Array[float] = []
	var violation_count: int = 0
	var trust_score: float = 100.0
	
	func _init(id: int):
		player_id = id
		last_position = Vector3.ZERO
		last_velocity = Vector3.ZERO
		last_rotation = Vector3.ZERO
		last_update_time = Time.get_time_dict_from_system()["unix"]
	
	func update_position(pos: Vector3, vel: Vector3, timestamp: float):
		last_position = pos
		last_velocity = vel
		last_update_time = timestamp
		
		movement_history.append(pos)
		if movement_history.size() > 60:  # Keep 1 second of history at 60fps
			movement_history.pop_front()
	
	func update_aim(rotation: Vector3, timestamp: float):
		last_rotation = rotation
		last_update_time = timestamp
		
		aim_history.append(rotation)
		if aim_history.size() > 60:
			aim_history.pop_front()
	
	func add_shot(shot_data: ShotData):
		shot_history.append(shot_data)
		if shot_history.size() > 100:  # Keep last 100 shots
			shot_history.pop_front()
	
	func add_input_timing(timestamp: float):
		input_timing.append(timestamp)
		if input_timing.size() > 100:
			input_timing.pop_front()

## Shot data for validation
class ShotData:
	var timestamp: float
	var origin: Vector3
	var direction: Vector3
	var target_position: Vector3
	var hit: bool
	var reaction_time: float
	
	func _init(ts: float, orig: Vector3, dir: Vector3, target: Vector3, did_hit: bool, reaction: float):
		timestamp = ts
		origin = orig
		direction = dir
		target_position = target
		hit = did_hit
		reaction_time = reaction

## Violation record
class Violation:
	var timestamp: float
	var cheat_type: String
	var severity: int  # 1-10 scale
	var evidence: Dictionary
	var false_positive: bool = false
	
	func _init(ts: float, type: String, sev: int, ev: Dictionary):
		timestamp = ts
		cheat_type = type
		severity = sev
		evidence = ev

## Movement Validator
class MovementValidator extends Node:
	var parent_anticheat: AntiCheat
	
	func _init(anticheat: AntiCheat):
		parent_anticheat = anticheat
	
	func validate_movement(player_id: int, position: Vector3, velocity: Vector3, timestamp: float) -> bool:
		if not parent_anticheat.player_data.has(player_id):
			return true  # No previous data to compare
		
		var player_data = parent_anticheat.player_data[player_id]
		var time_delta = timestamp - player_data.last_update_time
		
		if time_delta <= 0:
			return false  # Invalid timestamp
		
		# Check speed limits
		var distance = position.distance_to(player_data.last_position)
		var speed = distance / time_delta
		var max_allowed_speed = 6.0 * parent_anticheat.max_speed_multiplier  # Run speed * multiplier
		
		if speed > max_allowed_speed:
			_report_speed_violation(player_id, speed, max_allowed_speed)
			return false
		
		# Check acceleration limits
		var acceleration = (velocity - player_data.last_velocity).length() / time_delta
		if acceleration > parent_anticheat.max_acceleration:
			_report_acceleration_violation(player_id, acceleration)
			return false
		
		# Check for teleportation
		if distance > 10.0 and time_delta < 0.1:  # 10m in less than 100ms
			_report_teleport_violation(player_id, distance, time_delta)
			return false
		
		# Check movement consistency
		if not _validate_movement_consistency(player_data, position, velocity, time_delta):
			return false
		
		return true
	
	func _validate_movement_consistency(player_data: PlayerAntiCheatData, position: Vector3, velocity: Vector3, time_delta: float) -> bool:
		if player_data.movement_history.size() < 3:
			return true
		
		# Check for unnatural movement patterns
		var recent_positions = player_data.movement_history.slice(-3)
		var movement_vectors = []
		
		for i in range(recent_positions.size() - 1):
			movement_vectors.append(recent_positions[i + 1] - recent_positions[i])
		
		# Check for perfect straight lines (bot-like movement)
		if movement_vectors.size() >= 2:
			var angle_variance = 0.0
			for i in range(movement_vectors.size() - 1):
				var angle = movement_vectors[i].angle_to(movement_vectors[i + 1])
				angle_variance += angle
			
			if angle_variance < 0.01:  # Too perfect movement
				_report_bot_movement_violation(player_data.player_id)
				return false
		
		return true
	
	func _report_speed_violation(player_id: int, speed: float, max_speed: float):
		var evidence = {
			"actual_speed": speed,
			"max_allowed_speed": max_speed,
			"violation_ratio": speed / max_speed
		}
		parent_anticheat._report_violation(player_id, "speed_hack", 8, evidence)
	
	func _report_acceleration_violation(player_id: int, acceleration: float):
		var evidence = {
			"acceleration": acceleration,
			"max_allowed": parent_anticheat.max_acceleration
		}
		parent_anticheat._report_violation(player_id, "acceleration_hack", 7, evidence)
	
	func _report_teleport_violation(player_id: int, distance: float, time_delta: float):
		var evidence = {
			"distance": distance,
			"time_delta": time_delta,
			"implied_speed": distance / time_delta
		}
		parent_anticheat._report_violation(player_id, "teleport_hack", 10, evidence)
	
	func _report_bot_movement_violation(player_id: int):
		var evidence = {"movement_pattern": "too_perfect"}
		parent_anticheat._report_violation(player_id, "movement_bot", 6, evidence)

## Aim Validator
class AimValidator extends Node:
	var parent_anticheat: AntiCheat
	
	func _init(anticheat: AntiCheat):
		parent_anticheat = anticheat
	
	func validate_aim(player_id: int, rotation: Vector3, timestamp: float) -> bool:
		if not parent_anticheat.player_data.has(player_id):
			return true
		
		var player_data = parent_anticheat.player_data[player_id]
		var time_delta = timestamp - player_data.last_update_time
		
		if time_delta <= 0:
			return false
		
		# Check aim snap detection
		var rotation_delta = rotation - player_data.last_rotation
		var aim_speed = rotation_delta.length() / time_delta
		var max_aim_speed = deg_to_rad(parent_anticheat.max_aim_snap_angle)
		
		if aim_speed > max_aim_speed:
			_report_aim_snap_violation(player_id, aim_speed, max_aim_speed)
			return false
		
		# Check for inhuman precision
		if not _validate_aim_humanness(player_data, rotation, time_delta):
			return false
		
		return true
	
	func _validate_aim_humanness(player_data: PlayerAntiCheatData, rotation: Vector3, time_delta: float) -> bool:
		if player_data.aim_history.size() < 10:
			return true
		
		# Check for perfect tracking (aimbot indicator)
		var recent_aims = player_data.aim_history.slice(-10)
		var smoothness_variance = 0.0
		
		for i in range(recent_aims.size() - 1):
			var delta = recent_aims[i + 1] - recent_aims[i]
			smoothness_variance += delta.length()
		
		smoothness_variance /= recent_aims.size() - 1
		
		# Too smooth aiming indicates aimbot
		if smoothness_variance < 0.001:
			_report_aimbot_violation(player_data.player_id, smoothness_variance)
			return false
		
		# Check for micro-corrections (human trait)
		var has_micro_corrections = false
		for i in range(recent_aims.size() - 2):
			var delta1 = recent_aims[i + 1] - recent_aims[i]
			var delta2 = recent_aims[i + 2] - recent_aims[i + 1]
			
			if delta1.dot(delta2) < 0:  # Direction change
				has_micro_corrections = true
				break
		
		if not has_micro_corrections and recent_aims.size() >= 10:
			_report_inhuman_aim_violation(player_data.player_id)
			return false
		
		return true
	
	func _report_aim_snap_violation(player_id: int, aim_speed: float, max_speed: float):
		var evidence = {
			"aim_speed": aim_speed,
			"max_allowed": max_speed,
			"violation_ratio": aim_speed / max_speed
		}
		parent_anticheat._report_violation(player_id, "aim_snap", 9, evidence)
	
	func _report_aimbot_violation(player_id: int, smoothness: float):
		var evidence = {"smoothness_variance": smoothness}
		parent_anticheat._report_violation(player_id, "aimbot", 10, evidence)
	
	func _report_inhuman_aim_violation(player_id: int):
		var evidence = {"lack_of_micro_corrections": true}
		parent_anticheat._report_violation(player_id, "inhuman_aim", 8, evidence)

## Shot Validator
class ShotValidator extends Node:
	var parent_anticheat: AntiCheat
	
	func _init(anticheat: AntiCheat):
		parent_anticheat = anticheat
	
	func validate_shot(player_id: int, shot_data: ShotData) -> bool:
		if not parent_anticheat.player_data.has(player_id):
			return true
		
		var player_data = parent_anticheat.player_data[player_id]
		
		# Check reaction time
		if shot_data.reaction_time < parent_anticheat.min_human_reaction_time:
			_report_reaction_time_violation(player_id, shot_data.reaction_time)
			return false
		
		# Check hit rate consistency
		if not _validate_hit_rate(player_data):
			return false
		
		# Check for wallhack indicators
		if not _validate_line_of_sight(shot_data):
			return false
		
		return true
	
	func _validate_hit_rate(player_data: PlayerAntiCheatData) -> bool:
		if player_data.shot_history.size() < 20:
			return true
		
		var recent_shots = player_data.shot_history.slice(-20)
		var hit_count = 0
		
		for shot in recent_shots:
			if shot.hit:
				hit_count += 1
		
		var hit_rate = float(hit_count) / recent_shots.size()
		
		# Suspiciously high hit rate
		if hit_rate > 0.95:
			_report_high_hit_rate_violation(player_data.player_id, hit_rate)
			return false
		
		return true
	
	func _validate_line_of_sight(shot_data: ShotData) -> bool:
		# This would check if the shot target was actually visible
		# For now, simplified validation
		var distance = shot_data.origin.distance_to(shot_data.target_position)
		
		# Shots through walls would have impossible distances/angles
		if distance > 1000.0:  # Unrealistic shot distance
			_report_wallhack_violation(shot_data)
			return false
		
		return true
	
	func _report_reaction_time_violation(player_id: int, reaction_time: float):
		var evidence = {
			"reaction_time": reaction_time,
			"min_human_time": parent_anticheat.min_human_reaction_time
		}
		parent_anticheat._report_violation(player_id, "inhuman_reactions", 9, evidence)
	
	func _report_high_hit_rate_violation(player_id: int, hit_rate: float):
		var evidence = {"hit_rate": hit_rate}
		parent_anticheat._report_violation(player_id, "suspicious_accuracy", 7, evidence)
	
	func _report_wallhack_violation(shot_data: ShotData):
		var evidence = {
			"shot_distance": shot_data.origin.distance_to(shot_data.target_position),
			"shot_angle": shot_data.direction
		}
		# Would need player_id from shot_data
		# parent_anticheat._report_violation(shot_data.shooter_id, "wallhack", 10, evidence)

## Timing Validator
class TimingValidator extends Node:
	var parent_anticheat: AntiCheat
	
	func _init(anticheat: AntiCheat):
		parent_anticheat = anticheat
	
	func validate_input_timing(player_id: int, timestamp: float) -> bool:
		if not parent_anticheat.player_data.has(player_id):
			return true
		
		var player_data = parent_anticheat.player_data[player_id]
		
		# Check for macro/script usage
		if not _validate_timing_variance(player_data):
			return false
		
		# Check for impossible input rates
		if not _validate_input_rate(player_data, timestamp):
			return false
		
		return true
	
	func _validate_timing_variance(player_data: PlayerAntiCheatData) -> bool:
		if player_data.input_timing.size() < 10:
			return true
		
		var recent_timings = player_data.input_timing.slice(-10)
		var intervals = []
		
		for i in range(recent_timings.size() - 1):
			intervals.append(recent_timings[i + 1] - recent_timings[i])
		
		# Calculate variance in timing
		var mean_interval = 0.0
		for interval in intervals:
			mean_interval += interval
		mean_interval /= intervals.size()
		
		var variance = 0.0
		for interval in intervals:
			variance += (interval - mean_interval) * (interval - mean_interval)
		variance /= intervals.size()
		
		# Too consistent timing indicates macro usage
		if variance < 0.001:  # Very low variance
			_report_macro_violation(player_data.player_id, variance)
			return false
		
		return true
	
	func _validate_input_rate(player_data: PlayerAntiCheatData, timestamp: float) -> bool:
		if player_data.input_timing.size() < 5:
			return true
		
		var recent_timings = player_data.input_timing.slice(-5)
		var time_span = timestamp - recent_timings[0]
		var input_rate = recent_timings.size() / time_span
		
		# Impossible input rate (faster than human possible)
		if input_rate > 20.0:  # 20 inputs per second
			_report_input_rate_violation(player_data.player_id, input_rate)
			return false
		
		return true
	
	func _report_macro_violation(player_id: int, variance: float):
		var evidence = {"timing_variance": variance}
		parent_anticheat._report_violation(player_id, "macro_usage", 8, evidence)
	
	func _report_input_rate_violation(player_id: int, input_rate: float):
		var evidence = {"input_rate": input_rate}
		parent_anticheat._report_violation(player_id, "impossible_input_rate", 9, evidence)

func _ready():
	# Initialize validation systems
	movement_validator = MovementValidator.new(self)
	aim_validator = AimValidator.new(self)
	shot_validator = ShotValidator.new(self)
	timing_validator = TimingValidator.new(self)
	
	add_child(movement_validator)
	add_child(aim_validator)
	add_child(shot_validator)
	add_child(timing_validator)
	
	print("🛡️ Anti-Cheat system initialized")

## Public API

func register_player(player_id: int):
	"""Register player for anti-cheat monitoring"""
	player_data[player_id] = PlayerAntiCheatData.new(player_id)
	violation_history[player_id] = []

func unregister_player(player_id: int):
	"""Unregister player from monitoring"""
	player_data.erase(player_id)
	violation_history.erase(player_id)

func validate_player_movement(player_id: int, position: Vector3, velocity: Vector3, timestamp: float) -> bool:
	"""Validate player movement"""
	if not movement_validation_enabled:
		return true
	
	var result = movement_validator.validate_movement(player_id, position, velocity, timestamp)
	
	# Update player data
	if player_data.has(player_id):
		player_data[player_id].update_position(position, velocity, timestamp)
	
	return result

func validate_player_aim(player_id: int, rotation: Vector3, timestamp: float) -> bool:
	"""Validate player aim"""
	if not aim_validation_enabled:
		return true
	
	var result = aim_validator.validate_aim(player_id, rotation, timestamp)
	
	# Update player data
	if player_data.has(player_id):
		player_data[player_id].update_aim(rotation, timestamp)
	
	return result

func validate_player_shot(player_id: int, shot_data: ShotData) -> bool:
	"""Validate player shot"""
	if not shot_validation_enabled:
		return true
	
	var result = shot_validator.validate_shot(player_id, shot_data)
	
	# Update player data
	if player_data.has(player_id):
		player_data[player_id].add_shot(shot_data)
	
	return result

func validate_input_timing(player_id: int, timestamp: float) -> bool:
	"""Validate input timing"""
	if not timing_validation_enabled:
		return true
	
	var result = timing_validator.validate_input_timing(player_id, timestamp)
	
	# Update player data
	if player_data.has(player_id):
		player_data[player_id].add_input_timing(timestamp)
	
	return result

func get_player_trust_score(player_id: int) -> float:
	"""Get player's trust score"""
	if player_data.has(player_id):
		return player_data[player_id].trust_score
	return 100.0

func get_anti_cheat_stats() -> Dictionary:
	"""Get anti-cheat statistics"""
	var total_violations = 0
	for player_id in violation_history.keys():
		total_violations += violation_history[player_id].size()
	
	return {
		"monitored_players": player_data.size(),
		"total_violations": total_violations,
		"validation_time": validation_time,
		"validations_per_second": validations_per_second,
		"false_positive_rate": false_positive_rate
	}

## Private Methods

func _report_violation(player_id: int, cheat_type: String, severity: int, evidence: Dictionary):
	"""Report a cheat violation"""
	var violation = Violation.new(Time.get_time_dict_from_system()["unix"], cheat_type, severity, evidence)
	
	if not violation_history.has(player_id):
		violation_history[player_id] = []
	
	violation_history[player_id].append(violation)
	
	# Update player trust score
	if player_data.has(player_id):
		player_data[player_id].trust_score -= severity * 2.0
		player_data[player_id].violation_count += 1
	
	# Emit signals
	cheat_detected.emit(cheat_type, severity, evidence)
	
	# Flag player if trust score is too low
	if player_data.has(player_id) and player_data[player_id].trust_score < 50.0:
		player_flagged.emit(player_id, "Low trust score due to violations")
	
	print("🚨 Cheat detected: Player %d - %s (Severity: %d)" % [player_id, cheat_type, severity])
