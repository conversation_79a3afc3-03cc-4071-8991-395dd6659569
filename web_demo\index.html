<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galaxy Guns 3D - Web Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            font-family: 'Courier New', monospace;
            color: white;
            overflow: hidden;
            user-select: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        #gameCanvas {
            background: radial-gradient(circle at center, #2a2a4a 0%, #0a0a0a 100%);
            border: 2px solid #ff6b6b;
            box-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
            cursor: crosshair;
        }

        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        #hud {
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 18px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            pointer-events: none;
        }

        .crosshair-line {
            position: absolute;
            background: #ff6b6b;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.8);
        }

        .crosshair-h {
            width: 20px;
            height: 2px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .crosshair-v {
            width: 2px;
            height: 20px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        #ammoCounter {
            position: absolute;
            bottom: 30px;
            right: 30px;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        }

        #healthBar {
            position: absolute;
            bottom: 30px;
            left: 30px;
            width: 200px;
            height: 20px;
            background: rgba(0,0,0,0.5);
            border: 2px solid #fff;
            border-radius: 10px;
            overflow: hidden;
        }

        #healthFill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
            transition: width 0.3s ease;
            border-radius: 8px;
        }

        #minimap {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 150px;
            height: 150px;
            background: rgba(0,0,0,0.7);
            border: 2px solid #666;
            border-radius: 10px;
        }

        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            font-size: 14px;
            opacity: 0.8;
        }

        #startScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }

        #title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(255, 107, 107, 0.8); }
            to { text-shadow: 0 0 30px rgba(255, 107, 107, 1), 0 0 40px rgba(255, 107, 107, 0.8); }
        }

        #startButton {
            padding: 15px 30px;
            font-size: 20px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            border: none;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            font-family: 'Courier New', monospace;
        }

        #startButton:hover {
            background: linear-gradient(45deg, #ff8e8e, #ffaaaa);
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }

        #gameMode {
            margin-bottom: 30px;
            text-align: center;
        }

        .enemy {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff4444;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.8);
            transition: all 0.1s ease;
        }

        .bullet {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #ffff00;
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(255, 255, 0, 0.8);
        }

        .explosion {
            position: absolute;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle, #ff6b6b 0%, #ff4444 50%, transparent 100%);
            border-radius: 50%;
            animation: explode 0.3s ease-out forwards;
        }

        @keyframes explode {
            0% { transform: scale(0); opacity: 1; }
            100% { transform: scale(2); opacity: 0; }
        }

        #score {
            position: absolute;
            top: 60px;
            left: 20px;
            font-size: 20px;
            font-weight: bold;
        }

        #gameOver {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 200;
        }

        #finalScore {
            font-size: 36px;
            margin-bottom: 20px;
            color: #ff6b6b;
        }

        .muzzleFlash {
            position: absolute;
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, #ffff00 0%, #ff6b6b 50%, transparent 100%);
            border-radius: 50%;
            animation: flash 0.1s ease-out forwards;
            pointer-events: none;
        }

        @keyframes flash {
            0% { transform: scale(0); opacity: 1; }
            100% { transform: scale(1.5); opacity: 0; }
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="1200" height="800"></canvas>
        
        <div id="ui">
            <div id="hud">
                <div>GALAXY GUNS 3D</div>
                <div id="score">Score: 0</div>
                <div>Wave: 1</div>
            </div>
            
            <div id="crosshair">
                <div class="crosshair-line crosshair-h"></div>
                <div class="crosshair-line crosshair-v"></div>
            </div>
            
            <div id="ammoCounter">30 / 120</div>
            
            <div id="healthBar">
                <div id="healthFill" style="width: 100%"></div>
            </div>
            
            <div id="minimap"></div>
            
            <div id="controls">
                WASD: Move | Mouse: Aim | Click: Shoot | R: Reload | Space: Jump
            </div>
        </div>
        
        <div id="startScreen">
            <div id="title">GALAXY GUNS 3D</div>
            <div id="gameMode">
                <h3>🎮 Web Demo - Survival Mode</h3>
                <p>Survive waves of enemies in this mobile FPS experience!</p>
            </div>
            <button id="startButton" onclick="startGame()">START GAME</button>
            <button id="startButton" onclick="showControls()">CONTROLS</button>
        </div>
        
        <div id="gameOver">
            <div id="finalScore">Game Over!</div>
            <div>Final Score: <span id="finalScoreValue">0</span></div>
            <button id="startButton" onclick="restartGame()">PLAY AGAIN</button>
        </div>
    </div>

    <script>
        // Game state
        let gameState = {
            running: false,
            score: 0,
            wave: 1,
            health: 100,
            ammo: 30,
            reserveAmmo: 120,
            reloading: false,
            enemies: [],
            bullets: [],
            player: { x: 600, y: 400, angle: 0 },
            keys: {},
            mouse: { x: 0, y: 0, down: false },
            lastShot: 0,
            fireRate: 150, // ms between shots
            enemySpawnRate: 2000, // ms between enemy spawns
            lastEnemySpawn: 0,
            waveEnemiesRemaining: 10,
            waveEnemiesKilled: 0
        };

        // Get canvas and context
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');

        // Input handling
        document.addEventListener('keydown', (e) => {
            gameState.keys[e.code] = true;
            if (e.code === 'KeyR' && !gameState.reloading) {
                reload();
            }
            if (e.code === 'Space') {
                e.preventDefault();
            }
        });

        document.addEventListener('keyup', (e) => {
            gameState.keys[e.code] = false;
        });

        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            gameState.mouse.x = e.clientX - rect.left;
            gameState.mouse.y = e.clientY - rect.top;
            
            // Calculate player angle
            const dx = gameState.mouse.x - gameState.player.x;
            const dy = gameState.mouse.y - gameState.player.y;
            gameState.player.angle = Math.atan2(dy, dx);
        });

        canvas.addEventListener('mousedown', (e) => {
            gameState.mouse.down = true;
        });

        canvas.addEventListener('mouseup', (e) => {
            gameState.mouse.down = false;
        });

        // Game functions
        function startGame() {
            document.getElementById('startScreen').style.display = 'none';
            gameState.running = true;
            gameState.score = 0;
            gameState.wave = 1;
            gameState.health = 100;
            gameState.ammo = 30;
            gameState.reserveAmmo = 120;
            gameState.enemies = [];
            gameState.bullets = [];
            gameState.waveEnemiesRemaining = 10;
            gameState.waveEnemiesKilled = 0;
            gameLoop();
        }

        function restartGame() {
            document.getElementById('gameOver').style.display = 'none';
            startGame();
        }

        function showControls() {
            alert(`🎮 GALAXY GUNS 3D CONTROLS

🖱️ Mouse: Aim and look around
🖱️ Left Click: Fire weapon
⌨️ WASD: Move player
⌨️ R: Reload weapon
⌨️ Space: Jump (visual effect)

🎯 OBJECTIVE:
Survive waves of enemies and achieve the highest score!

🔫 FEATURES:
• Realistic weapon mechanics
• Wave-based enemy spawning
• Health and ammo management
• Score-based progression

Good luck, soldier! 🚀`);
        }

        function shoot() {
            const now = Date.now();
            if (now - gameState.lastShot < gameState.fireRate || gameState.ammo <= 0 || gameState.reloading) {
                return;
            }

            gameState.lastShot = now;
            gameState.ammo--;

            // Create bullet
            const bullet = {
                x: gameState.player.x,
                y: gameState.player.y,
                vx: Math.cos(gameState.player.angle) * 10,
                vy: Math.sin(gameState.player.angle) * 10,
                life: 100
            };
            gameState.bullets.push(bullet);

            // Muzzle flash effect
            createMuzzleFlash();

            // Update ammo display
            updateAmmoDisplay();

            // Auto-reload when empty
            if (gameState.ammo === 0 && gameState.reserveAmmo > 0) {
                setTimeout(() => reload(), 500);
            }
        }

        function reload() {
            if (gameState.reloading || gameState.reserveAmmo === 0 || gameState.ammo === 30) {
                return;
            }

            gameState.reloading = true;
            
            setTimeout(() => {
                const ammoNeeded = 30 - gameState.ammo;
                const ammoToReload = Math.min(ammoNeeded, gameState.reserveAmmo);
                
                gameState.ammo += ammoToReload;
                gameState.reserveAmmo -= ammoToReload;
                gameState.reloading = false;
                
                updateAmmoDisplay();
            }, 2000); // 2 second reload time
        }

        function createMuzzleFlash() {
            const flash = document.createElement('div');
            flash.className = 'muzzleFlash';
            flash.style.left = (gameState.player.x - 20) + 'px';
            flash.style.top = (gameState.player.y - 20) + 'px';
            document.getElementById('ui').appendChild(flash);
            
            setTimeout(() => {
                flash.remove();
            }, 100);
        }

        function spawnEnemy() {
            const side = Math.floor(Math.random() * 4);
            let x, y;
            
            switch(side) {
                case 0: // Top
                    x = Math.random() * canvas.width;
                    y = -20;
                    break;
                case 1: // Right
                    x = canvas.width + 20;
                    y = Math.random() * canvas.height;
                    break;
                case 2: // Bottom
                    x = Math.random() * canvas.width;
                    y = canvas.height + 20;
                    break;
                case 3: // Left
                    x = -20;
                    y = Math.random() * canvas.height;
                    break;
            }
            
            gameState.enemies.push({
                x: x,
                y: y,
                health: 100,
                speed: 1 + Math.random() * 2,
                lastShot: 0,
                fireRate: 1000 + Math.random() * 2000
            });
        }

        function updateEnemies() {
            gameState.enemies.forEach((enemy, index) => {
                // Move towards player
                const dx = gameState.player.x - enemy.x;
                const dy = gameState.player.y - enemy.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance > 0) {
                    enemy.x += (dx / distance) * enemy.speed;
                    enemy.y += (dy / distance) * enemy.speed;
                }
                
                // Enemy shooting (simplified)
                const now = Date.now();
                if (distance < 300 && now - enemy.lastShot > enemy.fireRate) {
                    enemy.lastShot = now;
                    // Enemy bullets would go here in full version
                    
                    // Damage player if close enough
                    if (distance < 50) {
                        gameState.health -= 10;
                        updateHealthDisplay();
                        
                        if (gameState.health <= 0) {
                            gameOver();
                        }
                    }
                }
            });
        }

        function updateBullets() {
            gameState.bullets.forEach((bullet, bulletIndex) => {
                bullet.x += bullet.vx;
                bullet.y += bullet.vy;
                bullet.life--;
                
                // Remove bullets that are off-screen or expired
                if (bullet.x < 0 || bullet.x > canvas.width || 
                    bullet.y < 0 || bullet.y > canvas.height || 
                    bullet.life <= 0) {
                    gameState.bullets.splice(bulletIndex, 1);
                    return;
                }
                
                // Check collision with enemies
                gameState.enemies.forEach((enemy, enemyIndex) => {
                    const dx = bullet.x - enemy.x;
                    const dy = bullet.y - enemy.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < 15) {
                        // Hit enemy
                        enemy.health -= 50;
                        gameState.bullets.splice(bulletIndex, 1);
                        
                        if (enemy.health <= 0) {
                            // Enemy killed
                            createExplosion(enemy.x, enemy.y);
                            gameState.enemies.splice(enemyIndex, 1);
                            gameState.score += 100;
                            gameState.waveEnemiesKilled++;
                            updateScoreDisplay();
                            
                            // Check if wave complete
                            if (gameState.waveEnemiesKilled >= gameState.waveEnemiesRemaining) {
                                nextWave();
                            }
                        }
                    }
                });
            });
        }

        function createExplosion(x, y) {
            const explosion = document.createElement('div');
            explosion.className = 'explosion';
            explosion.style.left = (x - 15) + 'px';
            explosion.style.top = (y - 15) + 'px';
            document.getElementById('ui').appendChild(explosion);
            
            setTimeout(() => {
                explosion.remove();
            }, 300);
        }

        function nextWave() {
            gameState.wave++;
            gameState.waveEnemiesRemaining = 10 + (gameState.wave * 5);
            gameState.waveEnemiesKilled = 0;
            gameState.enemySpawnRate = Math.max(500, gameState.enemySpawnRate - 100);
            
            // Bonus ammo for completing wave
            gameState.reserveAmmo += 60;
            updateAmmoDisplay();
            updateWaveDisplay();
        }

        function updatePlayer() {
            const speed = 3;
            
            if (gameState.keys['KeyW'] || gameState.keys['ArrowUp']) {
                gameState.player.y = Math.max(20, gameState.player.y - speed);
            }
            if (gameState.keys['KeyS'] || gameState.keys['ArrowDown']) {
                gameState.player.y = Math.min(canvas.height - 20, gameState.player.y + speed);
            }
            if (gameState.keys['KeyA'] || gameState.keys['ArrowLeft']) {
                gameState.player.x = Math.max(20, gameState.player.x - speed);
            }
            if (gameState.keys['KeyD'] || gameState.keys['ArrowRight']) {
                gameState.player.x = Math.min(canvas.width - 20, gameState.player.x + speed);
            }
            
            // Shooting
            if (gameState.mouse.down) {
                shoot();
            }
        }

        function render() {
            // Clear canvas
            ctx.fillStyle = 'rgba(10, 10, 10, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw grid background
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            for (let x = 0; x < canvas.width; x += 50) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            for (let y = 0; y < canvas.height; y += 50) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
            
            // Draw player
            ctx.save();
            ctx.translate(gameState.player.x, gameState.player.y);
            ctx.rotate(gameState.player.angle);
            
            // Player body
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(-10, -8, 20, 16);
            
            // Player weapon
            ctx.fillStyle = '#666';
            ctx.fillRect(10, -2, 25, 4);
            
            ctx.restore();
            
            // Draw enemies
            gameState.enemies.forEach(enemy => {
                ctx.fillStyle = '#ff4444';
                ctx.beginPath();
                ctx.arc(enemy.x, enemy.y, 10, 0, Math.PI * 2);
                ctx.fill();
                
                // Enemy health bar
                ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                ctx.fillRect(enemy.x - 15, enemy.y - 20, 30, 4);
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(enemy.x - 15, enemy.y - 20, (enemy.health / 100) * 30, 4);
            });
            
            // Draw bullets
            gameState.bullets.forEach(bullet => {
                ctx.fillStyle = '#ffff00';
                ctx.beginPath();
                ctx.arc(bullet.x, bullet.y, 2, 0, Math.PI * 2);
                ctx.fill();
                
                // Bullet trail
                ctx.strokeStyle = 'rgba(255, 255, 0, 0.5)';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(bullet.x, bullet.y);
                ctx.lineTo(bullet.x - bullet.vx * 3, bullet.y - bullet.vy * 3);
                ctx.stroke();
            });
        }

        function updateAmmoDisplay() {
            document.getElementById('ammoCounter').textContent = 
                `${gameState.ammo} / ${gameState.reserveAmmo}${gameState.reloading ? ' (Reloading...)' : ''}`;
        }

        function updateHealthDisplay() {
            document.getElementById('healthFill').style.width = `${gameState.health}%`;
            if (gameState.health < 30) {
                document.getElementById('healthFill').style.background = 
                    'linear-gradient(90deg, #f44336 0%, #ff5722 100%)';
            }
        }

        function updateScoreDisplay() {
            document.getElementById('score').textContent = `Score: ${gameState.score}`;
        }

        function updateWaveDisplay() {
            document.querySelector('#hud div:nth-child(3)').textContent = `Wave: ${gameState.wave}`;
        }

        function gameOver() {
            gameState.running = false;
            document.getElementById('finalScoreValue').textContent = gameState.score;
            document.getElementById('gameOver').style.display = 'flex';
        }

        function gameLoop() {
            if (!gameState.running) return;
            
            updatePlayer();
            updateEnemies();
            updateBullets();
            
            // Spawn enemies
            const now = Date.now();
            if (now - gameState.lastEnemySpawn > gameState.enemySpawnRate && 
                gameState.enemies.length < 15) {
                spawnEnemy();
                gameState.lastEnemySpawn = now;
            }
            
            render();
            requestAnimationFrame(gameLoop);
        }

        // Initialize displays
        updateAmmoDisplay();
        updateScoreDisplay();
        updateWaveDisplay();

        console.log("🎮 Galaxy Guns 3D Web Demo Loaded!");
        console.log("Click START GAME to begin the FPS experience!");
    </script>
</body>
</html>
