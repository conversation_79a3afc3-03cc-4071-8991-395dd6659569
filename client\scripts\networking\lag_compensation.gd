# Galaxy Guns 3D - Lag Compensation System
# Advanced lag compensation with hit validation and rollback
# Optimized for mobile with up to 120ms latency tolerance

class_name LagCompensation
extends Node

## Signals for lag compensation events
signal shot_validated(shooter_id: int, hit_confirmed: bool)
signal rollback_performed(tick: int, duration_ms: float)
signal desync_detected(player_id: int, error_magnitude: float)

## Configuration
@export_group("Lag Compensation")
@export var max_rollback_time: float = 0.200  # 200ms max rollback
@export var hit_validation_enabled: bool = true
@export var interpolation_buffer_size: int = 32
@export var extrapolation_limit: float = 0.050  # 50ms max extrapolation

@export_group("Hit Registration")
@export var hit_scan_tolerance: float = 0.1  # 10cm hit tolerance
@export var projectile_prediction_enabled: bool = true
@export var hit_confirmation_timeout: float = 1.0

## World state history for rollback
var world_state_history: Array[WorldStateSnapshot] = []
var player_state_history: Dictionary = {}  # player_id -> Array[PlayerStateSnapshot]
var shot_history: Array[ShotSnapshot] = []

## Interpolation and prediction
var interpolation_delay: float = 0.100  # 100ms interpolation delay
var prediction_enabled: bool = true
var rollback_in_progress: bool = false

## Performance tracking
var rollback_count: int = 0
var average_rollback_time: float = 0.0
var hit_validation_time: float = 0.0

## World state snapshot for rollback
class WorldStateSnapshot:
	var tick: int
	var timestamp: float
	var player_positions: Dictionary = {}
	var player_rotations: Dictionary = {}
	var player_velocities: Dictionary = {}
	var player_health: Dictionary = {}
	var projectiles: Array[ProjectileState] = []
	var destructible_objects: Dictionary = {}
	
	func _init(t: int, ts: float):
		tick = t
		timestamp = ts
	
	func add_player_state(player_id: int, pos: Vector3, rot: Vector3, vel: Vector3, hp: int):
		player_positions[player_id] = pos
		player_rotations[player_id] = rot
		player_velocities[player_id] = vel
		player_health[player_id] = hp
	
	func get_player_position(player_id: int) -> Vector3:
		return player_positions.get(player_id, Vector3.ZERO)
	
	func get_player_rotation(player_id: int) -> Vector3:
		return player_rotations.get(player_id, Vector3.ZERO)

## Player state snapshot for individual tracking
class PlayerStateSnapshot:
	var tick: int
	var timestamp: float
	var position: Vector3
	var rotation: Vector3
	var velocity: Vector3
	var health: int
	var weapon_state: Dictionary
	var movement_flags: int  # Bitfield for movement state
	
	func _init(t: int, ts: float, pos: Vector3, rot: Vector3, vel: Vector3, hp: int):
		tick = t
		timestamp = ts
		position = pos
		rotation = rot
		velocity = vel
		health = hp
		weapon_state = {}
		movement_flags = 0

## Shot snapshot for hit validation
class ShotSnapshot:
	var shot_id: String
	var shooter_id: int
	var tick: int
	var timestamp: float
	var origin: Vector3
	var direction: Vector3
	var weapon_type: String
	var damage: float
	var max_range: float
	var hit_results: Array[HitResult] = []
	var validated: bool = false
	
	func _init(id: String, shooter: int, t: int, ts: float, orig: Vector3, dir: Vector3):
		shot_id = id
		shooter_id = shooter
		tick = t
		timestamp = ts
		origin = orig
		direction = dir

## Hit result for shot validation
class HitResult:
	var target_id: int
	var hit_position: Vector3
	var hit_normal: Vector3
	var damage_dealt: float
	var hit_confirmed: bool = false
	
	func _init(target: int, pos: Vector3, normal: Vector3, damage: float):
		target_id = target
		hit_position = pos
		hit_normal = normal
		damage_dealt = damage

## Projectile state for prediction
class ProjectileState:
	var projectile_id: String
	var owner_id: int
	var position: Vector3
	var velocity: Vector3
	var spawn_time: float
	var lifetime: float
	
	func _init(id: String, owner: int, pos: Vector3, vel: Vector3, spawn: float, life: float):
		projectile_id = id
		owner_id = owner
		position = pos
		velocity = vel
		spawn_time = spawn
		lifetime = life

func _ready():
	# Initialize lag compensation system
	_setup_history_buffers()
	_connect_network_signals()
	
	print("⏱️ Lag Compensation system initialized")

func _setup_history_buffers():
	"""Initialize history buffers for rollback"""
	world_state_history.clear()
	player_state_history.clear()
	shot_history.clear()

func _connect_network_signals():
	"""Connect to network manager signals"""
	var network_manager = get_node("/root/NetworkManager")
	if network_manager:
		network_manager.player_joined.connect(_on_player_joined)
		network_manager.player_left.connect(_on_player_left)

func _process(delta):
	# Clean up old history data
	_cleanup_old_history()
	
	# Process pending shot validations
	_process_shot_validations()
	
	# Update interpolation for remote players
	_update_player_interpolation(delta)

## History Management

func record_world_state(tick: int, timestamp: float):
	"""Record current world state for rollback"""
	var snapshot = WorldStateSnapshot.new(tick, timestamp)
	
	# Record all player states
	var players = get_tree().get_nodes_in_group("network_players")
	for player in players:
		if player.has_method("get_network_data"):
			var data = player.get_network_data()
			snapshot.add_player_state(
				data.player_id,
				data.position,
				data.rotation,
				data.velocity,
				data.health
			)
	
	# Record projectiles
	var projectiles = get_tree().get_nodes_in_group("projectiles")
	for projectile in projectiles:
		if projectile.has_method("get_projectile_state"):
			var state = projectile.get_projectile_state()
			snapshot.projectiles.append(state)
	
	world_state_history.append(snapshot)
	
	# Limit history size
	if world_state_history.size() > interpolation_buffer_size:
		world_state_history.pop_front()

func record_player_state(player_id: int, tick: int, timestamp: float, position: Vector3, rotation: Vector3, velocity: Vector3, health: int):
	"""Record individual player state"""
	if not player_state_history.has(player_id):
		player_state_history[player_id] = []
	
	var snapshot = PlayerStateSnapshot.new(tick, timestamp, position, rotation, velocity, health)
	player_state_history[player_id].append(snapshot)
	
	# Limit history size per player
	if player_state_history[player_id].size() > interpolation_buffer_size:
		player_state_history[player_id].pop_front()

func _cleanup_old_history():
	"""Remove old history data beyond rollback time"""
	var current_time = Time.get_time_dict_from_system()["unix"]
	var cutoff_time = current_time - max_rollback_time
	
	# Clean world state history
	while world_state_history.size() > 0 and world_state_history[0].timestamp < cutoff_time:
		world_state_history.pop_front()
	
	# Clean player state history
	for player_id in player_state_history.keys():
		var history = player_state_history[player_id]
		while history.size() > 0 and history[0].timestamp < cutoff_time:
			history.pop_front()
	
	# Clean shot history
	while shot_history.size() > 0 and shot_history[0].timestamp < cutoff_time:
		shot_history.pop_front()

## Hit Registration and Validation

func register_shot(shooter_id: int, origin: Vector3, direction: Vector3, weapon_data: Dictionary) -> String:
	"""Register a shot for lag compensation"""
	var shot_id = _generate_shot_id()
	var current_time = Time.get_time_dict_from_system()["unix"]
	var current_tick = _get_current_tick()
	
	var shot = ShotSnapshot.new(shot_id, shooter_id, current_tick, current_time, origin, direction)
	shot.weapon_type = weapon_data.get("weapon_type", "hitscan")
	shot.damage = weapon_data.get("damage", 25.0)
	shot.max_range = weapon_data.get("max_range", 500.0)
	
	shot_history.append(shot)
	
	# Perform immediate hit detection with rollback
	if hit_validation_enabled:
		_validate_shot_with_rollback(shot)
	
	return shot_id

func _validate_shot_with_rollback(shot: ShotSnapshot):
	"""Validate shot using rollback to shooter's timestamp"""
	var validation_start_time = Time.get_time_dict_from_system()["unix"]
	
	# Calculate rollback time based on shooter's latency
	var shooter_latency = _get_player_latency(shot.shooter_id)
	var rollback_time = min(shooter_latency, max_rollback_time)
	var target_timestamp = shot.timestamp - rollback_time
	
	# Find world state at target time
	var rollback_state = _find_world_state_at_time(target_timestamp)
	if not rollback_state:
		print("⚠️ No rollback state found for timestamp: %.3f" % target_timestamp)
		return
	
	rollback_in_progress = true
	rollback_count += 1
	
	# Perform hit detection in rolled-back world
	var hit_results = _perform_hit_detection(shot, rollback_state)
	
	# Apply hit results if valid
	for hit_result in hit_results:
		if _validate_hit_result(hit_result, rollback_state):
			hit_result.hit_confirmed = true
			_apply_hit_damage(hit_result)
	
	shot.hit_results = hit_results
	shot.validated = true
	
	rollback_in_progress = false
	
	# Update performance metrics
	var validation_time = Time.get_time_dict_from_system()["unix"] - validation_start_time
	hit_validation_time = (hit_validation_time + validation_time) / 2.0
	
	# Emit validation result
	var hit_confirmed = hit_results.any(func(result): return result.hit_confirmed)
	shot_validated.emit(shot.shooter_id, hit_confirmed)
	
	rollback_performed.emit(shot.tick, rollback_time * 1000.0)

func _perform_hit_detection(shot: ShotSnapshot, world_state: WorldStateSnapshot) -> Array[HitResult]:
	"""Perform hit detection using rolled-back world state"""
	var hit_results: Array[HitResult] = []
	
	match shot.weapon_type:
		"hitscan":
			hit_results = _perform_hitscan_detection(shot, world_state)
		"projectile":
			hit_results = _perform_projectile_detection(shot, world_state)
		"beam":
			hit_results = _perform_beam_detection(shot, world_state)
	
	return hit_results

func _perform_hitscan_detection(shot: ShotSnapshot, world_state: WorldStateSnapshot) -> Array[HitResult]:
	"""Perform hitscan hit detection"""
	var hit_results: Array[HitResult] = []
	var ray_end = shot.origin + shot.direction * shot.max_range
	
	# Check hits against all players in rolled-back state
	for player_id in world_state.player_positions.keys():
		if player_id == shot.shooter_id:
			continue  # Don't hit yourself
		
		var player_pos = world_state.get_player_position(player_id)
		var player_rot = world_state.get_player_rotation(player_id)
		
		# Create player hitbox at rolled-back position
		var hitbox = _create_player_hitbox(player_pos, player_rot)
		
		# Perform ray-box intersection
		var hit_point = _ray_intersects_box(shot.origin, shot.direction, hitbox)
		if hit_point != Vector3.ZERO:
			var hit_normal = (hit_point - player_pos).normalized()
			var distance = shot.origin.distance_to(hit_point)
			var damage = _calculate_damage_with_falloff(shot.damage, distance)
			
			var hit_result = HitResult.new(player_id, hit_point, hit_normal, damage)
			hit_results.append(hit_result)
	
	return hit_results

func _perform_projectile_detection(shot: ShotSnapshot, world_state: WorldStateSnapshot) -> Array[HitResult]:
	"""Perform projectile hit detection with prediction"""
	var hit_results: Array[HitResult] = []
	
	# This would simulate projectile trajectory and predict hits
	# Implementation would depend on projectile physics
	
	return hit_results

func _perform_beam_detection(shot: ShotSnapshot, world_state: WorldStateSnapshot) -> Array[HitResult]:
	"""Perform beam weapon hit detection"""
	var hit_results: Array[HitResult] = []
	
	# This would handle continuous beam weapons
	# Implementation would track beam duration and continuous damage
	
	return hit_results

func _validate_hit_result(hit_result: HitResult, world_state: WorldStateSnapshot) -> bool:
	"""Validate hit result for anti-cheat"""
	# Check if target player was actually at the hit position
	var target_pos = world_state.get_player_position(hit_result.target_id)
	var distance_error = target_pos.distance_to(hit_result.hit_position)
	
	if distance_error > hit_scan_tolerance:
		print("⚠️ Hit validation failed: position error %.2fm" % distance_error)
		return false
	
	# Additional validation checks would go here
	# - Line of sight validation
	# - Weapon range validation
	# - Rate of fire validation
	
	return true

func _apply_hit_damage(hit_result: HitResult):
	"""Apply validated hit damage to target"""
	var target_player = _get_player_by_id(hit_result.target_id)
	if target_player and target_player.has_method("take_damage"):
		target_player.take_damage(hit_result.damage_dealt)
		print("💥 Hit confirmed: Player %d took %.1f damage" % [hit_result.target_id, hit_result.damage_dealt])

## Interpolation and Prediction

func _update_player_interpolation(delta: float):
	"""Update interpolation for remote players"""
	var current_time = Time.get_time_dict_from_system()["unix"]
	var interpolation_time = current_time - interpolation_delay
	
	for player_id in player_state_history.keys():
		var player = _get_player_by_id(player_id)
		if not player:
			continue
		
		var history = player_state_history[player_id]
		if history.size() < 2:
			continue
		
		# Find interpolation points
		var from_state = null
		var to_state = null
		
		for i in range(history.size() - 1):
			if history[i].timestamp <= interpolation_time and history[i + 1].timestamp >= interpolation_time:
				from_state = history[i]
				to_state = history[i + 1]
				break
		
		if from_state and to_state:
			# Interpolate between states
			var t = (interpolation_time - from_state.timestamp) / (to_state.timestamp - from_state.timestamp)
			t = clamp(t, 0.0, 1.0)
			
			var interpolated_pos = from_state.position.lerp(to_state.position, t)
			var interpolated_rot = from_state.rotation.lerp(to_state.rotation, t)
			
			# Apply interpolated state
			if player.has_method("set_interpolated_state"):
				player.set_interpolated_state(interpolated_pos, interpolated_rot)

func predict_player_position(player_id: int, future_time: float) -> Vector3:
	"""Predict player position at future time"""
	if not player_state_history.has(player_id):
		return Vector3.ZERO
	
	var history = player_state_history[player_id]
	if history.size() == 0:
		return Vector3.ZERO
	
	var latest_state = history[-1]
	var time_delta = future_time - latest_state.timestamp
	
	# Limit extrapolation time
	if time_delta > extrapolation_limit:
		time_delta = extrapolation_limit
	
	# Simple linear prediction based on velocity
	return latest_state.position + latest_state.velocity * time_delta

## Utility Functions

func _find_world_state_at_time(timestamp: float) -> WorldStateSnapshot:
	"""Find world state closest to target timestamp"""
	var closest_state = null
	var closest_distance = INF
	
	for state in world_state_history:
		var distance = abs(state.timestamp - timestamp)
		if distance < closest_distance:
			closest_distance = distance
			closest_state = state
	
	return closest_state

func _get_current_tick() -> int:
	"""Get current server tick"""
	var network_manager = get_node("/root/NetworkManager")
	if network_manager:
		return network_manager.server_tick
	return 0

func _get_player_latency(player_id: int) -> float:
	"""Get player's current latency"""
	# This would query the network manager for player latency
	return 0.050  # Default 50ms

func _generate_shot_id() -> String:
	"""Generate unique shot ID"""
	return "shot_" + str(Time.get_time_dict_from_system()["unix"]) + "_" + str(randi())

func _create_player_hitbox(position: Vector3, rotation: Vector3) -> AABB:
	"""Create player hitbox at position"""
	var size = Vector3(0.6, 1.8, 0.6)  # Player dimensions
	return AABB(position - size / 2, size)

func _ray_intersects_box(origin: Vector3, direction: Vector3, box: AABB) -> Vector3:
	"""Check if ray intersects with AABB"""
	# Simplified ray-box intersection
	# Full implementation would use proper ray-AABB intersection algorithm
	if box.has_point(origin + direction * 10.0):  # Simplified check
		return origin + direction * 10.0
	return Vector3.ZERO

func _calculate_damage_with_falloff(base_damage: float, distance: float) -> float:
	"""Calculate damage with distance falloff"""
	var falloff_start = 50.0
	var falloff_end = 200.0
	
	if distance <= falloff_start:
		return base_damage
	elif distance >= falloff_end:
		return base_damage * 0.3
	else:
		var falloff_factor = (distance - falloff_start) / (falloff_end - falloff_start)
		return lerp(base_damage, base_damage * 0.3, falloff_factor)

func _get_player_by_id(player_id: int) -> Node:
	"""Get player node by ID"""
	var players = get_tree().get_nodes_in_group("network_players")
	for player in players:
		if player.has_method("get_player_id") and player.get_player_id() == player_id:
			return player
	return null

func _process_shot_validations():
	"""Process pending shot validations"""
	var current_time = Time.get_time_dict_from_system()["unix"]
	
	for shot in shot_history:
		if not shot.validated and (current_time - shot.timestamp) > hit_confirmation_timeout:
			# Timeout validation
			shot.validated = true
			shot_validated.emit(shot.shooter_id, false)

## Event Handlers

func _on_player_joined(player_id: int, player_data: Dictionary):
	"""Handle player joined event"""
	player_state_history[player_id] = []

func _on_player_left(player_id: int):
	"""Handle player left event"""
	player_state_history.erase(player_id)

## Public API

func get_lag_compensation_stats() -> Dictionary:
	"""Get lag compensation statistics"""
	return {
		"rollback_count": rollback_count,
		"average_rollback_time": average_rollback_time,
		"hit_validation_time": hit_validation_time,
		"world_state_history_size": world_state_history.size(),
		"shot_history_size": shot_history.size(),
		"interpolation_delay": interpolation_delay,
		"max_rollback_time": max_rollback_time
	}

func set_interpolation_delay(delay: float):
	"""Set interpolation delay"""
	interpolation_delay = clamp(delay, 0.050, 0.200)  # 50-200ms range

func enable_hit_validation(enabled: bool):
	"""Enable/disable hit validation"""
	hit_validation_enabled = enabled

func enable_prediction(enabled: bool):
	"""Enable/disable client prediction"""
	prediction_enabled = enabled
