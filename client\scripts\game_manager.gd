# Galaxy Guns 3D - Game Manager
# Central game coordination and state management

extends Node3D

## Game state
enum GameState {
	MENU,
	PLAYING,
	PAUSED,
	GAME_OVER
}

var current_state: GameState = GameState.PLAYING
var player: PlayerController
var ui_manager: Control
var enemy_spawner: Node3D

## Enemy spawning
@export var max_enemies: int = 5
@export var spawn_distance: float = 30.0
@export var spawn_interval: float = 5.0

var current_enemies: Array[EnemyAI] = []
var spawn_timer: float = 0.0
var enemy_scene = preload("res://scenes/enemy.tscn")

func _ready():
	print("🎮 Game Manager initialized")
	
	# Find references
	player = get_node("Player") as PlayerController
	ui_manager = get_node("UI") as Control
	enemy_spawner = get_node("EnemySpawner")
	
	# Connect player signals
	if player:
		player.player_died.connect(_on_player_died)
		print("🔗 Connected to player")
	
	# Connect UI to player
	if ui_manager and player:
		_connect_ui_to_player()
	
	# Start spawning enemies
	_spawn_initial_enemies()

func _connect_ui_to_player():
	"""Connect UI input to player controller"""
	if ui_manager.has_signal("movement_input"):
		ui_manager.movement_input.connect(player._on_movement_input)
	if ui_manager.has_signal("look_input"):
		ui_manager.look_input.connect(player._on_look_input)
	if ui_manager.has_signal("fire_pressed"):
		ui_manager.fire_pressed.connect(player._on_fire_pressed)
	if ui_manager.has_signal("fire_released"):
		ui_manager.fire_released.connect(player._on_fire_released)
	if ui_manager.has_signal("reload_pressed"):
		ui_manager.reload_pressed.connect(player._on_reload_pressed)
	if ui_manager.has_signal("jump_pressed"):
		ui_manager.jump_pressed.connect(player._on_jump_pressed)
	
	print("🔗 UI connected to player input")

func _process(delta):
	if current_state == GameState.PLAYING:
		_update_enemy_spawning(delta)
		_cleanup_dead_enemies()

func _update_enemy_spawning(delta):
	"""Handle enemy spawning"""
	spawn_timer += delta
	
	if spawn_timer >= spawn_interval and current_enemies.size() < max_enemies:
		_spawn_enemy()
		spawn_timer = 0.0

func _spawn_enemy():
	"""Spawn a new enemy"""
	if not player:
		return
	
	var enemy = enemy_scene.instantiate() as EnemyAI
	enemy_spawner.add_child(enemy)
	
	# Position enemy around player
	var spawn_angle = randf() * TAU
	var spawn_pos = player.global_position + Vector3(
		cos(spawn_angle) * spawn_distance,
		0,
		sin(spawn_angle) * spawn_distance
	)
	enemy.global_position = spawn_pos
	
	# Connect enemy signals
	enemy.enemy_died.connect(_on_enemy_died)
	
	current_enemies.append(enemy)
	print("👾 Spawned enemy at %s" % spawn_pos)

func _spawn_initial_enemies():
	"""Spawn initial set of enemies"""
	for i in range(3):
		_spawn_enemy()

func _cleanup_dead_enemies():
	"""Remove dead enemies from tracking"""
	current_enemies = current_enemies.filter(func(enemy): return is_instance_valid(enemy))

func _on_enemy_died(enemy: EnemyAI):
	"""Handle enemy death"""
	current_enemies.erase(enemy)
	print("💀 Enemy died, %d remaining" % current_enemies.size())
	
	# Remove enemy after death animation
	get_tree().create_timer(2.0).timeout.connect(func(): 
		if is_instance_valid(enemy):
			enemy.queue_free()
	)

func _on_player_died():
	"""Handle player death"""
	print("💀 Player died - Game Over")
	current_state = GameState.GAME_OVER
	
	# Show game over screen after delay
	get_tree().create_timer(3.0).timeout.connect(_show_game_over)

func _show_game_over():
	"""Show game over screen"""
	print("🔄 Returning to main menu...")
	get_tree().change_scene_to_file("res://scenes/main_menu.tscn")

func restart_game():
	"""Restart the current game"""
	get_tree().reload_current_scene()

func pause_game():
	"""Pause the game"""
	if current_state == GameState.PLAYING:
		current_state = GameState.PAUSED
		get_tree().paused = true

func resume_game():
	"""Resume the game"""
	if current_state == GameState.PAUSED:
		current_state = GameState.PLAYING
		get_tree().paused = false
