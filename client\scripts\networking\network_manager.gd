# Galaxy Guns 3D - Network Manager
# Client-side networking with prediction, lag compensation, and anti-cheat
# Optimized for mobile with 120ms max latency tolerance

class_name NetworkManager
extends Node

## Signals for network events
signal connected_to_server()
signal disconnected_from_server(reason: String)
signal player_joined(player_id: int, player_data: Dictionary)
signal player_left(player_id: int)
signal match_started()
signal match_ended(results: Dictionary)
signal network_error(error: String)

## Network configuration
@export_group("Connection Settings")
@export var server_address: String = "127.0.0.1"
@export var server_port: int = 7777
@export var max_players: int = 8
@export var connection_timeout: float = 10.0
@export var heartbeat_interval: float = 1.0

@export_group("Performance")
@export var tick_rate: int = 60  # Server simulation rate
@export var send_rate: int = 30  # Client update rate
@export var max_latency: float = 0.120  # 120ms max acceptable latency
@export var prediction_buffer_size: int = 64

## Network state
var multiplayer_peer: MultiplayerPeer
var is_server: bool = false
var is_connected: bool = false
var local_player_id: int = 0
var server_tick: int = 0
var client_tick: int = 0

## Player management
var players: Dictionary = {}  # player_id -> PlayerNetworkData
var local_player: PlayerController
var network_players: Dictionary = {}  # player_id -> NetworkPlayer

## Lag compensation
var input_buffer: Array[InputSnapshot] = []
var state_buffer: Array[GameStateSnapshot] = []
var prediction_enabled: bool = true
var reconciliation_enabled: bool = true

## Anti-cheat
var movement_validator: MovementValidator
var shot_validator: ShotValidator
var cheat_detection_enabled: bool = true

## Performance tracking
var network_stats: NetworkStats
var last_ping_time: float = 0.0
var ping_samples: Array[float] = []

## Input snapshot for client-side prediction
class InputSnapshot:
	var tick: int
	var timestamp: float
	var movement_input: Vector2
	var look_input: Vector2
	var actions: Dictionary  # fire, reload, jump, etc.
	var sequence_number: int
	
	func _init(t: int, ts: float, move: Vector2, look: Vector2, acts: Dictionary, seq: int):
		tick = t
		timestamp = ts
		movement_input = move
		look_input = look
		actions = acts
		sequence_number = seq

## Game state snapshot for reconciliation
class GameStateSnapshot:
	var tick: int
	var timestamp: float
	var player_positions: Dictionary
	var player_rotations: Dictionary
	var player_velocities: Dictionary
	var player_health: Dictionary
	var weapon_states: Dictionary
	
	func _init(t: int, ts: float):
		tick = t
		timestamp = ts
		player_positions = {}
		player_rotations = {}
		player_velocities = {}
		player_health = {}
		weapon_states = {}

## Network statistics tracking
class NetworkStats:
	var ping: float = 0.0
	var jitter: float = 0.0
	var packet_loss: float = 0.0
	var bytes_sent: int = 0
	var bytes_received: int = 0
	var packets_sent: int = 0
	var packets_received: int = 0
	var last_update_time: float = 0.0
	
	func update_ping(new_ping: float):
		ping = new_ping
		# Calculate jitter (ping variance)
		# Implementation would track ping history

func _ready():
	# Initialize networking systems
	_setup_multiplayer()
	_initialize_validators()
	_setup_network_stats()
	
	# Connect to game systems
	_connect_game_signals()
	
	print("🌐 Network Manager initialized")

func _setup_multiplayer():
	"""Initialize multiplayer system"""
	multiplayer.peer_connected.connect(_on_peer_connected)
	multiplayer.peer_disconnected.connect(_on_peer_disconnected)
	multiplayer.connection_failed.connect(_on_connection_failed)
	multiplayer.connected_to_server.connect(_on_connected_to_server)
	multiplayer.server_disconnected.connect(_on_server_disconnected)

func _initialize_validators():
	"""Initialize anti-cheat validators"""
	movement_validator = MovementValidator.new()
	shot_validator = ShotValidator.new()
	add_child(movement_validator)
	add_child(shot_validator)

func _setup_network_stats():
	"""Initialize network statistics tracking"""
	network_stats = NetworkStats.new()

func _connect_game_signals():
	"""Connect to game system signals"""
	# Find local player
	local_player = get_tree().get_first_node_in_group("player")
	if local_player:
		local_player.movement_state_changed.connect(_on_player_movement_changed)
		local_player.weapon_changed.connect(_on_player_weapon_changed)

func _process(delta):
	if not is_connected:
		return
	
	# Update network statistics
	_update_network_stats(delta)
	
	# Send client updates at configured rate
	if not is_server:
		_send_client_update()
	
	# Process prediction and reconciliation
	if prediction_enabled:
		_process_client_prediction(delta)

## Connection Management

func connect_to_server(address: String = "", port: int = 0) -> bool:
	"""Connect to game server"""
	if is_connected:
		disconnect_from_server()
	
	if address != "":
		server_address = address
	if port > 0:
		server_port = port
	
	print("🔌 Connecting to server: %s:%d" % [server_address, server_port])
	
	# Create WebSocket peer for cross-platform compatibility
	var peer = WebSocketMultiplayerPeer.new()
	var url = "ws://%s:%d" % [server_address, server_port]
	
	var error = peer.create_client(url)
	if error != OK:
		network_error.emit("Failed to create client: " + str(error))
		return false
	
	multiplayer.multiplayer_peer = peer
	multiplayer_peer = peer
	
	# Start connection timeout
	get_tree().create_timer(connection_timeout).timeout.connect(_on_connection_timeout)
	
	return true

func disconnect_from_server():
	"""Disconnect from server"""
	if multiplayer_peer:
		multiplayer_peer.close()
		multiplayer_peer = null
	
	is_connected = false
	players.clear()
	network_players.clear()
	
	disconnected_from_server.emit("Manual disconnect")

func start_server(port: int = 0) -> bool:
	"""Start dedicated server"""
	if port > 0:
		server_port = port
	
	print("🖥️ Starting server on port: %d" % server_port)
	
	var peer = WebSocketMultiplayerPeer.new()
	var error = peer.create_server(server_port)
	
	if error != OK:
		network_error.emit("Failed to start server: " + str(error))
		return false
	
	multiplayer.multiplayer_peer = peer
	multiplayer_peer = peer
	is_server = true
	is_connected = true
	
	return true

## Input Processing

func send_input(movement: Vector2, look: Vector2, actions: Dictionary):
	"""Send input to server with client-side prediction"""
	if not is_connected or is_server:
		return
	
	client_tick += 1
	var timestamp = Time.get_time_dict_from_system()["unix"]
	
	# Create input snapshot
	var input_snapshot = InputSnapshot.new(
		client_tick,
		timestamp,
		movement,
		look,
		actions,
		input_buffer.size()
	)
	
	# Store for prediction
	input_buffer.append(input_snapshot)
	
	# Limit buffer size
	if input_buffer.size() > prediction_buffer_size:
		input_buffer.pop_front()
	
	# Send to server
	_send_input_to_server.rpc_unreliable(input_snapshot)
	
	# Apply client-side prediction
	if prediction_enabled and local_player:
		_apply_input_prediction(input_snapshot)

@rpc("any_peer", "unreliable")
func _send_input_to_server(input_data: InputSnapshot):
	"""Receive input from client (server-side)"""
	if not is_server:
		return
	
	var sender_id = multiplayer.get_remote_sender_id()
	
	# Validate input
	if cheat_detection_enabled:
		if not movement_validator.validate_input(sender_id, input_data):
			print("⚠️ Invalid input from player %d" % sender_id)
			return
	
	# Process input on server
	_process_server_input(sender_id, input_data)

func _apply_input_prediction(input_snapshot: InputSnapshot):
	"""Apply client-side prediction"""
	if not local_player:
		return
	
	# Store current state for reconciliation
	var state_snapshot = GameStateSnapshot.new(client_tick, input_snapshot.timestamp)
	state_snapshot.player_positions[local_player_id] = local_player.global_position
	state_snapshot.player_rotations[local_player_id] = local_player.rotation
	state_snapshot.player_velocities[local_player_id] = local_player.velocity
	
	state_buffer.append(state_snapshot)
	
	# Limit buffer size
	if state_buffer.size() > prediction_buffer_size:
		state_buffer.pop_front()
	
	# Apply predicted movement
	# This would integrate with the player controller
	# local_player.apply_predicted_input(input_snapshot)

## Server State Updates

@rpc("authority", "unreliable")
func receive_server_update(server_state: Dictionary):
	"""Receive authoritative state from server"""
	if is_server:
		return
	
	server_tick = server_state.get("tick", 0)
	var timestamp = server_state.get("timestamp", 0.0)
	
	# Update player positions
	var player_states = server_state.get("players", {})
	for player_id in player_states.keys():
		_update_network_player(player_id, player_states[player_id])
	
	# Perform reconciliation if enabled
	if reconciliation_enabled:
		_perform_reconciliation(server_state)

func _update_network_player(player_id: int, player_state: Dictionary):
	"""Update network player state"""
	if player_id == local_player_id:
		# Handle local player reconciliation
		_reconcile_local_player(player_state)
	else:
		# Update remote player
		if network_players.has(player_id):
			var network_player = network_players[player_id]
			network_player.update_from_server(player_state)

func _reconcile_local_player(server_state: Dictionary):
	"""Reconcile local player with server state"""
	if not local_player or not reconciliation_enabled:
		return
	
	var server_position = Vector3(
		server_state.get("x", 0),
		server_state.get("y", 0),
		server_state.get("z", 0)
	)
	
	var server_tick_received = server_state.get("tick", 0)
	
	# Find corresponding client state
	var client_state = null
	for state in state_buffer:
		if state.tick == server_tick_received:
			client_state = state
			break
	
	if not client_state:
		return
	
	# Check for significant discrepancy
	var position_error = server_position.distance_to(
		client_state.player_positions.get(local_player_id, Vector3.ZERO)
	)
	
	if position_error > 0.5:  # 50cm tolerance
		print("🔄 Reconciling position error: %.2fm" % position_error)
		
		# Snap to server position
		local_player.global_position = server_position
		
		# Re-apply inputs after server state
		_replay_inputs_after_tick(server_tick_received)

func _replay_inputs_after_tick(server_tick: int):
	"""Replay client inputs after server reconciliation"""
	for input_snapshot in input_buffer:
		if input_snapshot.tick > server_tick:
			_apply_input_prediction(input_snapshot)

## Network Statistics

func _update_network_stats(delta):
	"""Update network performance statistics"""
	network_stats.last_update_time += delta
	
	# Send ping every second
	if network_stats.last_update_time >= 1.0:
		network_stats.last_update_time = 0.0
		_send_ping()

func _send_ping():
	"""Send ping to measure latency"""
	if not is_connected or is_server:
		return
	
	last_ping_time = Time.get_time_dict_from_system()["unix"]
	_ping_request.rpc_unreliable(last_ping_time)

@rpc("any_peer", "unreliable")
func _ping_request(timestamp: float):
	"""Handle ping request (server-side)"""
	if not is_server:
		return
	
	var sender_id = multiplayer.get_remote_sender_id()
	_ping_response.rpc_id(sender_id, timestamp)

@rpc("authority", "unreliable")
func _ping_response(timestamp: float):
	"""Handle ping response (client-side)"""
	if is_server:
		return
	
	var current_time = Time.get_time_dict_from_system()["unix"]
	var ping = (current_time - timestamp) * 1000.0  # Convert to milliseconds
	
	network_stats.update_ping(ping)
	ping_samples.append(ping)
	
	# Keep only recent samples
	if ping_samples.size() > 10:
		ping_samples.pop_front()

## Client Update Sending

func _send_client_update():
	"""Send regular client updates to server"""
	if not local_player:
		return
	
	var update_data = {
		"tick": client_tick,
		"timestamp": Time.get_time_dict_from_system()["unix"],
		"position": {
			"x": local_player.global_position.x,
			"y": local_player.global_position.y,
			"z": local_player.global_position.z
		},
		"rotation": {
			"x": local_player.rotation.x,
			"y": local_player.rotation.y,
			"z": local_player.rotation.z
		},
		"velocity": {
			"x": local_player.velocity.x,
			"y": local_player.velocity.y,
			"z": local_player.velocity.z
		}
	}
	
	_client_update.rpc_unreliable(update_data)

@rpc("any_peer", "unreliable")
func _client_update(update_data: Dictionary):
	"""Receive client update (server-side)"""
	if not is_server:
		return
	
	var sender_id = multiplayer.get_remote_sender_id()
	
	# Validate update
	if cheat_detection_enabled:
		if not movement_validator.validate_movement(sender_id, update_data):
			print("⚠️ Invalid movement from player %d" % sender_id)
			return
	
	# Update player data
	if players.has(sender_id):
		players[sender_id].update_from_client(update_data)

## Connection Event Handlers

func _on_peer_connected(id: int):
	"""Handle peer connection"""
	print("👤 Player connected: %d" % id)
	
	if is_server:
		# Create player data
		var player_data = PlayerNetworkData.new(id)
		players[id] = player_data
		
		# Notify all clients
		_player_joined.rpc(id, player_data.to_dict())

func _on_peer_disconnected(id: int):
	"""Handle peer disconnection"""
	print("👋 Player disconnected: %d" % id)
	
	if is_server:
		players.erase(id)
		_player_left.rpc(id)
	else:
		network_players.erase(id)
	
	player_left.emit(id)

func _on_connection_failed():
	"""Handle connection failure"""
	network_error.emit("Connection failed")

func _on_connected_to_server():
	"""Handle successful server connection"""
	is_connected = true
	local_player_id = multiplayer.get_unique_id()
	connected_to_server.emit()
	print("✅ Connected to server as player %d" % local_player_id)

func _on_server_disconnected():
	"""Handle server disconnection"""
	is_connected = false
	disconnected_from_server.emit("Server disconnected")

func _on_connection_timeout():
	"""Handle connection timeout"""
	if not is_connected:
		network_error.emit("Connection timeout")
		disconnect_from_server()

## RPC Handlers

@rpc("authority", "reliable")
func _player_joined(player_id: int, player_data: Dictionary):
	"""Handle player joined notification"""
	print("👤 Player %d joined the game" % player_id)
	
	# Create network player representation
	var network_player = NetworkPlayer.new(player_id, player_data)
	network_players[player_id] = network_player
	
	player_joined.emit(player_id, player_data)

@rpc("authority", "reliable")
func _player_left(player_id: int):
	"""Handle player left notification"""
	print("👋 Player %d left the game" % player_id)
	
	if network_players.has(player_id):
		network_players[player_id].queue_free()
		network_players.erase(player_id)

## Public API

func get_ping() -> float:
	"""Get current ping in milliseconds"""
	return network_stats.ping

func get_player_count() -> int:
	"""Get current player count"""
	return players.size() if is_server else network_players.size() + 1

func is_host() -> bool:
	"""Check if this client is the host"""
	return is_server

func get_network_stats() -> Dictionary:
	"""Get network statistics"""
	return {
		"ping": network_stats.ping,
		"jitter": network_stats.jitter,
		"packet_loss": network_stats.packet_loss,
		"connected_players": get_player_count(),
		"server_tick": server_tick,
		"client_tick": client_tick,
		"prediction_enabled": prediction_enabled,
		"reconciliation_enabled": reconciliation_enabled
	}

## Player Network Data Class

class PlayerNetworkData:
	var player_id: int
	var username: String
	var position: Vector3
	var rotation: Vector3
	var velocity: Vector3
	var health: int
	var last_update_time: float
	
	func _init(id: int):
		player_id = id
		username = "Player" + str(id)
		position = Vector3.ZERO
		rotation = Vector3.ZERO
		velocity = Vector3.ZERO
		health = 100
		last_update_time = Time.get_time_dict_from_system()["unix"]
	
	func update_from_client(data: Dictionary):
		var pos_data = data.get("position", {})
		position = Vector3(
			pos_data.get("x", 0),
			pos_data.get("y", 0),
			pos_data.get("z", 0)
		)
		
		var rot_data = data.get("rotation", {})
		rotation = Vector3(
			rot_data.get("x", 0),
			rot_data.get("y", 0),
			rot_data.get("z", 0)
		)
		
		var vel_data = data.get("velocity", {})
		velocity = Vector3(
			vel_data.get("x", 0),
			vel_data.get("y", 0),
			vel_data.get("z", 0)
		)
		
		last_update_time = Time.get_time_dict_from_system()["unix"]
	
	func to_dict() -> Dictionary:
		return {
			"player_id": player_id,
			"username": username,
			"position": {"x": position.x, "y": position.y, "z": position.z},
			"rotation": {"x": rotation.x, "y": rotation.y, "z": rotation.z},
			"velocity": {"x": velocity.x, "y": velocity.y, "z": velocity.z},
			"health": health
		}

## Network Player Representation

class NetworkPlayer extends Node3D:
	var player_id: int
	var username: String
	var interpolation_enabled: bool = true
	var target_position: Vector3
	var target_rotation: Vector3
	
	func _init(id: int, data: Dictionary):
		player_id = id
		username = data.get("username", "Player" + str(id))
		
		# Create visual representation
		# This would instantiate the player model/mesh
	
	func update_from_server(data: Dictionary):
		var pos_data = data.get("position", {})
		target_position = Vector3(
			pos_data.get("x", 0),
			pos_data.get("y", 0),
			pos_data.get("z", 0)
		)
		
		var rot_data = data.get("rotation", {})
		target_rotation = Vector3(
			rot_data.get("x", 0),
			rot_data.get("y", 0),
			rot_data.get("z", 0)
		)
	
	func _process(delta):
		if interpolation_enabled:
			# Smooth interpolation to target position
			global_position = global_position.lerp(target_position, 10.0 * delta)
			rotation = rotation.lerp(target_rotation, 10.0 * delta)
