# Galaxy Guns 3D - Performance Tuner
# Final performance optimization and polish system
# Ensures 60fps on iPhone 12 mini with maximum visual quality

class_name PerformanceTuner
extends Node

## Signals for performance events
signal performance_target_achieved(fps: float)
signal optimization_applied(optimization: String, impact: float)
signal quality_adjusted(quality_level: float)
signal benchmark_completed(results: Dictionary)

## Performance targets
@export_group("Performance Targets")
@export var target_fps: float = 60.0
@export var min_acceptable_fps: float = 55.0
@export var frame_time_budget_ms: float = 16.67  # 60fps
@export var gpu_budget_ms: float = 12.0
@export var cpu_budget_ms: float = 4.67

@export_group("Quality Settings")
@export var auto_quality_adjustment: bool = true
@export var quality_adjustment_sensitivity: float = 0.1
@export var min_quality_level: float = 0.3
@export var max_quality_level: float = 1.0

## System references
var metal_renderer: MetalRenderer
var battery_manager: BatteryManager
var network_manager: NetworkManager
var audio_manager: AdaptiveAudioManager

## Performance monitoring
var frame_time_samples: Array[float] = []
var gpu_time_samples: Array[float] = []
var cpu_time_samples: Array[float] = []
var memory_samples: Array[int] = []

## Quality control
var current_quality_level: float = 1.0
var quality_adjustment_cooldown: float = 0.0
var last_quality_adjustment: float = 0.0

## Optimization strategies
var active_optimizations: Dictionary = {}
var optimization_impact: Dictionary = {}

## Performance benchmarking
var benchmark_running: bool = false
var benchmark_duration: float = 10.0
var benchmark_start_time: float = 0.0
var benchmark_results: Dictionary = {}

func _ready():
	# Initialize performance tuning
	_initialize_system_references()
	_setup_performance_monitoring()
	_run_initial_benchmark()
	
	print("⚡ Performance Tuner initialized")

func _initialize_system_references():
	"""Initialize references to system components"""
	metal_renderer = get_node("/root/MetalRenderer")
	battery_manager = get_node("/root/BatteryManager")
	network_manager = get_node("/root/NetworkManager")
	audio_manager = get_node("/root/AdaptiveAudioManager")

func _setup_performance_monitoring():
	"""Setup performance monitoring timers"""
	# Performance monitoring timer
	var monitor_timer = Timer.new()
	monitor_timer.wait_time = 0.1  # 100ms intervals
	monitor_timer.timeout.connect(_update_performance_metrics)
	monitor_timer.autostart = true
	add_child(monitor_timer)
	
	# Quality adjustment timer
	var quality_timer = Timer.new()
	quality_timer.wait_time = 1.0  # 1 second intervals
	quality_timer.timeout.connect(_evaluate_quality_adjustment)
	quality_timer.autostart = true
	add_child(quality_timer)

func _run_initial_benchmark():
	"""Run initial performance benchmark"""
	await get_tree().create_timer(2.0).timeout  # Wait for systems to initialize
	start_benchmark()

func _process(delta):
	# Update performance samples
	_collect_performance_samples(delta)
	
	# Apply dynamic optimizations
	if auto_quality_adjustment:
		_apply_dynamic_optimizations(delta)
	
	# Update quality adjustment cooldown
	if quality_adjustment_cooldown > 0:
		quality_adjustment_cooldown -= delta

func _collect_performance_samples(delta: float):
	"""Collect performance metrics"""
	# Frame time
	var frame_time_ms = delta * 1000.0
	frame_time_samples.append(frame_time_ms)
	
	# GPU time (estimated from rendering stats)
	var gpu_time_ms = _estimate_gpu_time()
	gpu_time_samples.append(gpu_time_ms)
	
	# CPU time (estimated)
	var cpu_time_ms = frame_time_ms - gpu_time_ms
	cpu_time_samples.append(cpu_time_ms)
	
	# Memory usage
	var memory_mb = _get_memory_usage_mb()
	memory_samples.append(memory_mb)
	
	# Limit sample history
	var max_samples = 600  # 60 seconds at 10Hz
	if frame_time_samples.size() > max_samples:
		frame_time_samples.pop_front()
		gpu_time_samples.pop_front()
		cpu_time_samples.pop_front()
		memory_samples.pop_front()

func _estimate_gpu_time() -> float:
	"""Estimate GPU time from rendering statistics"""
	if not metal_renderer:
		return 8.0  # Default estimate
	
	var stats = metal_renderer.get_performance_stats()
	var base_gpu_time = 8.0
	
	# Adjust based on resolution scale
	base_gpu_time *= stats.resolution_scale * stats.resolution_scale
	
	# Adjust based on thermal throttling
	if stats.thermal_state >= 2:
		base_gpu_time *= 1.3  # Thermal throttling increases GPU time
	
	return base_gpu_time

func _get_memory_usage_mb() -> int:
	"""Get current memory usage in MB"""
	var memory_usage = OS.get_static_memory_usage_by_type()
	var total_memory = 0
	
	for usage in memory_usage.values():
		total_memory += usage
	
	return total_memory / (1024 * 1024)  # Convert to MB

func _update_performance_metrics():
	"""Update performance metrics and check targets"""
	if frame_time_samples.size() < 10:
		return
	
	# Calculate averages
	var avg_frame_time = _calculate_average(frame_time_samples.slice(-30))  # Last 3 seconds
	var avg_gpu_time = _calculate_average(gpu_time_samples.slice(-30))
	var avg_cpu_time = _calculate_average(cpu_time_samples.slice(-30))
	
	# Check performance targets
	var current_fps = 1000.0 / avg_frame_time
	
	if current_fps >= target_fps:
		performance_target_achieved.emit(current_fps)
	
	# Check if optimization is needed
	if avg_frame_time > frame_time_budget_ms * 1.1:  # 10% tolerance
		_trigger_performance_optimization(avg_frame_time, avg_gpu_time, avg_cpu_time)

func _calculate_average(samples: Array[float]) -> float:
	"""Calculate average of samples"""
	if samples.size() == 0:
		return 0.0
	
	var sum = 0.0
	for sample in samples:
		sum += sample
	
	return sum / samples.size()

func _trigger_performance_optimization(frame_time: float, gpu_time: float, cpu_time: float):
	"""Trigger performance optimization based on bottleneck"""
	if gpu_time > gpu_budget_ms:
		_optimize_gpu_performance()
	elif cpu_time > cpu_budget_ms:
		_optimize_cpu_performance()
	else:
		_optimize_general_performance()

func _optimize_gpu_performance():
	"""Optimize GPU performance"""
	var optimizations_applied = []
	
	# Reduce resolution scale
	if metal_renderer and current_quality_level > 0.5:
		var new_scale = metal_renderer.current_resolution_scale * 0.9
		metal_renderer._set_resolution_scale(new_scale)
		optimizations_applied.append("resolution_scale")
		optimization_applied.emit("resolution_scale", 15.0)
	
	# Reduce shadow quality
	if current_quality_level > 0.4:
		_reduce_shadow_quality()
		optimizations_applied.append("shadow_quality")
		optimization_applied.emit("shadow_quality", 10.0)
	
	# Reduce particle effects
	if current_quality_level > 0.3:
		_reduce_particle_effects()
		optimizations_applied.append("particle_effects")
		optimization_applied.emit("particle_effects", 8.0)
	
	# Update active optimizations
	for opt in optimizations_applied:
		active_optimizations[opt] = true

func _optimize_cpu_performance():
	"""Optimize CPU performance"""
	var optimizations_applied = []
	
	# Reduce AI update frequency
	_reduce_ai_update_frequency()
	optimizations_applied.append("ai_frequency")
	optimization_applied.emit("ai_frequency", 12.0)
	
	# Reduce physics simulation quality
	_reduce_physics_quality()
	optimizations_applied.append("physics_quality")
	optimization_applied.emit("physics_quality", 8.0)
	
	# Reduce audio processing
	if audio_manager:
		audio_manager.reduce_processing_quality()
		optimizations_applied.append("audio_processing")
		optimization_applied.emit("audio_processing", 5.0)
	
	# Update active optimizations
	for opt in optimizations_applied:
		active_optimizations[opt] = true

func _optimize_general_performance():
	"""Apply general performance optimizations"""
	# Reduce overall quality level
	var new_quality = max(min_quality_level, current_quality_level - 0.1)
	set_quality_level(new_quality)
	
	optimization_applied.emit("general_quality", 10.0)

func _reduce_shadow_quality():
	"""Reduce shadow rendering quality"""
	# Reduce shadow map resolution
	var shadow_resolution = 1024  # Reduced from 2048
	
	# Reduce shadow distance
	var shadow_distance = 75.0  # Reduced from 150
	
	print("🌑 Shadow quality reduced")

func _reduce_particle_effects():
	"""Reduce particle effect complexity"""
	# Reduce particle counts
	var particle_scale = 0.7
	
	# Disable some particle systems
	var particles = get_tree().get_nodes_in_group("particles")
	for particle in particles:
		if particle.has_method("set_emission_scale"):
			particle.set_emission_scale(particle_scale)
	
	print("✨ Particle effects reduced")

func _reduce_ai_update_frequency():
	"""Reduce AI update frequency"""
	var ai_enemies = get_tree().get_nodes_in_group("enemies")
	for enemy in ai_enemies:
		if enemy.has_method("set_update_frequency"):
			enemy.set_update_frequency(0.15)  # Reduced from 0.1
	
	print("🤖 AI update frequency reduced")

func _reduce_physics_quality():
	"""Reduce physics simulation quality"""
	# Reduce physics tick rate
	Engine.physics_ticks_per_second = 50  # Reduced from 60
	
	# Reduce collision detection precision
	print("⚙️ Physics quality reduced")

func _evaluate_quality_adjustment():
	"""Evaluate if quality adjustment is needed"""
	if not auto_quality_adjustment or quality_adjustment_cooldown > 0:
		return
	
	if frame_time_samples.size() < 30:
		return
	
	var avg_frame_time = _calculate_average(frame_time_samples.slice(-30))
	var current_fps = 1000.0 / avg_frame_time
	
	# Increase quality if performance is good
	if current_fps > target_fps * 1.05 and current_quality_level < max_quality_level:
		var new_quality = min(max_quality_level, current_quality_level + quality_adjustment_sensitivity)
		set_quality_level(new_quality)
		quality_adjustment_cooldown = 3.0  # 3 second cooldown
	
	# Decrease quality if performance is poor
	elif current_fps < min_acceptable_fps and current_quality_level > min_quality_level:
		var new_quality = max(min_quality_level, current_quality_level - quality_adjustment_sensitivity)
		set_quality_level(new_quality)
		quality_adjustment_cooldown = 2.0  # 2 second cooldown

func _apply_dynamic_optimizations(delta: float):
	"""Apply dynamic optimizations based on current conditions"""
	# Battery-based optimizations
	if battery_manager:
		var battery_stats = battery_manager.get_battery_stats()
		
		# Reduce quality on low battery
		if battery_stats.battery_level < 0.2 and current_quality_level > 0.5:
			set_quality_level(0.5)
		
		# Thermal throttling
		if battery_stats.thermal_state >= 2:
			_apply_thermal_optimizations()

func _apply_thermal_optimizations():
	"""Apply optimizations for thermal throttling"""
	# Aggressive quality reduction
	var thermal_quality = max(0.3, current_quality_level - 0.3)
	set_quality_level(thermal_quality)
	
	# Reduce frame rate target
	Engine.max_fps = 30
	
	print("🌡️ Thermal optimizations applied")

## Public API

func set_quality_level(quality: float):
	"""Set overall quality level"""
	quality = clamp(quality, min_quality_level, max_quality_level)
	
	if abs(quality - current_quality_level) < 0.05:
		return  # No significant change
	
	current_quality_level = quality
	_apply_quality_settings(quality)
	
	quality_adjusted.emit(quality)
	print("🎨 Quality level set to: %.2f" % quality)

func _apply_quality_settings(quality: float):
	"""Apply quality settings to all systems"""
	# Rendering quality
	if metal_renderer:
		if quality >= 0.8:
			metal_renderer.set_quality_preset("high")
		elif quality >= 0.6:
			metal_renderer.set_quality_preset("medium")
		else:
			metal_renderer.set_quality_preset("low")
	
	# Audio quality
	if audio_manager:
		var audio_quality = int(quality * 4.0)  # 0-4 scale
		audio_manager.set_audio_quality(audio_quality)
	
	# Network quality
	if network_manager:
		var network_quality = quality > 0.5
		# Adjust network update rates based on quality
		pass

func start_benchmark():
	"""Start performance benchmark"""
	if benchmark_running:
		return
	
	benchmark_running = true
	benchmark_start_time = Time.get_time_dict_from_system()["unix"]
	benchmark_results.clear()
	
	# Clear performance samples
	frame_time_samples.clear()
	gpu_time_samples.clear()
	cpu_time_samples.clear()
	memory_samples.clear()
	
	print("📊 Starting performance benchmark...")
	
	# End benchmark after duration
	get_tree().create_timer(benchmark_duration).timeout.connect(_end_benchmark)

func _end_benchmark():
	"""End performance benchmark"""
	if not benchmark_running:
		return
	
	benchmark_running = false
	
	# Calculate benchmark results
	if frame_time_samples.size() > 0:
		benchmark_results = {
			"duration": benchmark_duration,
			"avg_fps": 1000.0 / _calculate_average(frame_time_samples),
			"min_fps": 1000.0 / frame_time_samples.max(),
			"max_fps": 1000.0 / frame_time_samples.min(),
			"avg_frame_time_ms": _calculate_average(frame_time_samples),
			"avg_gpu_time_ms": _calculate_average(gpu_time_samples),
			"avg_cpu_time_ms": _calculate_average(cpu_time_samples),
			"avg_memory_mb": _calculate_average(memory_samples.map(func(x): return float(x))),
			"quality_level": current_quality_level,
			"target_achieved": benchmark_results.get("avg_fps", 0) >= target_fps
		}
		
		benchmark_completed.emit(benchmark_results)
		print("📊 Benchmark completed: %.1f FPS average" % benchmark_results.avg_fps)

func get_performance_report() -> Dictionary:
	"""Get comprehensive performance report"""
	var recent_samples = 30  # Last 3 seconds
	
	if frame_time_samples.size() < recent_samples:
		return {"error": "Insufficient performance data"}
	
	var recent_frame_times = frame_time_samples.slice(-recent_samples)
	var recent_gpu_times = gpu_time_samples.slice(-recent_samples)
	var recent_cpu_times = cpu_time_samples.slice(-recent_samples)
	
	return {
		"current_fps": 1000.0 / _calculate_average(recent_frame_times),
		"target_fps": target_fps,
		"frame_time_ms": _calculate_average(recent_frame_times),
		"gpu_time_ms": _calculate_average(recent_gpu_times),
		"cpu_time_ms": _calculate_average(recent_cpu_times),
		"memory_mb": memory_samples[-1] if memory_samples.size() > 0 else 0,
		"quality_level": current_quality_level,
		"active_optimizations": active_optimizations.keys(),
		"performance_target_met": (1000.0 / _calculate_average(recent_frame_times)) >= target_fps,
		"gpu_budget_met": _calculate_average(recent_gpu_times) <= gpu_budget_ms,
		"cpu_budget_met": _calculate_average(recent_cpu_times) <= cpu_budget_ms
	}

func reset_optimizations():
	"""Reset all performance optimizations"""
	active_optimizations.clear()
	optimization_impact.clear()
	current_quality_level = 1.0
	
	# Reset engine settings
	Engine.max_fps = 60
	Engine.physics_ticks_per_second = 60
	
	# Reset system quality
	_apply_quality_settings(1.0)
	
	print("🔄 Performance optimizations reset")

func enable_auto_quality(enabled: bool):
	"""Enable/disable automatic quality adjustment"""
	auto_quality_adjustment = enabled
	print("🎛️ Auto quality adjustment: %s" % ("enabled" if enabled else "disabled"))

func force_quality_level(quality: float):
	"""Force specific quality level (disables auto adjustment)"""
	auto_quality_adjustment = false
	set_quality_level(quality)

func get_optimization_savings() -> Dictionary:
	"""Get estimated performance savings from optimizations"""
	var total_savings = 0.0
	
	for optimization in active_optimizations.keys():
		if active_optimizations[optimization]:
			total_savings += optimization_impact.get(optimization, 0.0)
	
	return {
		"total_savings_percent": total_savings,
		"active_optimizations": active_optimizations.size(),
		"estimated_fps_gain": total_savings * 0.6  # Rough estimate
	}
