{
  "name": "Galaxy Guns 3D Development",
  "dockerComposeFile": "docker-compose.yml",
  "service": "godot-dev",
  "workspaceFolder": "/workspace",
  
  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "settings": {
        "godot_tools.editor_path": "/usr/local/bin/godot",
        "terminal.integrated.defaultProfile.linux": "bash"
      },
      "extensions": [
        "geequlim.godot-tools",
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-python.python",
        "eamodio.gitlens",
        "streetsidesoftware.code-spell-checker"
      ]
    }
  },
  
  // Use 'forwardPorts' to make a list of ports inside the container available locally
  "forwardPorts": [
    6005,  // Godot LSP
    8080,  // Development server
    3000   // Web preview
  ],
  
  // Configure port attributes
  "portsAttributes": {
    "6005": {
      "label": "Godot LSP",
      "onAutoForward": "silent"
    },
    "8080": {
      "label": "Dev Server",
      "onAutoForward": "notify"
    },
    "3000": {
      "label": "Web Preview",
      "onAutoForward": "openBrowser"
    }
  },
  
  // Use 'postCreateCommand' to run commands after the container is created
  "postCreateCommand": "bash .devcontainer/post-create.sh",
  
  // Use 'postStartCommand' to run commands after the container starts
  "postStartCommand": "echo 'Galaxy Guns 3D development environment ready!'",
  
  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root
  "remoteUser": "godot",
  
  // Configure container features
  "features": {
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/devcontainers/features/docker-in-docker:2": {},
    "ghcr.io/devcontainers/features/common-utils:2": {
      "installZsh": true,
      "configureZshAsDefaultShell": true,
      "installOhMyZsh": true
    }
  },
  
  // Mount the workspace and preserve git history
  "mounts": [
    "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached",
    "source=galaxy-guns-godot-cache,target=/home/<USER>/.cache/godot,type=volume",
    "source=galaxy-guns-vscode-extensions,target=/home/<USER>/.vscode-server/extensions,type=volume"
  ],
  
  // Environment variables
  "containerEnv": {
    "DISPLAY": ":99",
    "GODOT_VERSION": "4.3-stable",
    "PROJECT_NAME": "Galaxy Guns 3D"
  },
  
  // Run arguments for the container
  "runArgs": [
    "--name=galaxy-guns-dev",
    "--hostname=galaxy-guns-dev"
  ],
  
  // Lifecycle scripts
  "initializeCommand": "echo 'Initializing Galaxy Guns 3D development container...'",
  "onCreateCommand": "echo 'Container created successfully!'",
  
  // Development container metadata
  "capAdd": ["SYS_PTRACE"],
  "securityOpt": ["seccomp=unconfined"],
  
  // Override the default command
  "overrideCommand": false,
  
  // Whether to shut down the container when VS Code is closed
  "shutdownAction": "stopContainer"
}
