# Galaxy Guns 3D - Dungeon Generation Presets
# Defines room types, spawn rules, and generation parameters

room_types:
  start_bay:
    weight: 100
    min_size: 15
    max_size: 20
    enemy_spawns: 0
    special_type: "start"
    required: true
    description: "Starting area with basic equipment and tutorial elements"
    decorations:
      - "landing_pad"
      - "equipment_lockers"
      - "navigation_console"
    
  combat_hall:
    weight: 60
    min_size: 20
    max_size: 30
    enemy_spawns: 3
    special_type: null
    description: "Standard combat area with moderate enemy presence"
    decorations:
      - "cover_barriers"
      - "weapon_racks"
      - "ammo_crates"
    enemy_types:
      - "drone_swarm"
      - "shock_trooper"
      - "plasma_gunner"
    
  elite_gauntlet:
    weight: 20
    min_size: 25
    max_size: 35
    enemy_spawns: 1
    special_type: "elite"
    description: "Challenging room with elite enemy and enhanced rewards"
    decorations:
      - "energy_barriers"
      - "elite_spawn_pad"
      - "reward_cache"
    enemy_types:
      - "elite_commander"
      - "cyber_wraith"
      - "quantum_soldier"
    
  treasure_vault:
    weight: 10
    min_size: 15
    max_size: 20
    enemy_spawns: 1
    special_type: "treasure"
    description: "Secure vault with valuable loot and light defenses"
    decorations:
      - "treasure_chest"
      - "security_turret"
      - "laser_grid"
    rewards:
      - type: "currency"
        amount: 500
      - type: "equipment"
        rarity: "rare"
    
  puzzle_hub:
    weight: 15
    min_size: 20
    max_size: 25
    enemy_spawns: 2
    special_type: "puzzle"
    description: "Interactive puzzle room with keycard rewards"
    decorations:
      - "control_panels"
      - "force_fields"
      - "data_terminals"
    puzzles:
      - "pressure_plate_sequence"
      - "power_coupling_rotation"
      - "data_terminal_hack"
    
  boss_chamber:
    weight: 100
    min_size: 30
    max_size: 40
    enemy_spawns: 1
    special_type: "boss"
    required: true
    description: "Final room containing sector boss"
    decorations:
      - "boss_arena"
      - "energy_conduits"
      - "warning_lights"

# Biome themes that affect visual appearance and hazards
biomes:
  starship:
    name: "Starship Corridors"
    color_tint: "#00FF99"
    background_pattern: "grid"
    ambient_sound: "ship_hum"
    hazards:
      - type: "plasma_leak"
        spawn_chance: 0.15
        damage: 5
        interval: 1000
      - type: "force_field"
        spawn_chance: 0.10
        effect: "movement_block"
    decorations:
      - "wall_panels"
      - "ceiling_lights"
      - "ventilation_grates"
      
  asteroid:
    name: "Asteroid Mining"
    color_tint: "#FF6B35"
    background_pattern: "rock"
    ambient_sound: "mining_drill"
    hazards:
      - type: "gravity_well"
        spawn_chance: 0.12
        effect: "movement_slow"
        radius: 100
      - type: "debris_storm"
        spawn_chance: 0.08
        damage: 8
        duration: 5000
    decorations:
      - "rock_formations"
      - "mining_equipment"
      - "ore_deposits"
      
  nebula:
    name: "Nebula Station"
    color_tint: "#9B59B6"
    background_pattern: "cloud"
    ambient_sound: "energy_hum"
    hazards:
      - type: "energy_storm"
        spawn_chance: 0.10
        effect: "weapon_malfunction"
        duration: 3000
      - type: "void_rift"
        spawn_chance: 0.05
        effect: "teleport_random"
        range: 200
    decorations:
      - "energy_crystals"
      - "floating_platforms"
      - "nebula_wisps"

# Connection rules for room layout
connectivity:
  guaranteed_path: true
  branch_probability: 0.35
  max_branches_per_room: 2
  locked_door_chance: 0.30
  
  keycard_colors:
    - "red"
    - "blue" 
    - "yellow"
    
  door_types:
    standard:
      weight: 70
      requirements: []
    locked:
      weight: 25
      requirements: ["keycard"]
    puzzle:
      weight: 5
      requirements: ["puzzle_solve"]

# Difficulty scaling parameters
difficulty_scaling:
  base_enemy_count: 3
  enemy_count_per_difficulty: 2
  max_enemies_per_room: 8
  
  health_multiplier_per_difficulty: 0.15
  damage_multiplier_per_difficulty: 0.10
  speed_multiplier_per_difficulty: 0.05
  
  elite_spawn_chance_base: 0.10
  elite_spawn_chance_per_difficulty: 0.05
  
  boss_health_multiplier: 2.0
  boss_damage_multiplier: 1.5

# Generation parameters
generation:
  min_rooms: 8
  max_rooms: 15
  rooms_per_difficulty: 2
  
  seed_randomization: true
  layout_algorithm: "branching_path"
  
  room_spacing:
    min_distance: 2
    max_distance: 5
    overlap_prevention: true
    
  special_room_limits:
    treasure_vault: 2
    puzzle_hub: 3
    elite_gauntlet: 2

# Event and secret spawning
events:
  secret_room_chance: 0.01
  merchant_spawn_chance: 0.05
  lore_terminal_chance: 0.08
  
  risk_shrine_types:
    - name: "Health Sacrifice"
      cost: { type: "health", amount: 25 }
      reward: { type: "damage_boost", multiplier: 1.5, duration: 60 }
    - name: "Ammo Gamble"
      cost: { type: "ammo", amount: 10 }
      reward: { type: "speed_boost", multiplier: 1.3, duration: 45 }
    - name: "Currency Investment"
      cost: { type: "currency", amount: 100 }
      reward: { type: "shield", absorption: 50, duration: 30 }

# Loot distribution
loot_tables:
  common_enemy:
    currency: { min: 5, max: 15, chance: 80 }
    equipment: { rarity: "common", chance: 10 }
    consumable: { type: "health_pack", chance: 15 }
    
  elite_enemy:
    currency: { min: 20, max: 50, chance: 90 }
    equipment: { rarity: "rare", chance: 35 }
    keycard: { chance: 25 }
    
  boss_enemy:
    currency: { min: 100, max: 300, chance: 100 }
    equipment: { rarity: "legendary", chance: 80 }
    nav_key_fragments: { amount: 3, chance: 100 }
    
  treasure_chest:
    currency: { min: 200, max: 800, chance: 100 }
    equipment: { rarity: "epic", chance: 60 }
    blueprint_shards: { min: 1, max: 3, chance: 40 }

# Audio cues for different room types
audio_cues:
  room_enter:
    start_bay: "airlock_cycle"
    combat_hall: "alarm_brief"
    elite_gauntlet: "warning_klaxon"
    treasure_vault: "vault_unlock"
    puzzle_hub: "computer_beep"
    boss_chamber: "boss_music_start"
    
  ambient_loops:
    starship: "ship_ambient"
    asteroid: "mining_ambient"
    nebula: "energy_ambient"
    
  hazard_sounds:
    plasma_leak: "energy_discharge"
    gravity_well: "gravitational_hum"
    energy_storm: "electrical_storm"
